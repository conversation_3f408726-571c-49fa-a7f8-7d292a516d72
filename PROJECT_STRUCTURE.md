# 项目清理后的结构

## 已移除的内容
- `test/` - 测试目录
- `tests/` - 测试目录
- `demo/` - 演示文件
- `grunt-config/` - Grunt 构建配置
- `node_modules/` - 依赖包（可重新安装）
- `Gruntfile.js` - Grunt 构建文件
- `JSLib.csproj`, `JSLib.sln` - .NET 项目文件
- `Web.config` - IIS 配置文件
- `packages.config` - NuGet 配置
- `pnpm-lock.yaml` - pnpm 锁文件
- `DEPENDENCIES` - 依赖说明文件

## 保留的核心文件结构

```
├── LICENSE                    # Apache 许可证
├── NOTICE                     # 版权声明
├── README.md                  # 项目说明
├── package.json               # 已更新为 ES 模块配置
├── index.js                   # Node.js 入口文件
├── index-browser.js           # 浏览器入口文件
└── lib/                       # 核心库代码
    ├── utils.js               # 工具函数（已部分转换为 ES 模块）
    ├── xml.js                 # XML 处理
    ├── deferred.js            # 异步处理
    ├── cache.js               # 缓存主文件
    ├── cache/
    │   └── source.js          # 缓存源
    ├── odata.js               # OData 主文件
    ├── odata/
    │   ├── batch.js           # 批处理
    │   ├── handler.js         # 处理器
    │   ├── json.js            # JSON 处理
    │   ├── metadata.js        # 元数据
    │   ├── net.js             # 网络（Node.js）
    │   ├── net-browser.js     # 网络（浏览器）
    │   └── odatautils.js      # OData 工具
    ├── store.js               # 存储主文件
    └── store/
        ├── dom.js             # DOM 存储
        ├── indexeddb.js       # IndexedDB 存储
        └── memory.js          # 内存存储
```

## package.json 更新内容
- 添加了 `"type": "module"` 启用 ES 模块
- 更新了 Node.js 版本要求到 >= 14.0.0
- 移除了所有构建和测试相关的 devDependencies
- 保留了 xmldom 作为唯一的运行时依赖
- 简化了脚本配置

## 下一步 ES 模块转换计划
1. 完成 `lib/utils.js` 的 ES 模块转换
2. 转换其他核心模块文件
3. 更新模块间的依赖关系
4. 创建新的 ES 模块入口文件
5. 测试模块加载和功能
