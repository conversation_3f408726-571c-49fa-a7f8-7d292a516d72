/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing,
 * software distributed under the License is distributed on an
 * "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 * KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations
 * under the License.
 */

var init=function(a,b,c){a.version={major:4,minor:0,build:0},a.deferred=c("./lib/deferred.js"),a.utils=c("./lib/utils.js"),a.xml=c("./lib/xml.js"),a.oData=c("./lib/odata.js"),a.store=c("./lib/store.js"),a.cache=c("./lib/cache.js")},datas={cache:function(a,b,c){"use strict";function d(a,b){var c=e(a,b),d=0,f=0;c&&(d=c.i-b.i,f=d+(a.c-A(a.d))),a.d=C(a.d,B(b.d,d,f))}function e(a,b){var c,d=a.i+a.c,e=b.i+b.c,f=a.i>b.i?a.i:b.i,g=e>d?d:e;return g>=f&&(c={i:f,c:g-f}),c}function f(a,b){if(void 0===a||"number"!=typeof a)throw{message:"'"+b+"' must be a number."};if(isNaN(a)||0>a||!isFinite(a))throw{message:"'"+b+"' must be greater than or equal to zero."}}function g(a,b){if(void 0!==a){if("number"!=typeof a)throw{message:"'"+b+"' must be a number."};if(isNaN(a)||0>=a||!isFinite(a))throw{message:"'"+b+"' must be greater than zero."}}}function h(a,b){if(void 0!==a&&("number"!=typeof a||isNaN(a)||!isFinite(a)))throw{message:"'"+b+"' must be a number."}}function i(a,b){var c,d;for(c=0,d=a.length;d>c;c++)if(a[c]===b)return a.splice(c,1),!0;return!1}function j(a){var b=0,c=typeof a;if("object"===c&&a)for(var d in a)b+=2*d.length+j(a[d]);else b="string"===c?2*a.length:8;return b}function k(a,b,c){return a=Math.floor(a/c)*c,b=Math.ceil((b+1)/c)*c,{i:a,c:b-a}}function l(a,b,c,d,e,f,g){var h,i,j=this;j.p=b,j.i=d,j.c=e,j.d=f,j.s=M,j.canceled=!1,j.pending=g,j.oncomplete=null,j.cancel=function(){if(c){var a=j.s;a!==L&&a!==K&&a!==J&&(j.canceled=!0,j.transition(J,h))}},j.complete=function(){v(j.s!==K,"DataCacheOperation.complete() - operation is in the end state",j),j.transition(K,h)},j.error=function(a){j.canceled||(v(j.s!==K,"DataCacheOperation.error() - operation is in the end state",j),v(j.s!==L,"DataCacheOperation.error() - operation is in the error state",j),j.transition(L,a))},j.run=function(a){i=a,j.transition(j.s,h)},j.wait=function(a){v(j.s!==K,"DataCacheOperation.wait() - operation is in the end state",j),j.transition(N,a)};var k=function(b,c,d){switch(b){case M:c!==F&&a(j,b,c,d);break;case N:a(j,b,c,d);break;case J:a(j,b,c,d),j.fireCanceled(),j.transition(K);break;case L:a(j,b,c,d),j.canceled=!0,j.fireRejected(d),j.transition(K);break;case K:j.oncomplete&&j.oncomplete(j),j.canceled||j.fireResolved(),a(j,b,c,d);break;default:var e=a(j,b,c,d);v(e,"Bad operation state: "+b+" cacheState: "+c,this)}};return j.transition=function(a,b){j.s=a,h=b,k(a,i,b)},j}function m(a){var b,c=F,e={counts:0,netReads:0,prefetches:0,cacheReads:0},g=[],h=[],m=[],n=0,p=!1,B=x(a.cacheSize,1048576),C=0,L=0,N=0,T=0===B,U=x(a.pageSize,50),V=x(a.prefetchSize,U),W="1.0",X=0,Y=a.source;"string"==typeof Y&&(Y=new r.ODataCacheSource(a)),Y.options=a;var Z=q.createStore(a.name,a.mechanism),$=this;$.onidle=a.idle,$.stats=e,$.count=function(){if(b)throw b;var a=y(),c=!1;if(p)return t(function(){a.resolve(C)}),a.promise();var d=Y.count(function(b){d=null,e.counts++,a.resolve(b)},function(b){d=null,a.reject(u(b,{canceled:c}))});return u(a.promise(),{cancel:function(){d&&(c=!0,d.abort(),d=null)}})},$.clear=function(){if(b)throw b;if(0===g.length){var a=y(),c=new l(ob,a,!1);return hb(c,g),a.promise()}return g[0].p},$.filterForward=function(a,b,c){return eb(a,b,c,!1)},$.filterBack=function(a,b,c){return eb(a,b,c,!0)},$.readRange=function(a,c){if(f(a,"index"),f(c,"count"),b)throw b;var d=y(),e=new l(qb,d,!0,a,c,{},0);return hb(e,h),u(d.promise(),{cancel:function(){e.cancel()}})},$.ToObservable=$.toObservable=function(){if(!o.inBrowser())throw{message:"Only in broser supported"};if(!window.Rx||!window.Rx.Observable)throw{message:"Rx library not available - include rx.js"};if(b)throw b;return new window.Rx.Observable(function(a){var b=!1,c=0,d=function(c){b||a.onError(c)},e=function(f){if(!b){var g,h;for(g=0,h=f.value.length;h>g;g++)a.onNext(f.value[g]);f.value.length<U?a.onCompleted():(c+=U,$.readRange(c,U).then(e,d))}};return $.readRange(c,U).then(e,d),{Dispose:function(){a.dispose(),b=!0}}})};var _=function(a){return function(c){b={message:a,error:c},v(0===m.length,"prefetchOperations.length === 0");var d,e;for(d=0,e=h.length;e>d;d++)h[d].fireRejected(b);for(d=0,e=g.length;e>d;d++)g[d].fireRejected(b);h=g=null}},ab=function(a){if(a!==c){c=a;var b,d,e=g.concat(h,m);for(b=0,d=e.length;d>b;b++)e[b].run(c)}},bb=function(){v(c===D||c===F,"DataCache.clearStore() - cache is not on the destroy or initialize state, current sate = "+c);var a=new z;return Z.clear(function(){n=0,p=!1,C=0,L=0,N=0,T=0===B,e={counts:0,netReads:0,prefetches:0,cacheReads:0},$.stats=e,Z.close(),a.resolve()},function(b){a.reject(b)}),a},cb=function(a){var b=i(g,a);b||(b=i(h,a),b||i(m,a)),X--,ab(E)},db=function(a){v(c!==D,"DataCache.fetchPage() - cache is on the destroy state"),v(c!==E,"DataCache.fetchPage() - cache is on the idle state");var b=new z,d=!1,e=Y.read(a,U,function(c){var d=A(c),e={i:a,c:d,d:c};b.resolve(e)},function(a){b.reject(a)});return u(b,{cancel:function(){e&&(e.abort(),d=!0,e=null)}})},eb=function(a,c,d,e){if(a=w(a),c=w(c),isNaN(a))throw{message:"'index' must be a valid number.",index:a};if(isNaN(c))throw{message:"'count' must be a valid number.",count:c};if(b)throw b;a=Math.max(a,0);var f=y(),g={};g.value=[];var h=!1,i=null,j=function(a,b){h||(c>0&&g.value.length>=c?f.resolve(g):i=$.readRange(a,b).then(function(h){h["@odata.context"]&&!g["@odata.context"]&&(g["@odata.context"]=h["@odata.context"]);for(var i=0,k=h.value.length;k>i&&(0>c||g.value.length<c);i++){var l=e?k-i-1:i,m=h.value[l];if(d(m)){var n={index:a+l,item:m};e?g.value.unshift(n):g.value.push(n)}}if(!e&&h.value.length<b||e&&0>=a)f.resolve(g);else{var o=e?Math.max(a-U,0):a+b;j(o,U)}},function(a){f.reject(a)}))},l=k(a,a,U),m=e?l.i:a,n=e?a-l.i+1:l.i+l.c-a;return j(m,n),u(f.promise(),{cancel:function(){i&&i.cancel(),h=!0}})},fb=function(){$.onidle&&0===X&&$.onidle()},gb=function(a){if(!p&&0!==V&&!T&&(v(c===G,"DataCache.prefetch() - cache is not on the read state, current state: "+c),0===m.length||m[0]&&-1!==m[0].c)){var b=new l(pb,null,!0,a,V,null,V);hb(b,m)}},hb=function(a,b){a.oncomplete=cb,b.push(a),X++,a.run(c)},ib=function(a){v(c!==D,"DataCache.readPage() - cache is on the destroy state");var b=!1,d=u(new z,{cancel:function(){b=!0}}),e=lb(d,"Read page from store failure");return Z.contains(a,function(c){return b?void 0:c?void Z.read(a,function(a,c){b||d.resolve(void 0!==c,c)},e):void d.resolve(!1)},e),d},jb=function(a,b){v(c!==D,"DataCache.savePage() - cache is on the destroy state"),v(c!==E,"DataCache.savePage() - cache is on the idle state");var d=!1,e=u(new z,{cancel:function(){d=!0}}),f=lb(e,"Save page to store failure"),g=function(){e.resolve(!0)};if(b.c>0){var h=j(b);T=B>=0&&n+h>B,T?g():Z.addOrUpdate(a,b,function(){mb(b,h),kb(g,f)},f)}else mb(b,0),kb(g,f);return e},kb=function(a,b){var c={actualCacheSize:n,allDataLocal:p,cacheSize:B,collectionCount:C,highestSavedPage:L,highestSavedPageSize:N,pageSize:U,sourceId:Y.identifier,version:W};Z.addOrUpdate("__settings",c,a,b)},lb=function(a){return function(){a.resolve(!1)}},mb=function(a,b){var c=a.c,d=a.i;0===c?L===d-U&&(C=L+N):(L=Math.max(L,d),L===d&&(N=c),n+=b,U>c&&!C&&(C=d+c)),p||C!==L+N||(p=!0)},nb=function(a,b,c,d){var e=a.canceled&&b!==K;return e&&b===J&&d&&d.cancel&&d.cancel(),e},ob=function(a,b,c){var d=a.transition;if(c!==D)return ab(D),!0;switch(b){case M:d(O);break;case K:fb();break;case O:bb().then(function(){a.complete()}),a.wait();break;default:return!1}return!0},pb=function(a,b,c,d){if(!nb(a,b,c,d)){var e=a.transition;if(c!==H)return c===D?b!==J&&a.cancel():c===E&&ab(H),!0;switch(b){case M:m[0]===a&&e(Q,a.i);break;case P:var f=a.pending;f>0&&(f-=Math.min(f,d.c)),p||0===f||d.c<U||T?a.complete():(a.pending=f,e(Q,d.i+U));break;default:return rb(a,b,c,d,!0)}}return!0},qb=function(a,b,f,g){if(!nb(a,b,f,g)){var h=a.transition;if(f!==G&&b!==M)return f===D?b!==M&&a.cancel():f!==I&&(v(c==E||c===H,"DataCache.readStateMachine() - cache is not on the read or idle state."),ab(G)),!0;switch(b){case M:if(f===E||f===H)if(ab(G),a.c>=0){var i=k(a.i,a.c,U);h(Q,i.i)}else h(P,a);break;case P:d(a,g);var j=A(a.d);a.c===j||g.c<U?(e.cacheReads++,gb(g.i+g.c),a.complete()):h(Q,g.i+U);break;default:return rb(a,b,f,g,!1)}}return!0},rb=function(a,b,c,d,f){var g,h=a.error,i=a.transition,j=a.wait;switch(b){case K:fb();break;case Q:g=ib(d).then(function(b,c){a.canceled||(b?i(P,c):i(S,d))});break;case S:g=db(d).then(function(b){a.canceled||(f?e.prefetches++:e.netReads++,i(R,b))},h);break;case R:c!==I&&(ab(I),g=jb(d.i,d).then(function(b){a.canceled||(!b&&f&&(a.pending=0),i(P,d)),ab(E)}));break;default:return!1}return g&&(a.canceled?g.cancel():a.s===b&&j(g)),!0};return Z.read("__settings",function(a,b){if(s(b)){var c=b.version;if(!c||0!==c.indexOf("1."))return void _("Unsupported cache store version "+c)();U!==b.pageSize||Y.identifier!==b.sourceId?bb().then(function(){ab(E)},_("Unable to clear store during initialization")):(n=b.actualCacheSize,p=b.allDataLocal,B=b.cacheSize,C=b.collectionCount,L=b.highestSavedPage,N=b.highestSavedPageSize,W=c,ab(E))}else kb(function(){ab(E)},_("Unable to write settings during initialization."))},_("Unable to read settings from store.")),$}function n(a){if(g(a.pageSize,"pageSize"),h(a.cacheSize,"cacheSize"),h(a.prefetchSize,"prefetchSize"),!s(a.name))throw{message:"Undefined or null name",options:a};if(!s(a.source))throw{message:"Undefined source",options:a};return new m(a)}var o=c("./utils.js"),p=c("./deferred.js"),q=c("./store.js"),r=c("./cache/source.js"),s=o.assigned,t=o.delay,u=o.extend,v=o.djsassert,w=(o.isArray,o.normalizeURI,o.parseInt10),x=o.undefinedDefault,y=p.createDeferred,z=p.DjsDeferred,A=o.getJsonValueArraryLength,B=o.sliceJsonValueArray,C=o.concatJsonValueArray,D="destroy",E="idle",F="init",G="read",H="prefetch",I="write",J="cancel",K="end",L="error",M="start",N="wait",O="clear",P="done",Q="local",R="save",S="source";l.prototype.fireResolved=function(){var a=this.p;a&&(this.p=null,a.resolve(this.d))},l.prototype.fireRejected=function(a){var b=this.p;b&&(this.p=null,b.reject(a))},l.prototype.fireCanceled=function(){this.fireRejected({canceled:!0,message:"Operation canceled"})},a.estimateSize=j,a.createDataCache=n},source:function(a,b,c){"use strict";function d(a,b){var c=a.indexOf("?")>=0?"&":"?";return a+c+b}function e(a,b){var c=a.indexOf("?"),d="";return c>=0&&(d=a.substr(c),a=a.substr(0,c)),"/"!==a[a.length-1]&&(a+="/"),a+b+d}function f(a,b){return{method:"GET",requestUri:a,user:b.user,password:b.password,enableJsonpCallback:b.enableJsonpCallback,callbackParameterName:b.callbackParameterName,formatQueryString:b.formatQueryString}}function g(a,b,c,d){return h(a,b,{},c,d)}function h(a,b,c,d,e){var g=f(a,b),i=k.request(g,function(f){var g=f["@odata.nextLink"];if(g){var j=a.indexOf(".svc/",0);-1!=j&&(g=a.substring(0,j+5)+g)}if(c.value&&f.value)c.value=c.value.concat(f.value);else for(var k in f)"@odata.nextLink"!=k&&(c[k]=f[k]);g?i=h(g,b,c,d,e):d(c)},e,void 0,b.httpClient,b.metadata);return{abort:function(){i.abort()}}}function i(a){var b=this,c=a.source;return b.identifier=m(encodeURI(decodeURI(c))),b.options=a,b.count=function(a,d){var g=b.options;return k.request(f(e(c,"$count"),g),function(b){var c=l(b.toString());isNaN(c)?d({message:"Count is NaN",count:c}):a(c)},d,void 0,g.httpClient,g.metadata)},b.read=function(a,e,f,h){var i="$skip="+a+"&$top="+e;return g(d(c,i),b.options,f,h)},b}var j=c("./../utils.js"),k=c("./../odata.js"),l=j.parseInt10,m=j.normalizeURICase;a.ODataCacheSource=i},deferred:function(a){"use strict";function b(a,b,c){return function(){return a[b].apply(a,arguments),c}}function c(){this._arguments=void 0,this._done=void 0,this._fail=void 0,this._resolved=!1,this._rejected=!1}function d(){return window.jQuery&&window.jQuery.Deferred?new window.jQuery.Deferred:new c}c.prototype={then:function(a,b){return a&&(this._done?this._done.push(a):this._done=[a]),b&&(this._fail?this._fail.push(b):this._fail=[b]),this._resolved?this.resolve.apply(this,this._arguments):this._rejected&&this.reject.apply(this,this._arguments),this},resolve:function(){if(this._done){var a,b;for(a=0,b=this._done.length;b>a;a++)this._done[a].apply(null,arguments);this._done=void 0,this._resolved=!1,this._arguments=void 0}else this._resolved=!0,this._arguments=arguments},reject:function(){if(this._fail){var a,b;for(a=0,b=this._fail.length;b>a;a++)this._fail[a].apply(null,arguments);this._fail=void 0,this._rejected=!1,this._arguments=void 0}else this._rejected=!0,this._arguments=arguments},promise:function(){var a={};return a.then=b(this,"then",a),a}},a.createDeferred=d,a.DjsDeferred=c},odata:function(a,b,c){"use strict";function d(a,b,c){var d,e;for(d=0,e=o.length;e>d&&!o[d][a](b,c);d++);if(d===e)throw{message:"no handler for data"}}var e=a.utils=c("./odata/odatautils.js"),f=a.handler=c("./odata/handler.js"),g=a.metadata=c("./odata/metadata.js"),h=a.net=c("./odata/net.js"),i=a.json=c("./odata/json.js");a.batch=c("./odata/batch.js");var j=c("./utils.js"),k=j.assigned,l=(j.defined,j.throwErrorCallback),m=(e.invokeRequest,f.MAX_DATA_SERVICE_VERSION),n=(e.prepareRequest,g.metadataParser),o=[i.jsonHandler,f.textHandler];a.defaultSuccess=function(a){window.alert(window.JSON.stringify(a))},a.defaultError=l,a.defaultHandler={read:function(a,b){a&&k(a.body)&&a.headers["Content-Type"]&&d("read",a,b)},write:function(a,b){d("write",a,b)},maxDataServiceVersion:m,accept:"application/json;q=0.9, */*;q=0.1"},a.defaultMetadata=[],a.read=function(b,c,d,e,f,g){var h;return h=b instanceof String||"string"==typeof b?{requestUri:b}:b,a.request(h,c,d,e,f,g)},a.request=function(b,c,d,f,g,k){c=c||a.defaultSuccess,d=d||a.defaultError,f=f||a.defaultHandler,g=g||h.defaultHttpClient,k=k||a.defaultMetadata,b.recognizeDates=j.defined(b.recognizeDates,i.jsonHandler.recognizeDates),b.callbackParameterName=j.defined(b.callbackParameterName,h.defaultHttpClient.callbackParameterName),b.formatQueryString=j.defined(b.formatQueryString,h.defaultHttpClient.formatQueryString),b.enableJsonpCallback=j.defined(b.enableJsonpCallback,h.defaultHttpClient.enableJsonpCallback);var l={metadata:k,recognizeDates:b.recognizeDates,callbackParameterName:b.callbackParameterName,formatQueryString:b.formatQueryString,enableJsonpCallback:b.enableJsonpCallback};try{return e.prepareRequest(b,f,l),e.invokeRequest(b,c,d,f,g,l)}catch(m){if(m.bIsSuccessHandlerError)throw m;d(m)}},a.parseMetadata=function(a){return n(null,a)},a.batch.batchHandler.partHandler=a.defaultHandler,a.metadataHandler=g.metadataHandler,a.jsonHandler=i.jsonHandler},batch:function(a,b,c){"use strict";function d(){return Math.floor(65536*(1+Math.random())).toString(16).substr(1)}function e(a){return a+d()+"-"+d()+"-"+d()}function f(a){return a.handler.partHandler}function g(a){var b=a.boundaries;return b[b.length-1]}function h(a,b,c){var d=c.contentType.properties.boundary;return{__batchResponses:j(b,{boundaries:[d],handlerContext:c})}}function i(a,b,c){var d=c.contentType=c.contentType||x(D);return d.mediaType===D?o(b,c):void 0}function j(a,b){var c="--"+g(b);n(a,b,c),m(a,b);for(var d=[],e=null;"--"!==e&&b.position<a.length;){var h,i=k(a,b),o=x(i["Content-Type"]);if(o&&o.mediaType===D){b.boundaries.push(o.properties.boundary);try{h=j(a,b)}catch(p){p.response=l(a,b,c),h=[p]}d.push({__changeResponses:h}),b.boundaries.pop(),n(a,b,"--"+g(b))}else{if(!o||"application/http"!==o.mediaType)throw{message:"invalid MIME part type "};m(a,b);var q=l(a,b,c);try{q.statusCode>=200&&q.statusCode<=299?f(b.handlerContext).read(q,b.handlerContext):q={message:"HTTP request failed",response:q}}catch(p){q=p}d.push(q)}e=a.substr(b.position,2),m(a,b)}return d}function k(a,b){var c,d,e,f={};do e=b.position,d=m(a,b),c=F.exec(d),null!==c?f[c[1]]=c[2]:b.position=e;while(d&&c);return B(f),f}function l(a,b,c){var d,e,f,g=b.position,h=E.exec(m(a,b));return h?(d=h[1],e=h[2],f=k(a,b),m(a,b)):b.position=g,{statusCode:d,statusText:e,headers:f,body:n(a,b,"\r\n"+c)}}function m(a,b){return n(a,b,"\r\n")}function n(a,b,c){var d=b.position||0,e=a.length;if(c){if(e=a.indexOf(c,d),-1===e)return null;b.position=e+c.length}else b.position=e;return a.substring(d,e)}function o(a,b){if(!z(a))throw{message:"Data is not a batch object."};var c,d,f=e("batch_"),g=a.__batchRequests,h="";for(c=0,d=g.length;d>c;c++)h+=p(f,!1)+q(g[c],b);h+=p(f,!0);var i=b.contentType.properties;return i.boundary=f,h}function p(a,b){var c="\r\n--"+a;return b&&(c+="--"),c+"\r\n"}function q(a,b,c){var d,g=a.__changeRequests;if(w(g)){if(c)throw{message:"Not Supported: change set nested in other change set"};var h=e("changeset_");d="Content-Type: "+D+"; boundary="+h+"\r\n";var i,j;for(i=0,j=g.length;j>i;i++)d+=p(h,!1)+q(g[i],b,!0);d+=p(h,!0)}else{d="Content-Type: application/http\r\nContent-Transfer-Encoding: binary\r\n\r\n";var k=v({},b);k.handler=y,k.request=a,k.contentType=null,C(a,f(b),k),d+=r(a)}return d}function r(a){var b=(a.method?a.method:"GET")+" "+a.requestUri+" HTTP/1.1\r\n";for(var c in a.headers)a.headers[c]&&(b=b+c+": "+a.headers[c]+"\r\n");return b+="\r\n",a.body&&(b+=a.body),b}var s=c("./../utils.js"),t=c("./odatautils.js"),u=c("./handler.js"),v=s.extend,w=s.isArray,x=(s.trimString,u.contentType),y=u.handler,z=t.isBatch,A=u.MAX_DATA_SERVICE_VERSION,B=t.normalizeHeaders,C=t.prepareRequest,D="multipart/mixed",E=/^HTTP\/1\.\d (\d{3}) (.*)$/i,F=/^([^()<>@,;:\\"\/[\]?={} \t]+)\s?:\s?(.*)/;a.batchHandler=y(h,i,D,A),a.batchSerializer=i,a.writeRequest=r},handler:function(a,b,c){"use strict";function d(a){if(!a)return null;var b,c,d=a.split(";"),e={};for(b=1,c=d.length;c>b;b++){var f=d[b].split("=");e[v(f[0])]=f[1]}return{mediaType:v(d[0]),properties:e}}function e(a){if(!a)return void 0;var b,c=a.mediaType;for(b in a.properties)c+=";"+b+"="+a.properties[b];return c}function f(a,b,c,d){var e={};return u(e,c),u(e,{contentType:a,dataServiceVersion:b,handler:d}),e}function g(a,b,c){if(a){var d=a.headers;d[b]||(d[b]=c)}}function h(a,b){if(a){var c=a.headers,d=c["OData-Version"];c["OData-Version"]=d?w(d,b):b}}function i(a,b){var c=a.headers;return c&&c[b]||void 0}function j(a){return d(i(a,"Content-Type"))}function k(a){var b=i(a,"OData-Version");if(b){var c=y.exec(b);if(c&&c.length)return c[1]}}function l(a,b){return a.accept.indexOf(b.mediaType)>=0}function m(a,b,c,d){if(!c||!c.headers)return!1;var e=j(c),g=k(c)||"",h=c.body;if(!t(h))return!1;if(l(a,e)){var i=f(e,g,d,a);return i.response=c,c.data=b(a,h,i),void 0!==c.data}return!1}function n(a,b,c,d){if(!c||!c.headers)return!1;var i=j(c),m=k(c);if(!i||l(a,i)){var n=f(i,m,d,a);if(n.request=c,c.body=b(a,c.data,n),void 0!==c.body)return h(c,n.dataServiceVersion||"4.0"),g(c,"Content-Type",e(n.contentType)),g(c,"OData-MaxVersion",a.maxDataServiceVersion),!0}return!1}function o(a,b,c,d){return{accept:c,maxDataServiceVersion:d,read:function(b,c){return m(this,a,b,c)},write:function(a,c){return n(this,b,a,c)}}}function p(a,b){return b}function q(a,b){return t(b)?b.toString():void 0}var r=c("./../utils.js"),s=c("./odatautils.js"),t=r.assigned,u=r.extend,v=r.trimString,w=s.maxVersion,x="4.0",y=/^\s?(\d+\.\d+);?.*$/;a.textHandler=o(p,q,"text/plain",x),a.contentType=d,a.contentTypeToString=e,a.handler=o,a.createReadWriteContext=f,a.fixRequestHeader=g,a.getRequestOrResponseHeader=i,a.getContentType=j,a.getDataServiceVersion=k,a.MAX_DATA_SERVICE_VERSION=x},json:function(a,b,c){function d(a,b,c){var d,e=J(c.recognizeDates,a.recognizeDates),f=c.metadata,g="string"==typeof b?JSON.parse(b):b;I(c.contentType)&&I(c.contentType.properties)&&(d=c.contentType.properties["odata.metadata"]);var h=N(d,1);return 0===h?g:1===h?k(g,f,e):2===h?i(g,f,e):g}function e(a,b,c){var d=c.dataServiceVersion||"4.0",e=c.contentType=c.contentType||pb;if(e&&e.mediaType===pb.mediaType){c.dataServiceVersion=$(d,"4.0");var g=f(b);if(g)return JSON.stringify(g,sb)}return void 0}function f(a){if(!a)return a;if(R(a))return a;if(K(a)){var b,c,d=[];for(b=0,c=a.length;c>b;b++)d[b]=f(a[b]);return d}var e={};for(var h in a)g(h)&&(e[h]=f(a[h]));return e}function g(a){if(!a)return!1;if(-1==a.indexOf("@odata."))return!0;var b,c;for(b=0,c=qb.length;c>b;b++){var d=qb[b];if(-1!=a.indexOf(d))return!0}return!1}function h(a,b){return{kind:a,type:b||null}}function i(a,b,c){var d;if(C.isObject(a))for(var e in a)if(a.hasOwnProperty(e)&&-1===e.indexOf("@"))if(C.isArray(a[e]))for(var f=0;f<a[e].length;++f)i(a[e][f],b,c);else if(C.isObject(a[e]))null!==a[e]&&(d=a[e+"@odata.type"],d?(d=d.substring(1),ab(d)||bb(d)||i(a[e],b,c)):i(a[e],b,c));else if(d=a[e+"@odata.type"],I(d))c&&t(a,e,d.substring(1));else{var g=typeof a[e];"string"===g?p(a,e,"String"):"boolean"===g?p(a,e,"Boolean"):"number"===g&&(a[e]%1===0?p(a,e,"Int32"):p(a,e,"Decimal"))}return a}function j(a,b,c,d,e){for(var f in a)if(-1===f.indexOf("@")){for(var g=b,h=a[f],i=Y(g.property,f);null===i&&void 0!==g.baseType;)g=U(g.baseType,d),i=Y(g.property,f);if(K(h)){S(i.type)?q(a,f,i.type.substring(11,i.type.length-1)):r(a,f,i.type);for(var j=0;j<h.length;j++)n(h[j],i,c,d,e)}else L(h)&&null!==h?n(h,i,c,d,e):(r(a,f,i.type),e&&s(a,f,i.type))}}function k(a,b,c){if(!I(b)||K(b))return a;var d=a[H],e=z(a,b);switch(e.detectedPayloadKind){case jb:return null!==e.type?m(a,e,d,b,c):r(a,"value",e.typeName);case cb:return l(a,b,e,d,c);case db:return m(a,e,d,b,c);case fb:return o(a,b,e,d,c);case eb:return null!==e.type?m(a,e,d,b,c):r(a,"value",e.typeName);case gb:return a;case PAYLOADTYPE_LINKS:return a}return a}function l(a,b,c,d,e){var f,g,h,i=[],j=a.value;for(f=0,g=j.length;g>f;f++){var k=j[f];if(J(k["@odata.type"])){var l=k["@odata.type"].substring(1),n=U(l,b),o={contentTypeOdata:c.contentTypeOdata,detectedPayloadKind:c.detectedPayloadKind,name:c.name,type:n,typeName:l};h=m(k,o,d,b,e)}else h=m(k,c,d,b,e);i.push(h)}return a.value=i,a}function m(a,b,c,d,e){p(a,"",b.typeName);for(var f=b.type;J(f)&&void 0===f.key&&void 0!==f.baseType;)f=U(f.baseType,d);if(void 0!==f.key){var g=b.name+A(a,f);a["@odata.id"]=c.substring(0,c.lastIndexOf("$metadata"))+g,a["@odata.editLink"]=g}return j(a,b.type,c,d,e),a}function n(a,b,c,d,e){var f=b.type;S(b.type)&&(f=b.type.substring(11,b.type.length-1)),p(a,"",b.type);var g=T(f,d);null!==g&&j(a,g,c,d,e)}function o(a,b,c,d,e){if(q(a,"",c.typeName),null!==c.type){var f,g,h,i=[],j=a.value;for(f=0,g=j.length;g>f;f++){var k=j[f];if(J(k["@odata.type"])){var l=k["@odata.type"].substring(1),n=U(l,b),o={contentTypeOdata:c.contentTypeOdata,detectedPayloadKind:c.detectedPayloadKind,name:c.name,type:n,typeName:l};h=m(k,o,d,b,e)}else h=m(k,c,d,b,e);i.push(h)}a.value=i}return a}function p(a,b,c){var d=b+"@odata.type";void 0===a[d]&&(a[d]="#"+c)}function q(a,b,c){var d=b+"@odata.type";void 0===a[d]&&(a[d]="Edm."===c.substring(0,4)?"#Collection("+c.substring(4)+")":"#Collection("+c+")")}function r(a,b,c){var d=b+"@odata.type";return void 0===a[d]&&(a[d]="Edm."===c.substring(0,4)?"#"+c.substring(4):"#"+c),a}function s(a,b,c){"Edm.Date"===c?a[b]=D.parseDate(a[b],!0):"Edm.DateTimeOffset"===c?a[b]=D.parseDateTimeOffset(a[b],!0):"Edm.Duration"===c?a[b]=D.parseDuration(a[b],!0):"Edm.Time"===c&&(a[b]=D.parseTime(a[b],!0))}function t(a,b,c){"Date"===c?a[b]=D.parseDate(a[b],!0):"DateTimeOffset"===c?a[b]=D.parseDateTimeOffset(a[b],!0):"Duration"===c?a[b]=D.parseDuration(a[b],!0):"Time"===c&&(a[b]=D.parseTime(a[b],!0))}function u(a,b){switch(a=""+v(a,b),a=encodeURIComponent(a.replace("'","''")),b){case"Edm.Binary":return"X'"+a+"'";case"Edm.DateTime":return"datetime'"+a+"'";case"Edm.DateTimeOffset":return"datetimeoffset'"+a+"'";case"Edm.Decimal":return a+"M";case"Edm.Guid":return"guid'"+a+"'";case"Edm.Int64":return a+"L";case"Edm.Float":return a+"f";case"Edm.Double":return a+"D";case"Edm.Geography":return"geography'"+a+"'";case"Edm.Geometry":return"geometry'"+a+"'";case"Edm.Time":return"time'"+a+"'";case"Edm.String":return"'"+a+"'";default:return a}}function v(a,b){switch(b){case"Edm.Binary":return O(a);default:return a}}function w(a){var b;0>a?(b="-",a=-a):b="+";var c=Math.floor(a/60);return a-=60*c,b+Q(c,2)+":"+Q(a,2)}function x(a){var b=a&&rb.exec(a);if(b){var c=new Date(M(b[1]));if(b[2]){var d=M(b[3]);"-"===b[2]&&(d=-d);var e=c.getUTCMinutes();c.setUTCMinutes(e-d),c.__edmType="Edm.DateTimeOffset",c.__offset=w(d)}if(!isNaN(c.valueOf()))return c}}function y(a,b){var c={};if(-1===a.indexOf("/")){if(0===a.length)return c.detectedPayloadKind=gb,c;if("Edm.Null"===a)return c.detectedPayloadKind=jb,c.isNullProperty=!0,c;if("Collection($ref)"===a)return c.detectedPayloadKind=ib,c;if("$ref"===a)return c.detectedPayloadKind=hb,c}c.type=void 0,c.typeName=void 0;for(var d,e=a.split("/"),f=0;f<e.length;++f){var g=e[f];if(void 0===c.typeName){if(-1!==g.indexOf("(")){for(var h=g.length-2,i=1;i>0&&h>0;--h)"("==g.charAt(h)?i--:")"==g.charAt(h)&&i++;var j=g.substring(h+2,g.length-1);if(g=g.substring(0,h+1),C.startsWith(g,"Collection")){if(c.detectedPayloadKind=fb,c.typeName=j,d=U(c.typeName,b),null!==d){c.type=d;continue}if(d=T(c.typeName,b),null!==d){c.type=d;continue}c.type=null;continue}c.projection=j}if(B(g)){c.typeName=g,c.type=null,c.detectedPayloadKind=jb;continue}var k=X(b),l=W(k.entitySet,g);if(null!==l){c.typeName=l.entityType,c.type=U(c.typeName,b),c.name=g,c.detectedPayloadKind=cb;continue}var m=V(k.singleton,g);if(null!==m){c.typeName=m.entityType,c.type=U(c.typeName,b),c.name=g,c.detectedPayloadKind=db;continue}}else{if(C.endsWith(g,"$entity")&&c.detectedPayloadKind===cb){c.detectedPayloadKind=db;continue}if(-1!==g.indexOf(".")){if(c.typeName=g,d=U(c.typeName,b),null!==d){c.type=d;continue}if(d=T(c.typeName,b),null!==d){c.type=d;continue}}if(c.detectedPayloadKind===cb||c.detectedPayloadKind===db){var n=Y(c.type.property,g);if(null!==n){if(c.typeName=n.type,C.startsWith(n.type,"Collection")){c.detectedPayloadKind=fb;var o=n.type.substring(11,n.type.length-1);c.typeName=o,c.type=T(o,b),c.detectedPayloadKind=fb}else c.type=T(n.type,b),c.detectedPayloadKind=eb;c.name=g}continue}if("$delta"===g){c.deltaKind=kb;continue}if(C.endsWith(g,"/$deletedEntity")){c.deltaKind=lb;continue}if(C.endsWith(g,"/$link")){c.deltaKind=mb;continue}if(C.endsWith(g,"/$deletedLink")){c.deltaKind=nb;continue}}}return c}function z(a,b){var c=a[H];if(!c||"string"!=typeof c)return null;var d=c.lastIndexOf("#");if(-1===d)return h(gb);var e=c.substring(d+1);return y(e,b)}function A(a,b){var c,d,e=b.key[0].propertyRef;if(c="(",1==e.length)d=Y(b.property,e[0].name).type,c+=u(a[e[0].name],d);else for(var f=!0,g=0;g<e.length;g++)f?f=!1:c+=",",d=Y(b.property,e[g].name).type,c+=e[g].name+"="+u(a[e[g].name],d);return c+=")"}function B(a){return _(a)||ab(a)||bb(a)}var C=c("./../utils.js"),D=c("./odatautils.js"),E=c("./handler.js"),F="odata",G=F+".",H="@"+G+"context",I=C.assigned,J=C.defined,K=C.isArray,L=C.isObject,M=C.parseInt10,N=C.getFormatKind,O=C.convertByteArrayToHexString,P=(D.formatDateTimeOffset,D.formatDuration),Q=D.formatNumberWidth,R=(D.getCanonicalTimezone,D.handler,D.isComplex,D.isPrimitive),S=D.isCollectionType,T=D.lookupComplexType,U=D.lookupEntityType,V=D.lookupSingleton,W=D.lookupEntitySet,X=D.lookupDefaultEntityContainer,Y=D.lookupProperty,Z=D.MAX_DATA_SERVICE_VERSION,$=D.maxVersion,_=D.isPrimitiveEdmType,ab=D.isGeographyEdmType,bb=D.isGeometryEdmType,cb="f",db="e",eb="p",fb="c",gb="s",hb="erl",ib="erls",jb="v",kb="f",lb="de",mb="l",nb="dl",ob="application/json",pb=E.contentType(ob),qb=["@odata.id","@odata.type"],rb=/^\/Date\((-?\d+)(\+|-)?(\d+)?\)\/$/,sb=function(a,b){return b&&"Edm.Time"===b.__edmType?P(b):b},tb=E.handler(d,e,ob,Z);tb.recognizeDates=!1,a.createPayloadInfo=z,a.jsonHandler=tb,a.jsonParser=d,a.jsonSerializer=e,a.parseJsonDateString=x},metadata:function(a,b,c){"use strict";function d(a,b,c,d){return{attributes:a,elements:b,text:c||!1,ns:d}}function e(a){if(!a)return a;if(a.length>1){var b=a.substr(0,2);return b===b.toUpperCase()?a:a.charAt(0).toLowerCase()+a.substr(1)}return a.charAt(0).toLowerCase()}function f(a,b){var c=a.elements;if(!c)return null;var d,f;for(d=0,f=c.length;f>d;d++){var g=c[d],h=!1;if("*"===g.charAt(g.length-1)&&(h=!0,g=g.substr(0,g.length-1)),b===g){var i=e(g);return{isArray:h,propertyName:i}}}return null}function g(a){return a===y}function h(a){var b=r(a),c=s(a),d=C.elements[b];if(!d)return null;if(d.ns){if(c!==d.ns)return null}else if(!g(c))return null;var i={},j=d.attributes||[];return n(a,function(a){var b=r(a),c=s(a),d=a.value;if(c!==t){var f=null;g(c)||null===c?f="":c===z&&(f="m:"),null!==f&&(f+=b,m(j,f)&&(i[e(b)]=d))}}),o(a,function(a){var b=r(a),c=f(d,b);if(c)if(c.isArray){var e=i[c.propertyName];e||(e=[],i[c.propertyName]=e),e.push(h(a))}else i[c.propertyName]=h(a)}),d.text&&(i.text=q(a)),i}function i(a,b){var c=u(b),d=p(c);return h(d)||void 0}var j=c("./../utils.js"),k=c("./../xml.js"),l=c("./handler.js"),m=j.contains,n=(j.normalizeURI,k.xmlAttributes),o=k.xmlChildElements,p=k.xmlFirstChildElement,q=k.xmlInnerText,r=k.xmlLocalName,s=k.xmlNamespaceURI,t=(k.xmlNS,k.xmlnsNS),u=k.xmlParse,v=k.http+"docs.oasis-open.org/odata/",w=v+"ns",x=w+"/edmx",y=w+"/edm",z=w+"/metadata",A=l.MAX_DATA_SERVICE_VERSION,B="application/xml",C={elements:{Action:d(["Name","IsBound","EntitySetPath"],["ReturnType","Parameter*","Annotation*"]),ActionImport:d(["Name","Action","EntitySet","Annotation*"]),Annotation:d(["Term","Qualifier","Binary","Bool","Date","DateTimeOffset","Decimal","Duration","EnumMember","Float","Guid","Int","String","TimeOfDay","AnnotationPath","NavigationPropertyPath","Path","PropertyPath","UrlRef"],["Binary*","Bool*","Date*","DateTimeOffset*","Decimal*","Duration*","EnumMember*","Float*","Guid*","Int*","String*","TimeOfDay*","And*","Or*","Not*","Eq*","Ne*","Gt*","Ge*","Lt*","Le*","AnnotationPath*","Apply*","Cast*","Collection*","If*","IsOf*","LabeledElement*","LabeledElementReference*","Null*","NavigationPropertyPath*","Path*","PropertyPath*","Record*","UrlRef*","Annotation*"]),AnnotationPath:d(null,null,!0),Annotations:d(["Target","Qualifier"],["Annotation*"]),Apply:d(["Function"],["String*","Path*","LabeledElement*","Annotation*"]),And:d(null,null,!0),Or:d(null,null,!0),Not:d(null,null,!0),Eq:d(null,null,!0),Ne:d(null,null,!0),Gt:d(null,null,!0),Ge:d(null,null,!0),Lt:d(null,null,!0),Le:d(null,null,!0),Binary:d(null,null,!0),Bool:d(null,null,!0),Cast:d(["Type"],["Path*","Annotation*"]),Collection:d(null,["Binary*","Bool*","Date*","DateTimeOffset*","Decimal*","Duration*","EnumMember*","Float*","Guid*","Int*","String*","TimeOfDay*","And*","Or*","Not*","Eq*","Ne*","Gt*","Ge*","Lt*","Le*","AnnotationPath*","Apply*","Cast*","Collection*","If*","IsOf*","LabeledElement*","LabeledElementReference*","Null*","NavigationPropertyPath*","Path*","PropertyPath*","Record*","UrlRef*"]),ComplexType:d(["Name","BaseType","Abstract","OpenType"],["Property*","NavigationProperty*","Annotation*"]),Date:d(null,null,!0),DateTimeOffset:d(null,null,!0),Decimal:d(null,null,!0),Duration:d(null,null,!0),EntityContainer:d(["Name","Extends"],["EntitySet*","Singleton*","ActionImport*","FunctionImport*","Annotation*"]),EntitySet:d(["Name","EntityType","IncludeInServiceDocument"],["NavigationPropertyBinding*","Annotation*"]),EntityType:d(["Name","BaseType","Abstract","OpenType","HasStream"],["Key*","Property*","NavigationProperty*","Annotation*"]),EnumMember:d(null,null,!0),EnumType:d(["Name","UnderlyingType","IsFlags"],["Member*"]),Float:d(null,null,!0),Function:d(["Name","IsBound","IsComposable","EntitySetPath"],["ReturnType","Parameter*","Annotation*"]),FunctionImport:d(["Name","Function","EntitySet","IncludeInServiceDocument","Annotation*"]),Guid:d(null,null,!0),If:d(null,["Path*","String*","Annotation*"]),Int:d(null,null,!0),IsOf:d(["Type","MaxLength","Precision","Scale","Unicode","SRID","DefaultValue","Annotation*"],["Path*"]),Key:d(null,["PropertyRef*"]),LabeledElement:d(["Name"],["Binary*","Bool*","Date*","DateTimeOffset*","Decimal*","Duration*","EnumMember*","Float*","Guid*","Int*","String*","TimeOfDay*","And*","Or*","Not*","Eq*","Ne*","Gt*","Ge*","Lt*","Le*","AnnotationPath*","Apply*","Cast*","Collection*","If*","IsOf*","LabeledElement*","LabeledElementReference*","Null*","NavigationPropertyPath*","Path*","PropertyPath*","Record*","UrlRef*","Annotation*"]),LabeledElementReference:d(["Term"],["Binary*","Bool*","Date*","DateTimeOffset*","Decimal*","Duration*","EnumMember*","Float*","Guid*","Int*","String*","TimeOfDay*","And*","Or*","Not*","Eq*","Ne*","Gt*","Ge*","Lt*","Le*","AnnotationPath*","Apply*","Cast*","Collection*","If*","IsOf*","LabeledElement*","LabeledElementReference*","Null*","NavigationPropertyPath*","Path*","PropertyPath*","Record*","UrlRef*"]),Member:d(["Name","Value"],["Annotation*"]),NavigationProperty:d(["Name","Type","Nullable","Partner","ContainsTarget"],["ReferentialConstraint*","OnDelete*","Annotation*"]),NavigationPropertyBinding:d(["Path","Target"]),NavigationPropertyPath:d(null,null,!0),Null:d(null,["Annotation*"]),OnDelete:d(["Action"],["Annotation*"]),Path:d(null,null,!0),Parameter:d(["Name","Type","Nullable","MaxLength","Precision","Scale","SRID"],["Annotation*"]),Property:d(["Name","Type","Nullable","MaxLength","Precision","Scale","Unicode","SRID","DefaultValue"],["Annotation*"]),PropertyPath:d(null,null,!0),PropertyRef:d(["Name","Alias"]),PropertyValue:d(["Property","Path"],["Binary*","Bool*","Date*","DateTimeOffset*","Decimal*","Duration*","EnumMember*","Float*","Guid*","Int*","String*","TimeOfDay*","And*","Or*","Not*","Eq*","Ne*","Gt*","Ge*","Lt*","Le*","AnnotationPath*","Apply*","Cast*","Collection*","If*","IsOf*","LabeledElement*","LabeledElementReference*","Null*","NavigationPropertyPath*","Path*","PropertyPath*","Record*","UrlRef*","Annotation*"]),Record:d(null,["PropertyValue*","Property*","Annotation*"]),ReferentialConstraint:d(["Property","ReferencedProperty","Annotation*"]),ReturnType:d(["Type","Nullable","MaxLength","Precision","Scale","SRID"]),String:d(null,null,!0),Schema:d(["Namespace","Alias"],["Action*","Annotations*","Annotation*","ComplexType*","EntityContainer","EntityType*","EnumType*","Function*","Term*","TypeDefinition*","Annotation*"]),Singleton:d(["Name","Type"],["NavigationPropertyBinding*","Annotation*"]),Term:d(["Name","Type","BaseTerm","DefaultValue ","AppliesTo","Nullable","MaxLength","Precision","Scale","SRID"],["Annotation*"]),TimeOfDay:d(null,null,!0),TypeDefinition:d(["Name","UnderlyingType","MaxLength","Unicode","Precision","Scale","SRID"],["Annotation*"]),UrlRef:d(null,["Binary*","Bool*","Date*","DateTimeOffset*","Decimal*","Duration*","EnumMember*","Float*","Guid*","Int*","String*","TimeOfDay*","And*","Or*","Not*","Eq*","Ne*","Gt*","Ge*","Lt*","Le*","AnnotationPath*","Apply*","Cast*","Collection*","If*","IsOf*","LabeledElement*","LabeledElementReference*","Null*","NavigationPropertyPath*","Path*","PropertyPath*","Record*","UrlRef*","Annotation*"]),Edmx:d(["Version"],["DataServices","Reference*"],!1,x),DataServices:d(["m:MaxDataServiceVersion","m:DataServiceVersion"],["Schema*"],!1,x),Reference:d(["Uri"],["Include*","IncludeAnnotations*","Annotation*"]),Include:d(["Namespace","Alias"]),IncludeAnnotations:d(["TermNamespace","Qualifier","TargetNamespace"])}};
a.metadataHandler=l.handler(i,null,B,A),a.schema=C,a.scriptCase=e,a.getChildSchema=f,a.parseConceptualModelElement=h,a.metadataParser=i},net:function(a,b,c){function d(a){return!(a.method&&"GET"!==a.method)}function e(a){var b=window.document.createElement("IFRAME");b.style.display="none";var c=a.replace(/&/g,"&amp;").replace(/"/g,"&quot;").replace(/</g,"&lt;"),d='<html><head><script type="text/javascript" src="'+c+'"></script></head><body></body></html>',e=window.document.getElementsByTagName("BODY")[0];return e.appendChild(b),l(b,d),b}function f(){if(window.XMLHttpRequest)return new window.XMLHttpRequest;var a;if(window.ActiveXObject)try{return new window.ActiveXObject("Msxml2.XMLHTTP.6.0")}catch(b){try{return new window.ActiveXObject("Msxml2.XMLHTTP.3.0")}catch(c){a=c}}else a={message:"XMLHttpRequest not supported"};throw a}function g(a){return 0===a.indexOf("http://")||0===a.indexOf("https://")||0===a.indexOf("file://")}function h(a){if(!g(a))return!0;var b=window.location,c=b.protocol+"//"+b.host+"/";return 0===a.indexOf(c)}function i(a,b){try{delete window[a]}catch(c){window[a]=void 0,b===p-1&&(p-=1)}}function j(a){return a&&(l(a,""),a.parentNode.removeChild(a)),null}function k(a,b){var c,d,e=a.getAllResponseHeaders().split(/\r?\n/);for(c=0,d=e.length;d>c;c++)if(e[c]){var f=e[c].split(": ");b[f[0]]=f[1]}}function l(a,b){var c=a.contentWindow?a.contentWindow.document:a.contentDocument.document;c.open(),c.write(b),c.close()}var m=c("./../utils.js"),n=m.defined,o=m.delay,p=0;a.defaultHttpClient={callbackParameterName:"$callback",formatQueryString:"$format=json",enableJsonpCallback:!1,request:function(){var a=this;return function(b,c,g){var l,m={},q=null,r=!1;m.abort=function(){l=j(l),r||(r=!0,q&&(q.abort(),q=null),g({message:"Request aborted"}))};var s,t=function(){l=j(l),r||(r=!0,q=null,g({message:"Request timed out"}))},u=b.requestUri,v=n(b.enableJsonpCallback,a.enableJsonpCallback),w=n(b.callbackParameterName,a.callbackParameterName),x=n(b.formatQueryString,a.formatQueryString);if(!v||h(u)){if(q=f(),q.onreadystatechange=function(){if(!r&&null!==q&&4===q.readyState){var a=q.statusText,d=q.status;1223===d&&(d=204,a="No Content");var e=[];k(q,e);var f={requestUri:u,statusCode:d,statusText:a,headers:e,body:q.responseText};r=!0,q=null,d>=200&&299>=d?c(f):g({message:"HTTP request failed",request:b,response:f})}},q.open(b.method||"GET",u,!0,b.user,b.password),b.headers)for(s in b.headers)q.setRequestHeader(s,b.headers[s]);b.timeoutMS&&(q.timeout=b.timeoutMS,q.ontimeout=t),q.send(b.body)}else{if(!d(b))throw{message:"Request is not local and cannot be done through JSONP."};var y=p;p+=1;var z,A=y.toString(),B=!1;s="handleJSONP_"+A,window[s]=function(a){if(l=j(l),!r){B=!0,window.clearTimeout(z),i(s,y),window.ActiveXObject&&(a=window.JSON.parse(window.JSON.stringify(a)));var b;b=x&&"$format=json"!=x?{"Content-Type":x.substring(8),"OData-Version":"4.0"}:{"Content-Type":"application/json;odata.metadata=minimal","OData-Version":"4.0"},o(function(){j(l),c({body:a,statusCode:200,headers:b})})}};var C=b.timeoutMS?b.timeoutMS:12e4;z=window.setTimeout(t,C);var D=w+"=parent."+s;x&&(D+="&"+x);var E=u.indexOf("?");-1===E?u=u+"?"+D:E===u.length-1?u+=D:u=u+"&"+D,l=e(u)}return m}}()},a.canUseJSONP=d,a.isAbsoluteUrl=g,a.isLocalUrl=h},odatautils:function(a,b,c){"use strict";function d(a,b){if(!a)return null;if(cb(a)){var c,e,f;for(c=0,e=a.length;e>c;c++)if(f=d(a[c],b))return f;return null}return a.dataServices?d(a.dataServices.schema,b):b(a)}function e(a,b){return a=0===a?"":"."+i(a.toString(),3),b>0&&(""===a&&(a=".000"),a+=i(b.toString(),4)),a}function f(a){return"/Date("+a.getTime()+")/"}function g(a){if("string"==typeof a)return a;var b=q(a),c=j(a.__offset);if(b&&"Z"!==c){a=new Date(a.valueOf());var d=W(c),f=a.getUTCHours()+d.d*d.h,g=a.getUTCMinutes()+d.d*d.m;a.setUTCHours(f,g)}else b||(c="");var h=a.getUTCFullYear(),k=a.getUTCMonth()+1,l="";0>=h&&(h=-(h-1),l="-");var m=e(a.getUTCMilliseconds(),a.__ns);return l+i(h,4)+"-"+i(k,2)+"-"+i(a.getUTCDate(),2)+"T"+i(a.getUTCHours(),2)+":"+i(a.getUTCMinutes(),2)+":"+i(a.getUTCSeconds(),2)+m+c}function h(a){var b=a.ms,c="";0>b&&(c="-",b=-b);var d=Math.floor(b/864e5);b-=864e5*d;var f=Math.floor(b/36e5);b-=36e5*f;var g=Math.floor(b/6e4);b-=6e4*g;var h=Math.floor(b/1e3);return b-=1e3*h,c+"P"+i(d,2)+"DT"+i(f,2)+"H"+i(g,2)+"M"+i(h,2)+e(b,a.ns)+"S"}function i(a,b,c){for(var d=a.toString(10);d.length<b;)c?d+="0":d="0"+d;return d}function j(a){return a&&"Z"!==a&&"+00:00"!==a&&"-00:00"!==a?a:"Z"}function k(a){if("string"==typeof a){var b=a.indexOf(")",10);if(0===a.indexOf("Collection(")&&b>0)return a.substring(11,b)}return null}function l(a,b,c,d,e,f){return e.request(a,function(e){try{e.headers&&P(e.headers),void 0===e.data&&204!==e.statusCode&&d.read(e,f)}catch(g){return void 0===g.request&&(g.request=a),void 0===g.response&&(g.response=e),void c(g)}try{b(e.data,e)}catch(g){throw g.bIsSuccessHandlerError=!0,g}},c)}function m(a){return p(a)&&cb(a.__batchRequests)}function n(a,b){var c=a&&a.results||a;return!!c&&o(b)||!b&&cb(c)&&!p(c[0])}function o(a){return oc.test(a)}function p(a){return!!a&&eb(a)&&!cb(a)&&!db(a)}function q(a){return"Edm.DateTimeOffset"===a.__edmType||!a.__edmType&&a.__offset}function r(a){if(!a&&!p(a))return!1;var b=a.__metadata||{},c=a.__deferred||{};return!b.type&&!!c.uri}function s(a){return p(a)&&a.__metadata&&"uri"in a.__metadata}function t(a,b){var c=a&&a.results||a;return cb(c)&&!o(b)&&p(c[0])}function u(a){return ab(mc,a)||-1===a.indexOf(".")&&ab(nc,a)}function v(a){return ab(kc,a)||-1===a.indexOf(".")&&ab(lc,a)}function w(a){if(!a&&!p(a))return!1;var b=a.__metadata,c=a.__mediaresource;return!b&&!!c&&!!c.media_src}function x(a){return db(a)||"string"==typeof a||"number"==typeof a||"boolean"==typeof a}function y(a){return ab(jc,a)}function z(a,b){return r(a)?"deferred":s(a)?"entry":t(a)?"feed":b&&b.relationship?null!==a&&void 0!==a&&t(a)?"feed":"entry":null}function A(a,b){return bb(a,function(a){return a.name===b})}function B(a,b,c){return a?d(b,function(b){return N(a,b,c)}):null}function C(a,b){return bb(a,function(a){return a.name===b})}function D(a,b){return bb(a,function(a){return a.name===b})}function E(a,b){return B(a,b,"complexType")}function F(a,b){return B(a,b,"entityType")}function G(a){return d(a,function(a){return eb(a.entityContainer)?a.entityContainer:void 0})}function H(a,b){return B(a,b,"entityContainer")}function I(a,b){return bb(a,function(a){return a.name===b})}function J(a,b){var c=null;if(a){var e=a.relationship,f=d(b,function(a){var b=M(a.namespace,e),c=a.association;if(b&&c){var d,f;for(d=0,f=c.length;f>d;d++)if(c[d].name===b)return c[d]}return null});if(f){var g=f.end[0];g.role!==a.toRole&&(g=f.end[1]),c=g.type}}return c}function K(a,b,c){if(a){var e=a.relationship,f=d(c,function(a){for(var b=a.entityContainer,c=0;c<b.length;c++){var d=b[c].associationSet;if(d)for(var f=0;f<d.length;f++)if(d[f].association==e)return d[f]}return null});if(f&&f.end[0]&&f.end[1])return f.end[0].entitySet==b?f.end[1].entitySet:f.end[0].entitySet}return null}function L(a,b){var c=d(b,function(b){var c=b.entityContainer,d=c.entitySet;if(d)for(var e=0;e<d.length;e++)if(d[e].name==a)return{entitySet:d[e],containerName:c.name,functionImport:c.functionImport};return null});return c}function M(a,b){return 0===b.indexOf(a)&&"."===b.charAt(a.length)?b.substr(a.length+1):null}function N(a,b,c){if(a&&b){var d=M(b.namespace,a);if(d)return bb(b[c],function(a){return a.name===d})}return null}function O(a,b){if(a===b)return a;for(var c=a.split("."),d=b.split("."),e=c.length>=d.length?c.length:d.length,f=0;e>f;f++){var g=c[f]&&fb(c[f]),h=d[f]&&fb(d[f]);if(g>h)return a;if(h>g)return b}}function P(a){for(var b in a){var c=b.toLowerCase(),d=pc[c];if(d&&b!==d){var e=a[b];delete a[b],a[d]=e}}}function Q(a){return"boolean"==typeof a?a:"string"==typeof a&&"true"===a.toLowerCase()}function R(a,b,c){var d=qc.exec(a),e=d?j(d[8]):null;if(!d||!b&&"Z"!==e){if(c)return null;throw{message:"Invalid date/time value"}}var f=fb(d[1]);0>=f&&f++;var g=d[7],h=0;if(g){if(g.length>7){if(c)return null;throw{message:"Cannot parse date/time value to given precision."}}h=i(g.substring(3),4,!0),g=i(g.substring(0,3),3,!0),g=fb(g),h=fb(h)}else g=0;var k=fb(d[4]),l=fb(d[5]),m=fb(d[6])||0;if("Z"!==e){var n=W(e),o=-n.d;k+=n.h*o,l+=n.m*o}var p=new Date;if(p.setUTCFullYear(f,fb(d[2])-1,fb(d[3])),p.setUTCHours(k,l,m,g),isNaN(p.valueOf())){if(c)return null;throw{message:"Invalid date/time value"}}return b&&(p.__edmType="Edm.DateTimeOffset",p.__offset=e),h&&(p.__ns=h),p}function S(a,b){var c=a.split("-");return 3!=c.length&&b?null:new Date(fb(c[0]),fb(c[1])-1,fb(c[2],0,0,0,0))}function T(a){var b=rc.exec(a);return{h:fb(b[1]),m:fb(b[2]),s:fb(b[4]),ms:fb(b[6])}}function U(a,b){return R(a,!0,b)}function V(a){var b=sc.exec(a);if(null===b)throw{message:"Invalid duration value."};var c=b[2]||"0",d=b[3]||"0",e=fb(b[4]||0),f=fb(b[5]||0),g=fb(b[6]||0),h=parseFloat(b[7]||0);if("0"!==c||"0"!==d)throw{message:"Unsupported duration value."};var j=b[8],k=0;if(j){if(j.length>7)throw{message:"Cannot parse duration value to given precision."};k=i(j.substring(3),4,!0),j=i(j.substring(0,3),3,!0),j=fb(j),k=fb(k)}else j=0;j+=1e3*h+6e4*g+36e5*f+864e5*e,"-"===b[1]&&(j=-j);var l={ms:j,__edmType:"Edm.Time"};return k&&(l.ns=k),l}function W(a){var b=a.substring(0,1);b="+"===b?1:-1;var c=fb(a.substring(1)),d=fb(a.substring(a.indexOf(":")+1));return{d:b,h:c,m:d}}function X(a,b,c){a.method||(a.method="GET"),a.headers?P(a.headers):a.headers={},void 0===a.headers.Accept&&(a.headers.Accept=b.accept),_(a.data)&&void 0===a.body&&b.write(a,c),_(a.headers["OData-MaxVersion"])||(a.headers["OData-MaxVersion"]=b.maxDataServiceVersion||"4.0"),void 0===a.async&&(a.async=!0)}function Y(a,b,c){if(a&&"object"==typeof a)for(var d in a){var e=a[d],f=Y(e,d,c);f=c(d,f,b),f!==e&&(void 0===e?delete a[d]:a[d]=f)}return a}function Z(a,b){return b("",Y(a,"",b))}var $=c("./../utils.js"),_=$.assigned,ab=$.contains,bb=$.find,cb=$.isArray,db=$.isDate,eb=$.isObject,fb=$.parseInt10,gb=function(a,b){var c=(a&&a.__metadata||{}).type;return c||(b?b.type:null)},hb="Edm.",ib=hb+"Boolean",jb=hb+"Byte",kb=hb+"SByte",lb=hb+"Int16",mb=hb+"Int32",nb=hb+"Int64",ob=hb+"Single",pb=hb+"Double",qb=hb+"Decimal",rb=hb+"String",sb=hb+"Binary",tb=hb+"Date",ub=hb+"DateTimeOffset",vb=hb+"Duration",wb=hb+"Guid",xb=hb+"Time",yb="Geography",zb=hb+yb,Ab=zb+"Point",Bb=zb+"LineString",Cb=zb+"Polygon",Db=zb+"Collection",Eb=zb+"MultiPolygon",Fb=zb+"MultiLineString",Gb=zb+"MultiPoint",Hb=yb+"Point",Ib=yb+"LineString",Jb=yb+"Polygon",Kb=yb+"Collection",Lb=yb+"MultiPolygon",Mb=yb+"MultiLineString",Nb=yb+"MultiPoint",Ob="Geometry",Pb=hb+Ob,Qb=Pb+"Point",Rb=Pb+"LineString",Sb=Pb+"Polygon",Tb=Pb+"Collection",Ub=Pb+"MultiPolygon",Vb=Pb+"MultiLineString",Wb=Pb+"MultiPoint",Xb=Ob+"Point",Yb=Ob+"LineString",Zb=Ob+"Polygon",$b=Ob+"Collection",_b=Ob+"MultiPolygon",ac=Ob+"MultiLineString",bc=Ob+"MultiPoint",cc="Point",dc="LineString",ec="Polygon",fc="MultiPoint",gc="MultiLineString",hc="MultiPolygon",ic="GeometryCollection",jc=[rb,mb,nb,ib,pb,ob,tb,ub,vb,xb,qb,wb,jb,lb,kb,sb],kc=[Pb,Qb,Rb,Sb,Tb,Ub,Vb,Wb],lc=[Ob,Xb,Yb,Zb,$b,_b,ac,bc],mc=[zb,Ab,Bb,Cb,Db,Eb,Fb,Gb],nc=[yb,Hb,Ib,Jb,Kb,Lb,Mb,Nb],oc=/Collection\((.*)\)/,pc={"content-type":"Content-Type","content-encoding":"Content-Encoding","content-length":"Content-Length","odata-version":"OData-Version",accept:"Accept","accept-charset":"Accept-Charset","if-match":"If-Match","if-none-match":"If-None-Match","odata-isolation":"OData-Isolation","odata-maxversion":"OData-MaxVersion",prefer:"Prefer","content-id":"Content-ID","content-transfer-encoding":"Content-Transfer-Encoding",etag:"ETag",location:"Location","odata-entityid":"OData-EntityId","preference-applied":"Preference-Applied","retry-after":"Retry-After"},qc=/^(-?\d{4,})-(\d{2})-(\d{2})T(\d{2}):(\d{2})(?::(\d{2}))?(?:\.(\d+))?(.*)$/,rc=/^(\d+):(\d+)(:(\d+)(.(\d+))?)?$/,sc=/^([+-])?P(?:(\d+)Y)?(?:(\d+)M)?(?:(\d+)D)?(?:T(?:(\d+)H)?(?:(\d+)M)?(?:(\d+)(?:\.(\d+))?S)?)?/;a.dataItemTypeName=gb,a.EDM_BINARY=sb,a.EDM_BOOLEAN=ib,a.EDM_BYTE=jb,a.EDM_DATE=tb,a.EDM_DATETIMEOFFSET=ub,a.EDM_DURATION=vb,a.EDM_DECIMAL=qb,a.EDM_DOUBLE=pb,a.EDM_GEOGRAPHY=zb,a.EDM_GEOGRAPHY_POINT=Ab,a.EDM_GEOGRAPHY_LINESTRING=Bb,a.EDM_GEOGRAPHY_POLYGON=Cb,a.EDM_GEOGRAPHY_COLLECTION=Db,a.EDM_GEOGRAPHY_MULTIPOLYGON=Eb,a.EDM_GEOGRAPHY_MULTILINESTRING=Fb,a.EDM_GEOGRAPHY_MULTIPOINT=Gb,a.EDM_GEOMETRY=Pb,a.EDM_GEOMETRY_POINT=Qb,a.EDM_GEOMETRY_LINESTRING=Rb,a.EDM_GEOMETRY_POLYGON=Sb,a.EDM_GEOMETRY_COLLECTION=Tb,a.EDM_GEOMETRY_MULTIPOLYGON=Ub,a.EDM_GEOMETRY_MULTILINESTRING=Vb,a.EDM_GEOMETRY_MULTIPOINT=Wb,a.EDM_GUID=wb,a.EDM_INT16=lb,a.EDM_INT32=mb,a.EDM_INT64=nb,a.EDM_SBYTE=kb,a.EDM_SINGLE=ob,a.EDM_STRING=rb,a.EDM_TIMEOFDAY=xb,a.GEOJSON_POINT=cc,a.GEOJSON_LINESTRING=dc,a.GEOJSON_POLYGON=ec,a.GEOJSON_MULTIPOINT=fc,a.GEOJSON_MULTILINESTRING=gc,a.GEOJSON_MULTIPOLYGON=hc,a.GEOJSON_GEOMETRYCOLLECTION=ic,a.forEachSchema=d,a.formatDateTimeOffset=g,a.formatDateTimeOffsetJSON=f,a.formatDuration=h,a.formatNumberWidth=i,a.getCanonicalTimezone=j,a.getCollectionType=k,a.invokeRequest=l,a.isBatch=m,a.isCollection=n,a.isCollectionType=o,a.isComplex=p,a.isDateTimeOffset=q,a.isDeferred=r,a.isEntry=s,a.isFeed=t,a.isGeographyEdmType=u,a.isGeometryEdmType=v,a.isNamedStream=w,a.isPrimitive=x,a.isPrimitiveEdmType=y,a.lookupComplexType=E,a.lookupDefaultEntityContainer=G,a.lookupEntityContainer=H,a.lookupEntitySet=C,a.lookupSingleton=D,a.lookupEntityType=F,a.lookupFunctionImport=I,a.lookupNavigationPropertyType=J,a.lookupNavigationPropertyEntitySet=K,a.lookupInSchema=N,a.lookupProperty=A,a.lookupInMetadata=B,a.getEntitySetInfo=L,a.maxVersion=O,a.navigationPropertyKind=z,a.normalizeHeaders=P,a.parseBool=Q,a.parseDate=S,a.parseDateTimeOffset=U,a.parseDuration=V,a.parseTimeOfDay=T,a.parseInt10=fb,a.prepareRequest=X,a.removeNamespace=M,a.traverse=Z},store:function(a,b,c){a.defaultStoreMechanism="best",a.createStore=function(b,c){c||(c=a.defaultStoreMechanism),"best"===c&&(c=DomStore.isSupported()?"dom":"memory");var e=d[c];if(e)return e.create(b);throw{message:"Failed to create store",name:b,mechanism:c}},a.DomStore=DomStore=c("./store/dom.js"),a.IndexedDBStore=IndexedDBStore=c("./store/indexeddb.js"),a.MemoryStore=MemoryStore=c("./store/memory.js");var d={indexeddb:IndexedDBStore,dom:DomStore,memory:MemoryStore};a.mechanisms=d},dom:function(a,b,c){"use strict";function d(){var a={v:this.valueOf(),t:"[object Date]"};for(var b in this)a[b]=this[b];return a}function e(a,b){if(b&&"[object Date]"===b.t){var c=new Date(b.v);for(var d in b)"t"!==d&&"v"!==d&&(c[d]=b[d]);b=c}return b}function f(a,b){return a.name+"#!#"+b}function g(a,b){return b.replace(a.name+"#!#","")}function h(a){this.name=a}var i=c("./../utils.js"),j=i.throwErrorCallback,k=i.delay,l=null;h.create=function(a){if(h.isSupported())return l=l||window.localStorage,new h(a);throw{message:"Web Storage not supported by the browser"}},h.isSupported=function(){return!!window.localStorage},h.prototype.add=function(a,b,c,d){d=d||this.defaultError;var e=this;this.contains(a,function(f){f?k(d,{message:"key already exists",key:a}):e.addOrUpdate(a,b,c,d)},d)},h.prototype.addOrUpdate=function(a,b,c,e){if(e=e||this.defaultError,a instanceof Array)e({message:"Array of keys not supported"});else{var g=f(this,a),h=Date.prototype.toJSON;try{var i=b;void 0!==i&&(Date.prototype.toJSON=d,i=window.JSON.stringify(b)),l.setItem(g,i),k(c,a,b)}catch(j){22===j.code||2147942414===j.number?k(e,{name:"QUOTA_EXCEEDED_ERR",error:j}):k(e,j)}finally{Date.prototype.toJSON=h}}},h.prototype.clear=function(a,b){b=b||this.defaultError;try{for(var c=0,d=l.length;d>0&&d>c;){var e=l.key(c),f=g(this,e);e!==f?(l.removeItem(e),d=l.length):c++}k(a)}catch(h){k(b,h)}},h.prototype.close=function(){},h.prototype.contains=function(a,b,c){c=c||this.defaultError;try{var d=f(this,a),e=l.getItem(d);k(b,null!==e)}catch(g){k(c,g)}},h.prototype.defaultError=j,h.prototype.getAllKeys=function(a,b){b=b||this.defaultError;var c,d,e=[];try{for(c=0,d=l.length;d>c;c++){var f=l.key(c),h=g(this,f);f!==h&&e.push(h)}k(a,e)}catch(i){k(b,i)}},h.prototype.mechanism="dom",h.prototype.read=function(a,b,c){if(c=c||this.defaultError,a instanceof Array)c({message:"Array of keys not supported"});else try{var d=f(this,a),g=l.getItem(d);g=null!==g&&"undefined"!==g?window.JSON.parse(g,e):void 0,k(b,a,g)}catch(h){k(c,h)}},h.prototype.remove=function(a,b,c){if(c=c||this.defaultError,a instanceof Array)c({message:"Batches not supported"});else try{var d=f(this,a);l.removeItem(d),k(b)}catch(e){k(c,e)}},h.prototype.update=function(a,b,c,d){d=d||this.defaultError;var e=this;this.contains(a,function(f){f?e.addOrUpdate(a,b,c,d):k(d,{message:"key not found",key:a})},d)},b.exports=h},indexeddb:function(a,b,c){"use strict";function d(a,b){return function(c){var d=a||b;if(d){if("[object IDBDatabaseException]"===Object.prototype.toString.call(c))return 11===c.code?void d({name:"QuotaExceededError",error:c}):void d(c);var e;try{var f=c.target.error||c;e=f.name}catch(g){e="blocked"===c.type?"IndexedDBBlocked":"UnknownError"}d({name:e,error:c})}}}function e(a,b,c){var d=a.name,e="_odatajs_"+d,f=j.open(e);f.onblocked=c,f.onerror=c,f.onupgradeneeded=function(){var a=f.result;a.objectStoreNames.contains(d)||a.createObjectStore(d)},f.onsuccess=function(a){var e=f.result;if(!e.objectStoreNames.contains(d)){if("setVersion"in e){var g=e.setVersion("1.0");return g.onsuccess=function(){var a=g.transaction;a.oncomplete=function(){b(e)},e.createObjectStore(d,null,!1)},g.onerror=c,void(g.onblocked=c)}return a.target.error={name:"DBSchemaMismatch"},void c(a)}e.onversionchange=function(a){a.target.close()},b(e)}}function f(a,b,c,f){var g=a.name,h=a.db,i=d(f,a.defaultError);return h?void c(h.transaction(g,b)):void e(a,function(d){a.db=d,c(d.transaction(g,b))},i)}function g(a){this.name=a}var h=c("./../utils.js"),i=h.throwErrorCallback,j=(h.delay,h.inBrowser()?window.mozIndexedDB||window.webkitIndexedDB||window.msIndexedDB||window.indexedDB:void 0),k=h.inBrowser()?window.IDBKeyRange||window.webkitIDBKeyRange:void 0,l=h.inBrowser()?window.IDBTransaction||window.webkitIDBTransaction||{}:{},m=l.READ_ONLY||"readonly",n=l.READ_WRITE||"readwrite";g.create=function(a){if(g.isSupported())return new g(a);throw{message:"IndexedDB is not supported on this browser"}},g.isSupported=function(){return!!j},g.prototype.add=function(a,b,c,e){var g=this.name,h=this.defaultError,i=[],j=[];a instanceof Array?(i=a,j=b):(i=[a],j=[b]),f(this,n,function(f){f.onabort=d(e,h,a,"add"),f.oncomplete=function(){a instanceof Array?c(i,j):c(a,b)};for(var k=0;k<i.length&&k<j.length;k++)f.objectStore(g).add({v:j[k]},i[k])},e)},g.prototype.addOrUpdate=function(a,b,c,e){var g=this.name,h=this.defaultError,i=[],j=[];a instanceof Array?(i=a,j=b):(i=[a],j=[b]),f(this,n,function(f){f.onabort=d(e,h),f.oncomplete=function(){a instanceof Array?c(i,j):c(a,b)};for(var k=0;k<i.length&&k<j.length;k++){var l={v:j[k]};f.objectStore(g).put(l,i[k])}},e)},g.prototype.clear=function(a,b){var c=this.name,e=this.defaultError;f(this,n,function(f){f.onerror=d(b,e),f.oncomplete=function(){a()},f.objectStore(c).clear()},b)},g.prototype.close=function(){this.db&&(this.db.close(),this.db=null)},g.prototype.contains=function(a,b,c){var e=this.name,g=this.defaultError;f(this,m,function(f){var h=f.objectStore(e),i=h.get(a);f.oncomplete=function(){b(!!i.result)},f.onerror=d(c,g)},c)},g.prototype.defaultError=i,g.prototype.getAllKeys=function(a,b){var c=this.name,e=this.defaultError;f(this,n,function(f){var g=[];f.oncomplete=function(){a(g)};var h=f.objectStore(c).openCursor();h.onerror=d(b,e),h.onsuccess=function(a){var b=a.target.result;b&&(g.push(b.key),b["continue"].call(b))}},b)},g.prototype.mechanism="indexeddb",g.prototype.read=function(a,b,c){var e=this.name,g=this.defaultError,h=a instanceof Array?a:[a];f(this,m,function(f){var i=[];f.onerror=d(c,g,a,"read"),f.oncomplete=function(){a instanceof Array?b(h,i):b(h[0],i[0])};for(var j=0;j<h.length;j++){var k=f.objectStore(e),l=k.get.call(k,h[j]);l.onsuccess=function(a){var b=a.target.result;i.push(b?b.v:void 0)}}},c)},g.prototype.remove=function(a,b,c){var e=this.name,g=this.defaultError,h=a instanceof Array?a:[a];f(this,n,function(a){a.onerror=d(c,g),a.oncomplete=function(){b()};for(var f=0;f<h.length;f++){var i=a.objectStore(e);i["delete"].call(i,h[f])}},c)},g.prototype.update=function(a,b,c,e){var g=this.name,h=this.defaultError,i=[],j=[];a instanceof Array?(i=a,j=b):(i=[a],j=[b]),f(this,n,function(f){f.onabort=d(e,h),f.oncomplete=function(){a instanceof Array?c(i,j):c(a,b)};for(var l=0;l<i.length&&l<j.length;l++){var m=f.objectStore(g).openCursor(k.only(i[l])),n={v:j[l]};m.pair={key:i[l],value:n},m.onsuccess=function(a){var b=a.target.result;b?b.update(a.target.pair.value):f.abort()}}},e)},b.exports=g},memory:function(a,b,c){"use strict";function d(a){function b(a,b){var c;return a instanceof Array&&(c="Array of keys not supported"),(void 0===a||null===a)&&(c="Invalid key"),c?(g(b,{message:c}),!1):!0}var c=[],d=[],e={};this.name=a;var f=function(a){return a||this.defaultError};this.add=function(a,c,d,g){g=f(g),b(a,g)&&(e.hasOwnProperty(a)?g({message:"key already exists",key:a}):this.addOrUpdate(a,c,d,g))},this.addOrUpdate=function(a,h,i,j){if(j=f(j),b(a,j)){var k=e[a];void 0===k&&(k=c.length>0?c.splice(0,1):d.length),d[k]=h,e[a]=k,g(i,a,h)}},this.clear=function(a){d=[],e={},c=[],g(a)},this.contains=function(a,b){var c=e.hasOwnProperty(a);g(b,c)},this.getAllKeys=function(a){var b=[];for(var c in e)b.push(c);g(a,b)},this.read=function(a,c,h){if(h=f(h),b(a,h)){var i=e[a];g(c,a,d[i])}},this.remove=function(a,h,i){if(i=f(i),b(a,i)){var j=e[a];void 0!==j&&(j===d.length-1?d.pop():(d[j]=void 0,c.push(j)),delete e[a],0===d.length&&(c=[])),g(h)}},this.update=function(a,c,d,g){g=f(g),b(a,g)&&(e.hasOwnProperty(a)?this.addOrUpdate(a,c,d,g):g({message:"key not found",key:a}))}}var e=c("./../utils.js"),f=e.throwErrorCallback,g=e.delay;d.create=function(a){return new d(a)},d.isSupported=function(){return!0},d.prototype.close=function(){},d.prototype.defaultError=f,d.prototype.mechanism="memory",b.exports=d},utils:function(a){"use strict";function b(){return"undefined"!=typeof window}function c(a){return null!==a&&void 0!==a}function d(a,b){var c,d;for(c=0,d=a.length;d>c;c++)if(a[c]===b)return!0;return!1}function e(a,b){return void 0!==a?a:b}function f(a){if(1===arguments.length)return void window.setTimeout(a,0);var b=Array.prototype.slice.call(arguments,1);window.setTimeout(function(){a.apply(this,b)},0)}function g(a,b,c){if(!a)throw{message:"Assert fired: "+b,data:c}}function h(a,b){for(var c in b)a[c]=b[c];return a}function i(a,b){if(a){var c,d;for(c=0,d=a.length;d>c;c++)if(b(a[c]))return a[c]}return null}function j(a){return"[object Array]"===Object.prototype.toString.call(a)}function k(a){return"[object Date]"===Object.prototype.toString.call(a)}function l(a){return"object"==typeof a}function m(a){return parseInt(a,10)}function n(a,b,c){a.hasOwnProperty(b)&&(a[c]=a[b],delete a[b])}function o(a){throw a}function p(a){return a.trim?a.trim():a.replace(/^\s+|\s+$/g,"")}function q(a,b){return void 0!==a?a:b}function r(a){var b={isAbsolute:!1};if(a){var c=I.exec(a);if(c){var d,e;for(d=0,e=J.length;e>d;d++)c[d+1]&&(b[J[d]]=c[d+1])}b.scheme&&(b.isAbsolute=!0)}return b}function s(a){return"".concat(a.scheme||"",a.authority||"",a.path||"",a.query||"",a.fragment||"")}function t(a){var b=r(a),c=b.scheme,d=b.authority;if(c&&(b.scheme=c.toLowerCase(),d)){var e=K.exec(d);e&&(b.authority="//"+(e[1]?e[1]+"@":"")+e[2].toLowerCase()+(e[3]?":"+e[3]:""))}return a=s(b),a.replace(L,function(a){return a.toLowerCase()})}function u(a,b){if(!b)return a;var c=r(a);if(c.isAbsolute)return a;var d,e=r(b),f={};return c.authority?(f.authority=c.authority,d=c.path,f.query=c.query):(c.path?(d="/"===c.path.charAt(0)?c.path:v(c.path,e.path),f.query=c.query):(d=e.path,f.query=c.query||e.query),f.authority=e.authority),f.path=w(d),f.scheme=e.scheme,f.fragment=c.fragment,s(f)}function v(a,b){var c,d="/";return b&&(c=b.lastIndexOf("/"),d=b.substring(0,c),"/"!==d.charAt(d.length-1)&&(d+="/")),d+a}function w(a){for(var b,c="",d="";a;)0===a.indexOf("..")||0===a.indexOf(".")?a=a.replace(/^\.\.?\/?/g,""):0===a.indexOf("/..")?(a=a.replace(/^\/\..\/?/g,"/"),b=c.lastIndexOf("/"),c=-1===b?"":c.substring(0,b)):0===a.indexOf("/.")?a=a.replace(/^\/\.\/?/g,"/"):(d=a,b=a.indexOf("/",1),-1!==b&&(d=a.substring(0,b)),c+=d,a=a.replace(d,""));return c}function x(a){var b=[];if(void 0===window.atob)b=y(a);else for(var c=window.atob(a),d=0;d<c.length;d++)b.push(c.charCodeAt(d));for(var e="",f="0123456789ABCDEF",g=0;g<b.length;g++){var h=b[g];e+=f[h>>4],e+=f[15&h]}return e}function y(a){for(var b="",c=0;c<a.length;c++){var d=z(a[c]),e="";null!==d&&(e=d.toString(2),b+=A(e))}var f=[],g=parseInt(b.length/8,10);for(c=0;g>c;c++){var h=parseInt(b.substring(8*c,8*(c+1)),2);f.push(h)}return f}function z(a){var b=a.charCodeAt(0),c=65,d=6;return b>=65&&90>=b?b-c:b>=97&&122>=b?b-c-d:b>=48&&57>=b?b+4:"+"==a?62:"/"==a?63:null}function A(a){for(;a.length<6;)a="0"+a;return a}function B(a){return a&&a.value?a.value.length:0}function C(a,b,c){if(void 0===a||void 0===a.value)return a;0>b&&(b=0);var d=B(a);c>d&&(c=d);var e={};for(var f in a)e[f]="value"==f?a[f].slice(b,c):a[f];return e}function D(a,b){return void 0===b||void 0===b.value?a:void 0===a||0===Object.keys(a).length?b:void 0===a.value?(a.value=b.value,a):(a.value=a.value.concat(b.value),a)}function E(a,b){return-1!==a.indexOf(b,a.length-b.length)}function F(a,b){return 0===a.indexOf(b)}function G(a,b){var d=b;if(!c(a))return d;var e=a.toLowerCase();switch(e){case"none":d=0;break;case"minimal":d=1;break;case"full":d=2}return d}var H=function(a){return window.ActiveXObject?new window.ActiveXObject(a):null},I=/^([^:\/?#]+:)?(\/\/[^\/?#]*)?([^?#:]+)?(\?[^#]*)?(#.*)?/,J=["scheme","authority","path","query","fragment"],K=/^\/{0,2}(?:([^@]*)@)?([^:]+)(?::{1}(\d+))?/,L=/%[0-9A-F]{2}/gi;a.inBrowser=b,a.activeXObject=H,a.assigned=c,a.contains=d,a.defined=e,a.delay=f,a.djsassert=g,a.extend=h,a.find=i,a.getURIInfo=r,a.isArray=j,a.isDate=k,a.isObject=l,a.normalizeURI=u,a.normalizeURICase=t,a.parseInt10=m,a.renameProperty=n,a.throwErrorCallback=o,a.trimString=p,a.undefinedDefault=q,a.decodeBase64=y,a.convertByteArrayToHexString=x,a.getJsonValueArraryLength=B,a.sliceJsonValueArray=C,a.concatJsonValueArray=D,a.startsWith=F,a.endsWith=E,a.getFormatKind=G},xml:function(a,b,c){"use strict";function d(a){var b=/(^\s)|(\s$)/;return b.test(a)}function e(a){var b=/^\s*$/;return null===a||b.test(a)}function f(a){for(;null!==a&&1===a.nodeType;){var b=q(a,"space",$);if("preserve"===b)return!0;if("default"===b)break;a=a.parentNode}return!1}function g(a){var b=a.nodeName;return"xmlns"==b||0===b.indexOf("xmlns:")}function h(a,b,c){try{a.setProperty(b,c)}catch(d){}}function i(){var a=R("Msxml2.DOMDocument.3.0");return a&&(h(a,"ProhibitDTD",!0),h(a,"MaxElementDepth",256),h(a,"AllowDocumentFunction",!1),h(a,"AllowXsltScript",!1)),a}function j(){try{var a=R("Msxml2.DOMDocument.6.0");return a&&(a.async=!0),a}catch(b){return i()}}function k(a){var b=j();if(!b)return null;b.loadXML(a);var c=b.parseError;return 0!==c.errorCode&&l(c.reason,c.srcText,a),b}function l(a,b,c){throw"string"==typeof a&&(a={message:a}),T(a,{srcText:b||"",errorXmlText:c||""})}function m(a){var b=void 0;b=Q.inBrowser()?window.DOMParser&&new window.DOMParser:new(c("xmldom").DOMParser);var d;if(!b)return d=k(a),d||l("XML DOM parser not supported"),d;try{d=b.parseFromString(a,"text/xml")}catch(e){l(e,"",a)}var f=d.documentElement,g=f.namespaceURI,h=A(f);if("parsererror"===h&&g===_){var i=w(f,_,"sourcetext"),j=i?C(i):"";l(z(f)||"",j,a)}if("h3"===h&&g===Y||x(f,Y,"h3")){for(var m="",n=[],o=f.firstChild;o;)1===o.nodeType&&(m+=z(o)||""),n.push(o.nextSibling),o=o.firstChild||n.shift();m+=z(f)||"",l(m,"",a)}return d}function n(a,b){return a?a+":"+b:b}function o(a,b){if(d(b.data)){var c=r(a,$,"space");c||(c=I(a.ownerDocument,$,n("xml","space")),H(a,c)),c.value="preserve"}return a.appendChild(b),a}function p(a,b){var c,d,e=a.attributes;for(c=0,d=e.length;d>c;c++)b(e.item(c))}function q(a,b,c){var d=r(a,b,c);return d?C(d):null}function r(a,b,c){var d=a.attributes;return d.getNamedItemNS?d.getNamedItemNS(c||null,b):d.getQualifiedItem(b,c)||null}function s(a,b){var c=r(a,"base",$);return(c?V(c.value,b):b)||null}function t(a,b){D(a,!1,function(a){return 1===a.nodeType&&b(a),!0})}function u(a,b,c){var d,e,f=c.split("/");for(d=0,e=f.length;e>d;d++)a=a&&w(a,b,f[d]);return a||null}function v(a,b,c){var d=c.lastIndexOf("/"),e=c.substring(d+1),f=c.substring(0,d),g=f?u(a,b,f):a;return g?"@"===e.charAt(0)?r(g,e.substring(1),b):w(g,b,e):null}function w(a,b,c){return y(a,b,c,!1)}function x(a,b,c){if(a.getElementsByTagNameNS){var d=a.getElementsByTagNameNS(b,c);return d.length>0?d[0]:null}return y(a,b,c,!0)}function y(a,b,c,d){var e=null;return D(a,d,function(a){if(1===a.nodeType){var d=!b||B(a)===b,f=!c||A(a)===c;d&&f&&(e=a)}return null===e}),e}function z(a){var b,c=null,d=9===a.nodeType&&a.documentElement?a.documentElement:a,g=d.ownerDocument.preserveWhiteSpace===!1;return D(d,!1,function(a){if(3===a.nodeType||4===a.nodeType){var h=C(a),i=g||!e(h);i||(void 0===b&&(b=f(d)),i=b),i&&(c?c+=h:c=h)}return!0}),c}function A(a){return a.localName||a.baseName}function B(a){return a.namespaceURI||null}function C(a){return 1===a.nodeType?z(a):a.nodeValue}function D(a,b,c){for(var d=[],e=a.firstChild,f=!0;e&&f;)f=c(e),f&&(b&&e.firstChild&&d.push(e.firstChild),e=e.nextSibling||d.shift())}function E(a,b,c){for(var d=a.nextSibling;d;){if(1===d.nodeType){var e=!b||B(d)===b,f=!c||A(d)===c;if(e&&f)return d}d=d.nextSibling}return null}function F(){var a=window.document.implementation;return a&&a.createDocument?a.createDocument(null,null,null):j()}function G(a,b){if(!U(b))return H(a,b);var c,d;for(c=0,d=b.length;d>c;c++)b[c]&&H(a,b[c]);return a}function H(a,b){if(S(a!==b,"xmlAppendChild() - parent and child are one and the same!"),b){if("string"==typeof b)return o(a,M(a.ownerDocument,b));2===b.nodeType?a.setAttributeNodeNS?a.setAttributeNodeNS(b):a.setAttributeNode(b):a.appendChild(b)}return a}function I(a,b,c,d){var e=a.createAttributeNS&&a.createAttributeNS(b,c)||a.createNode(2,c,b||void 0);return e.value=d||"",e}function J(a,b,c,d){var e=a.createElementNS&&a.createElementNS(nampespaceURI,c)||a.createNode(1,c,nampespaceURI||void 0);return G(e,d||[])}function K(a,b,c){return I(a,Z,n("xmlns",c),b)}function L(a,b){for(var c="<c>"+b+"</c>",d=m(c),e=d.documentElement,f=("importNode"in a?a.importNode(e,!0):e),g=a.createDocumentFragment(),h=f.firstChild;h;)g.appendChild(h),h=h.nextSibling;return g}function M(a,b){return a.createTextNode(b)}function N(a,b,c,d,e){var f,g,h="",i=e.split("/"),j=w,k=J,l=b;for(f=0,g=i.length;g>f;f++){h=i[f],"@"===h.charAt(0)&&(h=h.substring(1),j=r,k=I);var m=j(l,c,h);m||(m=k(a,c,n(d,h)),H(l,m)),l=m}return l}function O(a){var b=window.XMLSerializer;if(b){var c=new b;return c.serializeToString(a)}if(a.xml)return a.xml;throw{message:"XML serialization unsupported"}}function P(a){var b,c=a.childNodes,d=c.length;if(0===d)return"";var e=a.ownerDocument,f=e.createDocumentFragment(),g=e.createElement("c");for(f.appendChild(g),b=0;d>b;b++)g.appendChild(c[b]);var h=O(f);for(h=h.substr(3,h.length-7),b=0;d>b;b++)a.appendChild(g.childNodes[b]);return h}var Q=c("./utils.js"),R=Q.activeXObject,S=Q.djsassert,T=Q.extend,U=Q.isArray,V=Q.normalizeURI,W="http://",X=W+"www.w3.org/",Y=X+"1999/xhtml",Z=X+"2000/xmlns/",$=X+"XML/1998/namespace",_=W+"www.mozilla.org/newlayout/xml/parsererror.xml";a.http=W,a.w3org=X,a.xmlNS=$,a.xmlnsNS=Z,a.hasLeadingOrTrailingWhitespace=d,a.isXmlNSDeclaration=g,a.xmlAppendChild=H,a.xmlAppendChildren=G,a.xmlAttributeNode=r,a.xmlAttributes=p,a.xmlAttributeValue=q,a.xmlBaseURI=s,a.xmlChildElements=t,a.xmlFindElementByPath=u,a.xmlFindNodeByPath=v,a.xmlFirstChildElement=w,a.xmlFirstDescendantElement=x,a.xmlInnerText=z,a.xmlLocalName=A,a.xmlNamespaceURI=B,a.xmlNodeValue=C,a.xmlDom=F,a.xmlNewAttribute=I,a.xmlNewElement=J,a.xmlNewFragment=L,a.xmlNewNodeByPath=N,a.xmlNewNSDeclaration=K,a.xmlNewText=M,a.xmlParse=m,a.xmlQualifiedName=n,a.xmlSerialize=O,a.xmlSerializeDescendants=P,a.xmlSiblingElement=E
}},modules={},require=function(a){var b=a.substring(a.lastIndexOf("/")+1,a.length-3);if(modules[b])return modules[b].exports;if(modules[b]={exports:{}},console.log(b),"sou"===b);return datas[b].call(this,modules[b].exports,modules[b],require),modules[b].exports};window.odatajs={},init.call(this,window.odatajs,window.odatajs,require);
//# sourceMappingURL=odatajs-4.0.0.map