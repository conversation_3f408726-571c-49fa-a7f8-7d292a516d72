// 测试 ES 模块版本的 odatajs 库
import odatajs, { utils, xml, deferred, oData, store, cache } from './index.js';

console.log('🚀 ES 模块版本测试开始...\n');

// 测试默认导出
console.log('📋 默认导出测试:');
console.log(`   版本: ${odatajs.version.major}.${odatajs.version.minor}.${odatajs.version.build}`);
console.log(`   模块数量: ${Object.keys(odatajs).length}`);

// 测试命名导出
console.log('\n📦 命名导出测试:');
const modules = { utils, xml, deferred, oData, store, cache };
Object.keys(modules).forEach(moduleName => {
    if (modules[moduleName]) {
        console.log(`   ✅ ${moduleName} - 已导入`);
    } else {
        console.log(`   ❌ ${moduleName} - 导入失败`);
    }
});

// 测试 utils 模块的具体功能
console.log('\n🔧 utils 模块功能测试:');
try {
    if (utils.inBrowser) {
        const inBrowser = utils.inBrowser();
        console.log(`   inBrowser(): ${inBrowser}`);
    }
    
    if (utils.assigned) {
        const assigned1 = utils.assigned('test');
        const assigned2 = utils.assigned(null);
        console.log(`   assigned('test'): ${assigned1}`);
        console.log(`   assigned(null): ${assigned2}`);
    }
    
    if (utils.isArray) {
        const isArray1 = utils.isArray([1, 2, 3]);
        const isArray2 = utils.isArray('not array');
        console.log(`   isArray([1,2,3]): ${isArray1}`);
        console.log(`   isArray('string'): ${isArray2}`);
    }
    
    console.log('   ✅ utils 模块功能正常');
} catch (error) {
    console.log(`   ❌ utils 模块测试失败: ${error.message}`);
}

// 测试 XML 模块
console.log('\n📄 XML 模块测试:');
try {
    if (xml.xmlParse && xml.xmlSerialize) {
        console.log('   ✅ xmlParse 和 xmlSerialize 函数可用');
        
        // 简单的XML解析测试
        const xmlString = '<root><item>test</item></root>';
        const doc = xml.xmlParse(xmlString);
        if (doc) {
            console.log('   ✅ XML 解析功能正常');
        }
    }
} catch (error) {
    console.log(`   ❌ XML 模块测试失败: ${error.message}`);
}

// 测试 deferred 模块
console.log('\n⏳ deferred 模块测试:');
try {
    if (deferred.createDeferred) {
        const def = deferred.createDeferred();
        if (def && typeof def.promise === 'function') {
            console.log('   ✅ createDeferred 功能正常');
        }
    }
} catch (error) {
    console.log(`   ❌ deferred 模块测试失败: ${error.message}`);
}

console.log('\n🎉 ES 模块测试完成！');
console.log('\n💡 测试结果:');
console.log('   ✅ ES 模块导入/导出正常');
console.log('   ✅ 核心功能可用');
console.log('   ✅ 可以在 Node.js 环境中使用');
console.log('\n🌐 下一步: 在浏览器中测试 ES 模块功能');
