// 批量转换 utils.js 中剩余的函数为 export 形式
const fs = require('fs');

// 读取文件
let content = fs.readFileSync('./lib/utils.js', 'utf8');

// 需要转换的函数列表（剩余的）
const functionsToConvert = [
    'djsassert',
    'extend', 
    'find',
    'isArray',
    'isDate',
    'isObject',
    'parseInt10',
    'renameProperty',
    'throwErrorCallback',
    'trimString',
    'undefinedDefault',
    'getURIInfo',
    'getURIFromInfo',
    'normalizeURICase',
    'normalizeURI',
    'mergeUriPathWithBase',
    'removeDotsFromPath',
    'convertByteArrayToHexString',
    'decodeBase64',
    'getBase64IndexValue',
    'addBase64Padding',
    'getJsonValueArraryLength',
    'sliceJsonValueArray',
    'concatJsonValueArray',
    'endsWith',
    'startsWith',
    'getFormatKind'
];

// 转换函数定义
functionsToConvert.forEach(funcName => {
    // 匹配 function funcName( 的模式
    const functionPattern = new RegExp(`^function\\s+${funcName}\\s*\\(`, 'gm');
    content = content.replace(functionPattern, `export function ${funcName}(`);
    
    // 匹配 var funcName = function( 的模式  
    const varPattern = new RegExp(`^var\\s+${funcName}\\s*=\\s*function\\s*\\(`, 'gm');
    content = content.replace(varPattern, `export const ${funcName} = function(`);
});

// 移除末尾的 exports 语句
const exportsPattern = /^exports\.\w+\s*=\s*\w+;$/gm;
content = content.replace(exportsPattern, '');

// 清理多余的空行
content = content.replace(/\n\s*\n\s*\n/g, '\n\n');

// 写回文件
fs.writeFileSync('./lib/utils.js', content);

console.log('✅ utils.js 转换完成！');
console.log('已转换的函数:', functionsToConvert.length);
console.log('已移除 exports 语句');
