{"name": "odatajs", "version": "4.0.1", "title": "Olingo OData Client for JavaScript", "description": "the Olingo OData Client for JavaScript library is a new cross-browser JavaScript library that enables data-centric web applications by leveraging modern protocols such as JSON and OData and HTML5-enabled browser features. It's designed to be small, fast and easy to use.", "homepage": "http://olingo.apache.org", "main": "index.js", "browser": "index-browser.js", "repository": {"type": "git", "url": "http://git-wip-us.apache.org/repos/asf/olingo-odata4-js.git"}, "engines": {"node": ">= 14.0.0"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>-Morris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "scripts": {"test": "node test-library.js", "start": "node index.js", "build": "echo 'Build script placeholder'", "serve": "python -m http.server 8080 || python3 -m http.server 8080"}, "dependencies": {"xmldom": "^0.6.0"}}