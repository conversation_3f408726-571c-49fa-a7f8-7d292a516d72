{"name": "odatajs", "version": "4.0.1", "postfix": "", "releaseCandidate": "", "title": "Olingo OData Client for JavaScript", "description": "the Olingo OData Client for JavaScript library is a new cross-browser JavaScript library that enables data-centric web applications by leveraging modern protocols such as JSON and OData and HTML5-enabled browser features. It's designed to be small, fast and easy to use.", "homepage": "http://olingo.apache.org", "main": "index-node.js", "main-browser": "index.js", "repository": {"type": "git", "url": "http://git-wip-us.apache.org/repos/asf/olingo-odata4-js.git"}, "engines": {"node": ">= 0.12.0"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>-Morris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "scripts": {"preinstall": "npm --prefix ./grunt-config/custom-tasks/rat install"}, "devDependencies": {"chai": "2.0.0", "grunt": "0.4.5", "grunt-connect-proxy": "0.1.10", "grunt-contrib-clean": "0.6.0", "grunt-contrib-compress": "0.10.0", "grunt-contrib-copy": "0.5.0", "grunt-contrib-uglify": "0.4.0", "grunt-curl": "2.0.2", "grunt-jsdoc": "0.5.6", "grunt-nuget": "0.1.3", "mocha": "2.1.0", "xmldom": "0.1.19"}}