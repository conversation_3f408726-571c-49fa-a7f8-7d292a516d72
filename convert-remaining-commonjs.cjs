// 转换剩余的 CommonJS 语法到 ES 模块
const fs = require('fs');

const jsFiles = [
    './lib/utils.js',
    './lib/xml.js',
    './lib/deferred.js',
    './lib/cache.js',
    './lib/cache/source.js',
    './lib/odata.js',
    './lib/odata/batch.js',
    './lib/odata/handler.js',
    './lib/odata/json.js',
    './lib/odata/metadata.js',
    './lib/odata/net.js',
    './lib/odata/net-browser.js',
    './lib/odata/odatautils.js',
    './lib/store.js',
    './lib/store/dom.js',
    './lib/store/indexeddb.js',
    './lib/store/memory.js',
    './index.js',
    './index-browser.js'
];

function convertCommonJS(filePath) {
    console.log(`转换文件: ${filePath}`);
    
    if (!fs.existsSync(filePath)) {
        console.log(`  ⚠️  文件不存在: ${filePath}`);
        return;
    }
    
    let content = fs.readFileSync(filePath, 'utf8');
    let changeCount = 0;
    
    // 1. 转换 require 语句
    content = content.replace(/var\s+(\w+)\s*=\s*require\(['"]([^'"]+)['"]\);?/g, (match, varName, modulePath) => {
        if (modulePath.startsWith('./') || modulePath.startsWith('../')) {
            if (!modulePath.endsWith('.js')) {
                modulePath += '.js';
            }
        }
        changeCount++;
        return `import * as ${varName} from '${modulePath}';`;
    });
    
    content = content.replace(/const\s+(\w+)\s*=\s*require\(['"]([^'"]+)['"]\);?/g, (match, varName, modulePath) => {
        if (modulePath.startsWith('./') || modulePath.startsWith('../')) {
            if (!modulePath.endsWith('.js')) {
                modulePath += '.js';
            }
        }
        changeCount++;
        return `import * as ${varName} from '${modulePath}';`;
    });
    
    // 2. 转换 exports.xxx = 语句
    content = content.replace(/exports\.(\w+)\s*=\s*([^;]+);?/g, (match, exportName, value) => {
        changeCount++;
        return `export const ${exportName} = ${value};`;
    });
    
    // 3. 转换 module.exports = 语句
    content = content.replace(/module\.exports\s*=\s*([^;]+);?/g, (match, value) => {
        changeCount++;
        return `export default ${value};`;
    });
    
    // 4. 处理 exports 对象的引用
    content = content.replace(/\bexports\b(?!\s*[=.])/g, (match) => {
        // 这种情况比较复杂，暂时跳过
        return match;
    });
    
    if (changeCount > 0) {
        fs.writeFileSync(filePath, content);
        console.log(`  ✅ 转换了 ${changeCount} 个 CommonJS 语法`);
    } else {
        console.log(`  ℹ️  没有发现需要转换的 CommonJS 语法`);
    }
}

// 转换所有文件
console.log('开始转换剩余的 CommonJS 语法...');
jsFiles.forEach(convertCommonJS);
console.log('\n🎉 CommonJS 转换完成！');
