{"version": 3, "file": "odatajs-4.0.0.min.js", "sources": ["odatajs-4.0.0.js"], "names": ["init", "exports", "module", "require", "version", "major", "minor", "build", "deferred", "utils", "xml", "oData", "store", "cache", "datas", "appendPage", "operation", "page", "intersection", "intersectRanges", "start", "end", "i", "c", "getJsonValueArrary<PERSON><PERSON>th", "d", "concatJsonValueArray", "sliceJsonValueArray", "x", "y", "result", "xLast", "yLast", "resultIndex", "resultLast", "checkZeroGreater", "val", "name", "undefined", "message", "isNaN", "isFinite", "checkUndefinedGreaterThanZero", "checkUndefinedOrNumber", "removeFromArray", "arr", "item", "len", "length", "splice", "estimateSize", "object", "size", "type", "snapToPageBoundaries", "lowIndex", "highIndex", "pageSize", "Math", "floor", "ceil", "DataCacheOperation", "stateMachine", "promise", "isCancelable", "index", "count", "data", "pending", "stateData", "cacheState", "that", "this", "p", "s", "OPERATION_STATE_START", "canceled", "oncomplete", "cancel", "state", "OPERATION_STATE_ERROR", "OPERATION_STATE_END", "OPERATION_STATE_CANCEL", "transition", "complete", "d<PERSON><PERSON><PERSON><PERSON>", "error", "err", "run", "wait", "OPERATION_STATE_WAIT", "operationStateMachine", "opTargetState", "CACHE_STATE_INIT", "fireCanceled", "fireRejected", "fireResolved", "handled", "DataCache", "options", "cacheFailure", "stats", "counts", "netReads", "prefetches", "cacheReads", "clearOperations", "readOperations", "prefetchOperations", "actualCacheSize", "allDataLocal", "cacheSize", "undefined<PERSON><PERSON><PERSON>", "collectionCount", "highestSavedPage", "highestSavedPageSize", "overflowed", "prefetchSize", "pendingOperations", "source", "cacheSource", "ODataCacheSource", "storeReq", "createStore", "mechanism", "onidle", "idle", "createDeferred", "delay", "resolve", "request", "reject", "extend", "abort", "clear", "op", "destroyStateMachine", "queueAndStart", "filterForward", "predicate", "filter", "filterBack", "readRange", "readStateMachine", "ToObservable", "toObservable", "inBrowser", "window", "Rx", "Observable", "obs", "disposed", "<PERSON><PERSON><PERSON><PERSON>", "onError", "success<PERSON>allback", "value", "onNext", "onCompleted", "then", "Dispose", "dispose", "cacheFailureCallback", "changeState", "newState", "operations", "concat", "clearStore", "CACHE_STATE_DESTROY", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "close", "dequeueOperation", "removed", "CACHE_STATE_IDLE", "fetchPage", "read", "backwards", "parseInt10", "max", "returnData", "pendingReadRange", "readMore", "readIndex", "readCount", "dataIndex", "element", "unshift", "push", "nextIndex", "initialPage", "initialIndex", "initialCount", "fireOnIdle", "prefetch", "CACHE_STATE_READ", "prefetchStateMachine", "queue", "readPage", "key", "storeFailureCallback", "contains", "contained", "_", "savePage", "pageBytes", "addOrUpdate", "updateSettings", "saveSettings", "success", "settings", "sourceId", "identifier", "pageCount", "pageIndex", "cancelStateMachine", "DESTROY_STATE_CLEAR", "CACHE_STATE_PREFETCH", "READ_STATE_LOCAL", "READ_STATE_DONE", "min", "readSaveStateMachine", "CACHE_STATE_WRITE", "range", "isPrefetch", "found", "READ_STATE_SOURCE", "READ_STATE_SAVE", "saved", "assigned", "settingsVersion", "indexOf", "createDataCache", "isArray", "normalizeURI", "prototype", "reason", "appendQueryOption", "uri", "queryOption", "separator", "appendSegment", "segment", "queryPortion", "substr", "buildODataRequest", "method", "requestUri", "user", "password", "enableJsonpCallback", "callbackParameterName", "formatQueryString", "queryForData", "queryForDataInternal", "currentRequest", "odataRequest", "newData", "nextLink", "substring", "property", "httpClient", "metadata", "normalizeURICase", "encodeURI", "decodeURI", "toString", "queryOptions", "forwardCall", "thisValue", "returnValue", "apply", "arguments", "_arguments", "_done", "_fail", "_resolved", "_rejected", "j<PERSON><PERSON><PERSON>", "Deferred", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "odata", "dispatchHandler", "handler<PERSON><PERSON><PERSON>", "requestOrResponse", "context", "handlers", "odataUtils", "odata<PERSON><PERSON><PERSON>", "handler", "odataMetadata", "odataNet", "net", "odataJson", "json", "batch", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "defined", "MAX_DATA_SERVICE_VERSION", "invokeRequest", "metadataParser", "prepareRequest", "jsonHandler", "<PERSON><PERSON><PERSON><PERSON>", "defaultSuccess", "alert", "JSON", "stringify", "defaultError", "defaultHandler", "response", "body", "headers", "write", "maxDataServiceVersion", "accept", "defaultMetadata", "urlOrRequest", "String", "defaultHttpClient", "recognizeDates", "bIsSuccessHandlerError", "parseMetadata", "csdlMetadataDocument", "<PERSON><PERSON><PERSON><PERSON>", "part<PERSON>andler", "metadataHandler", "hex16", "random", "createBoundary", "prefix", "currentBoundary", "boundaries", "<PERSON><PERSON><PERSON><PERSON>", "text", "boundary", "contentType", "properties", "__batchResponses", "readBatch", "handlerContext", "batchSerializer", "cType", "batchMediaType", "mediaType", "writeBatch", "delimiter", "readTo", "readLine", "responses", "partEnd", "position", "changeResponses", "partHeaders", "readHeaders", "partContentType", "e", "readResponse", "__changeResponses", "pop", "statusCode", "parts", "line", "pos", "responseHeaderRegex", "exec", "normalizeHeaders", "statusText", "match", "responseStatusRegex", "str", "isBatch", "batchBoundary", "batchParts", "__batchRequests", "writeBatchPartDelimiter", "writeBatchPart", "contentTypeProperties", "part", "nested", "changeSet", "__changeRequests", "changeSetBoundary", "partContext", "writeRequest", "trimString", "contentTypeParts", "split", "contentTypeParams", "contentTypeToString", "createReadWriteContext", "dataServiceVersion", "rwContext", "fixRequestHeader", "fixDataServiceVersionHeader", "dsv", "maxVersion", "getRequestOrResponseHeader", "getContentType", "getDataServiceVersion", "matches", "versionRE", "handlerAccepts", "handlerRead", "parse<PERSON><PERSON>back", "readContext", "handlerWrite", "serializeCallback", "writeContext", "textParse", "textSerialize", "oDataUtils", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "metadataContentType", "model", "parse", "payloadFormat", "getFormatKind", "addMinimalMetadataToJsonPayload", "addFullMetadataToJsonPayload", "jsonSerializer", "jsonContentType", "newdata", "formatJsonRequestPayload", "jsonReplacer", "isPrimitive", "newArrayData", "isJsonSerializableProperty", "attribute", "jsonSerializableMetadata", "jsonMakePayloadInfo", "kind", "isObject", "hasOwnProperty", "isGeographyEdmType", "isGeometryEdmType", "convertDatesNoEdm", "typeFromObject", "addType", "checkProperties", "objectInfoType", "baseURI", "curType", "propertyValue", "lookupProperty", "baseType", "lookupEntityType", "isCollectionType", "addTypeColNoEdm", "addTypeNoEdm", "addMetadataToJsonMinimalPayloadComplex", "convertDates", "contextUrlAnnotation", "payloadInfo", "createPayloadInfo", "detectedPayloadKind", "PAYLOADTYPE_VALUE", "addMetadataToJsonMinimalPayloadEntity", "typeName", "PAYLOADTYPE_FEED", "addMetadataToJsonMinimalPayloadFeed", "PAYLOADTYPE_ENTRY", "PAYLOADTYPE_COLLECTION", "addMetadataToJsonMinimalPayloadCollection", "PAYLOADTYPE_PROPERTY", "PAYLOADTYPE_SVCDOC", "PAYLOADTYPE_LINKS", "feedInfo", "entry", "entries", "items", "entryInfo", "contentTypeOdata", "objectInfo", "keyType", "lastIdSegment", "jsonGetEntryKey", "lastIndexOf", "propertyType", "lookupComplexType", "collectionInfo", "fullName", "propertyName", "parseDate", "parseDateTimeOffset", "parseDuration", "parseTime", "formatLiteral", "formatRawLiteral", "encodeURIComponent", "replace", "convertByteArrayToHexString", "minutesToOffset", "minutes", "sign", "hours", "formatNumberWidth", "parseJsonDateString", "jsonDateRE", "Date", "mins", "current", "getUTCMinutes", "setUTCMinutes", "__edmType", "__offset", "valueOf", "parseContextUriFragment", "fragments", "ret", "isNullProperty", "PAYLOADTYPE_ENTITY_REF_LINKS", "PAYLOADTYPE_ENTITY_REF_LINK", "fragmentParts", "fragment", "rCount", "char<PERSON>t", "inPharenthesis", "startsWith", "projection", "jsonIsPrimitiveType", "container", "lookupDefaultEntityContainer", "entitySet", "lookupEntitySet", "entityType", "singleton", "lookupSingleton", "endsWith", "tmp12", "deltaKind", "DELTATYPE_FEED", "DELTATYPE_DELETED_ENTRY", "DELTATYPE_LINK", "DELTATYPE_DELETED_LINK", "metadataUri", "fragmentStart", "entityModel", "entityInstanceKey", "entityKeys", "propertyRef", "first", "isPrimitiveEdmType", "oDataHandler", "odataNs", "odataAnnotationPrefix", "formatDuration", "formatDateTimeOffset", "getCanonicalTimezone", "isComplex", "jsonMediaType", "schemaElement", "attributes", "elements", "ns", "scriptCase", "firstTwo", "toUpperCase", "toLowerCase", "getChildSchema", "parentSchema", "<PERSON><PERSON><PERSON>", "elementName", "multipleElements", "isEdmNamespace", "nsURI", "edmNs1", "parseConceptualModelElement", "localName", "xmlLocalName", "xmlNamespaceURI", "elementSchema", "schema", "xmlAttributes", "xmlnsNS", "schemaName", "odataMetaXmlNs", "xmlChildElements", "child", "childSchema", "xmlInnerText", "doc", "xmlParse", "root", "xmlFirstChildElement", "oDSxml", "xmlNS", "ado", "http", "adoDs", "edmxNs", "xmlMediaType", "Action", "ActionImport", "Annotation", "AnnotationPath", "Annotations", "Apply", "And", "Or", "Not", "Eq", "Ne", "Gt", "Ge", "Lt", "Le", "Binary", "Bool", "Cast", "Collection", "ComplexType", "DateTimeOffset", "Decimal", "Duration", "EntityContainer", "EntitySet", "EntityType", "EnumMember", "EnumType", "Float", "Function", "FunctionImport", "Guid", "If", "Int", "IsOf", "Key", "LabeledElement", "LabeledElementReference", "Member", "NavigationProperty", "NavigationPropertyBinding", "NavigationPropertyPath", "<PERSON><PERSON>", "OnDelete", "Path", "Parameter", "Property", "PropertyPath", "PropertyRef", "PropertyValue", "Record", "ReferentialConstraint", "ReturnType", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Term", "TimeOfDay", "TypeDefinition", "UrlRef", "Edmx", "DataServices", "Reference", "Include", "IncludeAnnotations", "canUseJSONP", "createIFrame", "url", "iframe", "document", "createElement", "style", "display", "attributeEncodedUrl", "html", "getElementsByTagName", "append<PERSON><PERSON><PERSON>", "writeHtmlToIFrame", "createXmlHttpRequest", "XMLHttpRequest", "exception", "ActiveXObject", "isAbsoluteUrl", "isLocalUrl", "location", "locationDomain", "protocol", "host", "removeCallback", "tick", "ticks", "removeIFrame", "parentNode", "<PERSON><PERSON><PERSON><PERSON>", "readResponseHeaders", "xhr", "responseHeaders", "getAllResponseHeaders", "header", "frameDocument", "contentWindow", "contentDocument", "open", "done", "handleTimeout", "onreadystatechange", "readyState", "status", "responseText", "setRequestHeader", "timeoutMS", "timeout", "ontimeout", "send", "timeoutId", "tickText", "succeeded", "clearTimeout", "Content-Type", "OData-Version", "setTimeout", "queryStringParams", "qIndex", "odatautils", "forEachSchema", "callback", "dataServices", "formatMilliseconds", "ms", "formatDateTimeOffsetJSON", "getTime", "hasOffset", "isDateTimeOffset", "offset", "timezone", "parseTimezone", "getUTCHours", "h", "m", "setUTCHours", "year", "getUTCFullYear", "month", "getUTCMonth", "getUTCMilliseconds", "__ns", "getUTCDate", "getUTCSeconds", "days", "seconds", "width", "append", "getCollectionType", "isCollection", "colData", "results", "collectionTypeRE", "test", "isDate", "is<PERSON><PERSON><PERSON><PERSON>", "__metadata", "__deferred", "isEntry", "isFeed", "feedData", "geographyEdmTypes", "geographyTypes", "geometryEdmTypes", "geometryTypes", "isNamedStream", "mediaResource", "__mediaresource", "media_src", "primitiveEdmTypes", "navigationPropertyKind", "propertyModel", "relationship", "find", "lookupInMetadata", "lookupInSchema", "entitySets", "singletons", "entityContainer", "lookupEntityContainer", "lookupFunctionImport", "functionImports", "functionImport", "lookupNavigationPropertyType", "navigationProperty", "rel", "association", "nameOnly", "removeNamespace", "namespace", "associations", "role", "toRole", "lookupNavigationPropertyEntitySet", "sourceEntitySetName", "associationSet", "containers", "associationSets", "j", "getEntitySetInfo", "entitySetName", "info", "containerName", "left", "right", "leftParts", "rightParts", "leftVersion", "rightVersion", "lowerName", "normalName", "normalHeaders", "parseBool", "parseDateTimeMaybeOffset", "withOffset", "nullOnError", "parseDateTimeRE", "direction", "setUTCFullYear", "parseTimeOfDay", "parseTimeOfDayRE", "duration", "parseTimeRE", "years", "months", "parseFloat", "offsetHours", "offsetMinutes", "Accept", "async", "traverseInternal", "owner", "traverse", "dataItemTypeName", "valueTypeName", "EDM", "EDM_BOOLEAN", "EDM_BYTE", "EDM_SBYTE", "EDM_INT16", "EDM_INT32", "EDM_INT64", "EDM_SINGLE", "EDM_DOUBLE", "EDM_DECIMAL", "EDM_STRING", "EDM_BINARY", "EDM_DATE", "EDM_DATETIMEOFFSET", "EDM_DURATION", "EDM_GUID", "EDM_TIMEOFDAY", "GEOGRAPHY", "EDM_GEOGRAPHY", "EDM_GEOGRAPHY_POINT", "EDM_GEOGRAPHY_LINESTRING", "EDM_GEOGRAPHY_POLYGON", "EDM_GEOGRAPHY_COLLECTION", "EDM_GEOGRAPHY_MULTIPOLYGON", "EDM_GEOGRAPHY_MULTILINESTRING", "EDM_GEOGRAPHY_MULTIPOINT", "GEOGRAPHY_POINT", "GEOGRAPHY_LINESTRING", "GEOGRAPHY_POLYGON", "GEOGRAPHY_COLLECTION", "GEOGRAPHY_MULTIPOLYGON", "GEOGRAPHY_MULTILINESTRING", "GEOGRAPHY_MULTIPOINT", "GEOMETRY", "EDM_GEOMETRY", "EDM_GEOMETRY_POINT", "EDM_GEOMETRY_LINESTRING", "EDM_GEOMETRY_POLYGON", "EDM_GEOMETRY_COLLECTION", "EDM_GEOMETRY_MULTIPOLYGON", "EDM_GEOMETRY_MULTILINESTRING", "EDM_GEOMETRY_MULTIPOINT", "GEOMETRY_POINT", "GEOMETRY_LINESTRING", "GEOMETRY_POLYGON", "GEOMETRY_COLLECTION", "GEOMETRY_MULTIPOLYGON", "GEOMETRY_MULTILINESTRING", "GEOMETRY_MULTIPOINT", "GEOJSON_POINT", "GEOJSON_LINESTRING", "GEOJSON_POLYGON", "GEOJSON_MULTIPOINT", "GEOJSON_MULTILINESTRING", "GEOJSON_MULTIPOLYGON", "GEOJSON_GEOMETRYCOLLECTION", "content-type", "content-encoding", "content-length", "odata-version", "accept-charset", "if-match", "if-none-match", "odata-isolation", "odata-maxversion", "prefer", "content-id", "content-transfer-encoding", "etag", "odata-entityid", "preference-applied", "retry-after", "defaultStoreMechanism", "DomStore", "isSupported", "factory", "mechanisms", "create", "IndexedDBStore", "MemoryStore", "indexeddb", "dom", "memory", "domStoreDateToJSON", "newValue", "v", "t", "domStoreJSONToDate", "qualifyDomStoreKey", "unqualifyDomStoreKey", "localStorage", "add", "Array", "<PERSON><PERSON><PERSON>", "oldDateToJSON", "toJSON", "storedValue", "setItem", "code", "number", "removeItem", "getItem", "getAllKeys", "remove", "update", "getError", "errorFunc", "Object", "call", "err<PERSON><PERSON>", "err<PERSON><PERSON><PERSON>", "target", "ex", "openStoreDb", "storeName", "dbN<PERSON>", "indexedDB", "onblocked", "onerror", "onupgradeneeded", "db", "objectStoreNames", "createObjectStore", "onsuccess", "event", "versionRequest", "setVersion", "transaction", "onversionchange", "openTransaction", "mode", "storeDb", "mozIndexedDB", "webkitIndexedDB", "msIndexedDB", "IDBKeyRange", "webkitIDBKeyRange", "IDBTransaction", "webkitIDBTransaction", "IDBT_READ_ONLY", "READ_ONLY", "IDBT_READ_WRITE", "READ_WRITE", "keys", "values", "<PERSON>ab<PERSON>", "objectStore", "record", "put", "get", "openCursor", "cursor", "only", "pair", "validateKeyInput", "messageString", "holes", "get<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "a", "b", "args", "slice", "condition", "parseInt", "renameProperty", "obj", "old<PERSON>ame", "newName", "trim", "defaultValue", "getURIInfo", "isAbsolute", "uriRegEx", "uriPartNames", "scheme", "getURIFromInfo", "uriInfo", "authority", "path", "query", "uriAuthorityRegEx", "pctEncodingRegEx", "base", "baseInfo", "normInfo", "mergeUriPathWithBase", "removeDotsFromPath", "<PERSON><PERSON><PERSON><PERSON>", "basePath", "atob", "decodeBase64", "binaryStr", "charCodeAt", "hexValue", "hex<PERSON><PERSON><PERSON>", "binaryString", "base65IndexValue", "getBase64IndexValue", "binaryValue", "addBase64Padding", "byteArray", "numberOfBytes", "intValue", "character", "asciiCode", "asciiOfA", "differenceBetweenZanda", "concatData", "input", "search", "format", "defaultFormatKind", "formatKind", "normalizedFormat", "activeXObject", "progId", "hasLeadingOrTrailingWhitespace", "re", "isWhitespace", "ws", "isWhitespacePreserveContext", "dom<PERSON>lement", "nodeType", "xmlAttributeValue", "isXmlNSDeclaration", "domAttribute", "nodeName", "safeSetProperty", "setProperty", "msXmlDom3", "msxml3", "msXmlDom", "msxml", "msXmlParse", "loadXML", "parseError", "errorCode", "xmlThrowParserError", "srcText", "exceptionOrReason", "errorXmlText", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "parseFromString", "documentElement", "namespaceURI", "mozillaParserErroNS", "srcTextElement", "xmlNodeValue", "xhtmlNS", "xmlFirstDescendantElement", "siblings", "<PERSON><PERSON><PERSON><PERSON>", "nextS<PERSON>ling", "shift", "xmlQualifiedName", "xmlAppendText", "domNode", "textNode", "attr", "xmlAttributeNode", "xmlNewAttribute", "ownerDocument", "xmlAppendChild", "onAttributeCallback", "getNamedItemNS", "getQualifiedItem", "xmlBaseURI", "onElementCallback", "xmlTraverse", "xmlFindElementByPath", "xmlFindNodeByPath", "lastSegmentStart", "nodePath", "parentPath", "node", "xmlFirstElementMaybeRecursive", "getElementsByTagNameNS", "recursive", "firstElement", "isExpectedNamespace", "isExpectedNodeName", "xmlElement", "whitespacePreserveContext", "whitespaceAlreadyRemoved", "preserveWhiteSpace", "shouldInclude", "baseName", "nodeValue", "onChildCallback", "subtrees", "proceed", "xmlSiblingElement", "sibling", "xmlDom", "implementation", "createDocument", "xmlAppendChildren", "parent", "children", "xmlNewText", "setAttributeNodeNS", "setAttributeNode", "qualifiedName", "createAttributeNS", "createNode", "xmlNewElement", "createElementNS", "nampespaceURI", "xmlNewNSDeclaration", "xmlNewFragment", "tempDom", "tempRoot", "imported", "importNode", "createDocumentFragment", "importedChild", "createTextNode", "xmlNewNodeByPath", "xmlFindNode", "xmlNewNode", "xmlNode", "childNode", "xmlSerialize", "xmlSerializer", "XMLSerializer", "serializer", "serializeToString", "xmlSerializeDescendants", "childNodes", "fragmentRoot", "w3org", "modules", "console", "log", "odatajs"], "mappings": ";;;;;;;;;;;;;;;;;;;AAkBA,GAAIA,MAAO,SAASC,EAASC,EAAQC,GAIrCF,EAAQG,SAAYC,MAAO,EAAGC,MAAO,EAAGC,MAAO,GAG/CN,EAAQO,SAAWL,EAAQ,qBAC3BF,EAAQQ,MAAQN,EAAQ,kBAGxBF,EAAQS,IAAMP,EAAQ,gBAGtBF,EAAQU,MAAQR,EAAQ,kBACxBF,EAAQW,MAAQT,EAAQ,kBACxBF,EAAQY,MAAQV,EAAQ,mBAOpBW,OAASD,MAAU,SAASZ,EAASC,EAAQC,GACjD,YAkCA,SAASY,GAAWC,EAAWC,GAE3B,GAAIC,GAAeC,EAAgBH,EAAWC,GAC1CG,EAAQ,EACRC,EAAM,CACNH,KACAE,EAAQF,EAAaI,EAAIL,EAAKK,EAC9BD,EAAMD,GAASJ,EAAUO,EAAIC,EAAyBR,EAAUS,KAGpET,EAAUS,EAAIC,EAAqBV,EAAUS,EAAGE,EAAoBV,EAAKQ,EAAGL,EAAOC,IAQvF,QAASF,GAAgBS,EAAGC,GAExB,GAIIC,GAJAC,EAAQH,EAAEN,EAAIM,EAAEL,EAChBS,EAAQH,EAAEP,EAAIO,EAAEN,EAChBU,EAAeL,EAAEN,EAAIO,EAAEP,EAAKM,EAAEN,EAAIO,EAAEP,EACpCY,EAAsBF,EAARD,EAAiBA,EAAQC,CAM3C,OAJIE,IAAcD,IACdH,GAAWR,EAAGW,EAAaV,EAAGW,EAAaD,IAGxCH,EAQX,QAASK,GAAiBC,EAAKC,GAE3B,GAAYC,SAARF,GAAoC,gBAARA,GAC5B,MAAQG,QAAS,IAAMF,EAAO,sBAGlC,IAAIG,MAAMJ,IAAc,EAANA,IAAYK,SAASL,GACnC,MAAQG,QAAS,IAAMF,EAAO,4CAStC,QAASK,GAA8BN,EAAKC,GAExC,GAAYC,SAARF,EAAmB,CACnB,GAAmB,gBAARA,GACP,MAAQG,QAAS,IAAMF,EAAO,sBAGlC,IAAIG,MAAMJ,IAAe,GAAPA,IAAaK,SAASL,GACpC,MAAQG,QAAS,IAAMF,EAAO,iCAU1C,QAASM,GAAuBP,EAAKC,GACjC,GAAYC,SAARF,IAAqC,gBAARA,IAAoBI,MAAMJ,KAASK,SAASL,IACzE,MAAQG,QAAS,IAAMF,EAAO,uBAStC,QAASO,GAAgBC,EAAKC,GAE1B,GAAIxB,GAAGyB,CACP,KAAKzB,EAAI,EAAGyB,EAAMF,EAAIG,OAAYD,EAAJzB,EAASA,IACnC,GAAIuB,EAAIvB,KAAOwB,EAEX,MADAD,GAAII,OAAO3B,EAAG,IACP,CAIf,QAAO,EAQX,QAAS4B,GAAaC,GAClB,GAAIC,GAAO,EACPC,QAAcF,EAElB,IAAa,WAATE,GAAqBF,EACrB,IAAK,GAAId,KAAQc,GACbC,GAAsB,EAAdf,EAAKW,OAAaE,EAAaC,EAAOd,QAGlDe,GADgB,WAATC,EACgB,EAAhBF,EAAOH,OAEP,CAEX,OAAOI,GASX,QAASE,GAAqBC,EAAUC,EAAWC,GAG/C,MAFAF,GAAWG,KAAKC,MAAMJ,EAAWE,GAAYA,EAC7CD,EAAYE,KAAKE,MAAMJ,EAAY,GAAKC,GAAYA,GAC3CnC,EAAGiC,EAAUhC,EAAGiC,EAAYD,GAwCzC,QAASM,GAAmBC,EAAcC,EAASC,EAAcC,EAAOC,EAAOC,EAAMC,GAElF,GAAIC,GACCC,EACAC,EAAOC,IAEXD,GAAKE,EAAIV,EACTQ,EAAKjD,EAAI2C,EACTM,EAAKhD,EAAI2C,EACTK,EAAK9C,EAAI0C,EACTI,EAAKG,EAAIC,EAETJ,EAAKK,UAAW,EAChBL,EAAKH,QAAUA,EACfG,EAAKM,WAAa,KAMlBN,EAAKO,OAAS,WAEV,GAAKd,EAAL,CAIA,GAAIe,GAAQR,EAAKG,CACbK,KAAUC,GAAyBD,IAAUE,GAAuBF,IAAUG,IAC9EX,EAAKK,UAAW,EAChBL,EAAKY,WAAWD,EAAwBb,MAOhDE,EAAKa,SAAW,WAEZC,EAAUd,EAAKG,IAAMO,EAAqB,gEAAiEV,GAC3GA,EAAKY,WAAWF,EAAqBZ,IAMzCE,EAAKe,MAAQ,SAAUC,GACdhB,EAAKK,WACNS,EAAUd,EAAKG,IAAMO,EAAqB,6DAA8DV,GACxGc,EAAUd,EAAKG,IAAMM,EAAuB,+DAAgET,GAC5GA,EAAKY,WAAWH,EAAuBO,KAQ/ChB,EAAKiB,IAAM,SAAUT,GAEjBT,EAAaS,EACbR,EAAKY,WAAWZ,EAAKG,EAAGL,IAM5BE,EAAKkB,KAAO,SAAUtB,GAElBkB,EAAUd,EAAKG,IAAMO,EAAqB,4DAA6DV,GACvGA,EAAKY,WAAWO,EAAsBvB,GAS1C,IAAIwB,GAAwB,SAAUC,EAAetB,EAAYH,GAE7D,OAAQyB,GACJ,IAAKjB,GAEGL,IAAeuB,GACf/B,EAAaS,EAAMqB,EAAetB,EAAYH,EAElD,MAEJ,KAAKuB,GAED5B,EAAaS,EAAMqB,EAAetB,EAAYH,EAC9C,MAEJ,KAAKe,GAEDpB,EAAaS,EAAMqB,EAAetB,EAAYH,GAC9CI,EAAKuB,eACLvB,EAAKY,WAAWF,EAChB,MAEJ,KAAKD,GAEDlB,EAAaS,EAAMqB,EAAetB,EAAYH,GAC9CI,EAAKK,UAAW,EAChBL,EAAKwB,aAAa5B,GAClBI,EAAKY,WAAWF,EAChB,MAEJ,KAAKA,GAEGV,EAAKM,YACLN,EAAKM,WAAWN,GAEfA,EAAKK,UACNL,EAAKyB,eAETlC,EAAaS,EAAMqB,EAAetB,EAAYH,EAC9C,MAEJ,SAKQ,GAAI8B,GAAUnC,EAAaS,EAAMqB,EAAetB,EAAYH,EAC5DkB,GAAUY,EAAS,wBAA0BL,EAAgB,gBAAkBtB,EAAYE,OAwB3G,OANAD,GAAKY,WAAa,SAAUJ,EAAOZ,GAC/BI,EAAKG,EAAIK,EACTV,EAAYF,EACZwB,EAAsBZ,EAAOT,EAAYH,IAGtCI,EA4CX,QAAS2B,GAAUC,GAEf,GAiBIC,GAjBArB,EAAQc,EACRQ,GAAUC,OAAQ,EAAGC,SAAU,EAAGC,WAAY,EAAGC,WAAY,GAE7DC,KACAC,KACAC,KAEAC,EAAkB,EAClBC,GAAe,EACfC,EAAYC,EAAiBb,EAAQY,UAAW,SAChDE,EAAkB,EAClBC,EAAmB,EACnBC,EAAuB,EACvBC,EAA2B,IAAdL,EACbtD,EAAWuD,EAAiBb,EAAQ1C,SAAU,IAC9C4D,EAAeL,EAAiBb,EAAQkB,aAAc5D,GACtDrD,EAAU,MAGVkH,EAAoB,EAEpBC,EAASpB,EAAQoB,MACC,iBAAXA,KAEPA,EAAS,GAAIC,GAAYC,iBAAiBtB,IAE9CoB,EAAOpB,QAAUA,CAGjB,IAAIvF,GAAQ8G,EAASC,YAAYxB,EAAQ9D,KAAM8D,EAAQyB,WAEnDrD,EAAOC,IAEXD,GAAKsD,OAAS1B,EAAQ2B,KACtBvD,EAAK8B,MAAQA,EAMb9B,EAAKL,MAAQ,WAET,GAAIkC,EACA,KAAMA,EAGV,IAAI5F,GAAWuH,IACXnD,GAAW,CAEf,IAAIkC,EAKA,MAJAkB,GAAM,WACFxH,EAASyH,QAAQhB,KAGdzG,EAASuD,SAIpB,IAAImE,GAAUX,EAAOrD,MAAM,SAAUA,GACjCgE,EAAU,KACV7B,EAAMC,SACN9F,EAASyH,QAAQ/D,IAClB,SAAUqB,GACT2C,EAAU,KACV1H,EAAS2H,OAAOC,EAAO7C,GAAOX,SAAUA,MAG5C,OAAOwD,GAAO5H,EAASuD,WAKnBe,OAAQ,WAEAoD,IACAtD,GAAW,EACXsD,EAAQG,QACRH,EAAU,UAY1B3D,EAAK+D,MAAQ,WAET,GAAIlC,EACA,KAAMA,EAGV,IAA+B,IAA3BM,EAAgB1D,OAAc,CAC9B,GAAIxC,GAAWuH,IACXQ,EAAK,GAAI1E,GAAmB2E,GAAqBhI,GAAU,EAE/D,OADAiI,IAAcF,EAAI7B,GACXlG,EAASuD,UAEpB,MAAO2C,GAAgB,GAAGjC,GAW9BF,EAAKmE,cAAgB,SAAUzE,EAAOC,EAAOyE,GACzC,MAAOC,IAAO3E,EAAOC,EAAOyE,GAAW,IAW3CpE,EAAKsE,WAAa,SAAU5E,EAAOC,EAAOyE,GACtC,MAAOC,IAAO3E,EAAOC,EAAOyE,GAAW,IAY3CpE,EAAKuE,UAAY,SAAU7E,EAAOC,GAK9B,GAHA/B,EAAiB8B,EAAO,SACxB9B,EAAiB+B,EAAO,SAEpBkC,EACA,KAAMA,EAGV,IAAI5F,GAAWuH,IAGXQ,EAAK,GAAI1E,GAAmBkF,GAAkBvI,GAAU,EAAMyD,EAAOC,KAAW,EAGpF,OAFAuE,IAAcF,EAAI5B,GAEXyB,EAAO5H,EAASuD,WACnBe,OAAQ,WAIJyD,EAAGzD,aASfP,EAAKyE,aAAezE,EAAK0E,aAAe,WACpC,IAAMxI,EAAMyI,YACR,MAAQ3G,QAAS,2BAGrB,KAAK4G,OAAOC,KAAOD,OAAOC,GAAGC,WACzB,MAAQ9G,QAAS,2CAGrB,IAAI6D,EACA,KAAMA,EAIV,OAAO,IAAI+C,QAAOC,GAAGC,WAAW,SAAUC,GACtC,GAAIC,IAAW,EACXtF,EAAQ,EAERuF,EAAgB,SAAUlE,GACrBiE,GACDD,EAAIG,QAAQnE,IAIhBoE,EAAkB,SAAUvF,GAC5B,IAAKoF,EAAU,CACX,GAAIjI,GAAGyB,CACP,KAAKzB,EAAI,EAAGyB,EAAMoB,EAAKwF,MAAM3G,OAAYD,EAAJzB,EAASA,IAI1CgI,EAAIM,OAAOzF,EAAKwF,MAAMrI,GAGtB6C,GAAKwF,MAAM3G,OAASS,EAEpB6F,EAAIO,eAEJ5F,GAASR,EACTc,EAAKuE,UAAU7E,EAAOR,GAAUqG,KAAKJ,EAAiBF,KAOlE,OAFAjF,GAAKuE,UAAU7E,EAAOR,GAAUqG,KAAKJ,EAAiBF,IAE7CO,QAAS,WACdT,EAAIU,UACJT,GAAW,MAYvB,IAAIU,GAAuB,SAAU1H,GAGjC,MAAO,UAAU+C,GACbc,GAAiB7D,QAASA,EAAS+C,MAAOA,GAM1CD,EAAwC,IAA9BuB,EAAmB5D,OAAc,kCAC3C,IAAI1B,GAAGyB,CACP,KAAKzB,EAAI,EAAGyB,EAAM4D,EAAe3D,OAAYD,EAAJzB,EAASA,IAC9CqF,EAAerF,GAAGyE,aAAaK,EAEnC,KAAK9E,EAAI,EAAGyB,EAAM2D,EAAgB1D,OAAYD,EAAJzB,EAASA,IAC/CoF,EAAgBpF,GAAGyE,aAAaK,EAIpCO,GAAiBD,EAAkB,OASvCwD,GAAc,SAAUC,GAExB,GAAIA,IAAapF,EAAO,CACpBA,EAAQoF,CACR,IACI7I,GAAGyB,EADHqH,EAAa1D,EAAgB2D,OAAO1D,EAAgBC,EAExD,KAAKtF,EAAI,EAAGyB,EAAMqH,EAAWpH,OAAYD,EAAJzB,EAASA,IAC1C8I,EAAW9I,GAAGkE,IAAIT,KAS1BuF,GAAa,WACbjF,EAAUN,IAAUwF,GAAuBxF,IAAUc,EAAkB,4FAA8Fd,EAErK,IAAIvE,GAAW,GAAIgK,EAsBnB,OArBA5J,GAAM0H,MAAM,WAGRzB,EAAkB,EAClBC,GAAe,EACfG,EAAkB,EAClBC,EAAmB,EACnBC,EAAuB,EACvBC,EAA2B,IAAdL,EAKbV,GAAUC,OAAQ,EAAGC,SAAU,EAAGC,WAAY,EAAGC,WAAY,GAC7DlC,EAAK8B,MAAQA,EAEbzF,EAAM6J,QACNjK,EAASyH,WACV,SAAU1C,GACT/E,EAAS2H,OAAO5C,KAEb/E,GAQPkK,GAAmB,SAAU1J,GAE7B,GAAI2J,GAAU/H,EAAgB8D,EAAiB1F,EAC1C2J,KACDA,EAAU/H,EAAgB+D,EAAgB3F,GACrC2J,GACD/H,EAAgBgE,EAAoB5F,IAI5CsG,IACA4C,GAAYU,IAQZC,GAAY,SAAUzJ,GAEtBiE,EAAUN,IAAUwF,EAAqB,yDACzClF,EAAUN,IAAU6F,EAAkB,qDAEtC,IAAIpK,GAAW,GAAIgK,GACf5F,GAAW,EAEXsD,EAAUX,EAAOuD,KAAK1J,EAAOqC,EAAU,SAAUU,GACjD,GAAInB,GAASxB,EAAyB2C,GAClClD,GAASK,EAAGF,EAAOG,EAAGyB,EAAQvB,EAAG0C,EACrC3D,GAASyH,QAAQhH,IAClB,SAAUsE,GACT/E,EAAS2H,OAAO5C,IAGpB,OAAO6C,GAAO5H,GACVsE,OAAQ,WACAoD,IACAA,EAAQG,QACRzD,GAAW,EACXsD,EAAU,UAetBU,GAAS,SAAU3E,EAAOC,EAAOyE,EAAWoC,GAK5C,GAHA9G,EAAQ+G,EAAW/G,GACnBC,EAAQ8G,EAAW9G,GAEf1B,MAAMyB,GACN,MAAQ1B,QAAS,kCAAmC0B,MAAOA,EAE/D,IAAIzB,MAAM0B,GACN,MAAQ3B,QAAS,kCAAmC2B,MAAOA,EAG/D,IAAIkC,EACA,KAAMA,EAGVnC,GAAQP,KAAKuH,IAAIhH,EAAO,EAExB,IAAIzD,GAAWuH,IACXmD,IACJA,GAAWvB,QACX,IAAI/E,IAAW,EACXuG,EAAmB,KAEnBC,EAAW,SAAUC,EAAWC,GAC3B1G,IACGV,EAAQ,GAAKgH,EAAWvB,MAAM3G,QAAUkB,EACxC1D,EAASyH,QAAQiD,GAEjBC,EAAmB5G,EAAKuE,UAAUuC,EAAWC,GAAWxB,KAAK,SAAU3F,GAC/DA,EAAK,oBAAsB+G,EAAW,oBACtCA,EAAW,kBAAoB/G,EAAK,kBAGxC,KAAK,GAAI7C,GAAI,EAAG0B,EAASmB,EAAKwF,MAAM3G,OAAYA,EAAJ1B,IAAuB,EAAR4C,GAAagH,EAAWvB,MAAM3G,OAASkB,GAAQ5C,IAAK,CAC3G,GAAIiK,GAAYR,EAAY/H,EAAS1B,EAAI,EAAIA,EACzCwB,EAAOqB,EAAKwF,MAAM4B,EACtB,IAAI5C,EAAU7F,GAAO,CACjB,GAAI0I,IACAvH,MAAOoH,EAAYE,EACnBzI,KAAMA,EAGViI,GAAYG,EAAWvB,MAAM8B,QAAQD,GAAWN,EAAWvB,MAAM+B,KAAKF,IAK9E,IAAMT,GAAa5G,EAAKwF,MAAM3G,OAASsI,GAAeP,GAA0B,GAAbM,EAC/D7K,EAASyH,QAAQiD,OACd,CACH,GAAIS,GAAYZ,EAAYrH,KAAKuH,IAAII,EAAY5H,EAAU,GAAK4H,EAAYC,CAC5EF,GAASO,EAAWlI,KAEzB,SAAU8B,GACT/E,EAAS2H,OAAO5C,OAO5BqG,EAActI,EAAqBW,EAAOA,EAAOR,GACjDoI,EAAed,EAAYa,EAAYtK,EAAI2C,EAC3C6H,EAAef,EAAY9G,EAAQ2H,EAAYtK,EAAI,EAAIsK,EAAYtK,EAAIsK,EAAYrK,EAAI0C,CAG3F,OAFAmH,GAASS,EAAcC,GAEhB1D,EAAO5H,EAASuD,WAInBe,OAAQ,WAEAqG,GACAA,EAAiBrG,SAErBF,GAAW,MAQnBmH,GAAa,WAETxH,EAAKsD,QAAgC,IAAtBP,GACf/C,EAAKsD,UAaTmE,GAAW,SAAU5K,GAGrB,IAAI0F,GAAiC,IAAjBO,IAAsBD,IAI1C/B,EAAUN,IAAUkH,EAAkB,yEAA2ElH,GAE/E,IAA9B6B,EAAmB5D,QAAiB4D,EAAmB,IAAkC,KAA5BA,EAAmB,GAAGrF,GAAW,CAE9F,GAAIgH,GAAK,GAAI1E,GAAmBqI,GAAsB,MAAM,EAAM9K,EAAOiG,EAAc,KAAMA,EAC7FoB,IAAcF,EAAI3B,KAQtB6B,GAAgB,SAAUF,EAAI4D,GAE9B5D,EAAG1D,WAAa6F,GAChByB,EAAMT,KAAKnD,GACXjB,IACAiB,EAAG/C,IAAIT,IAQPqH,GAAW,SAAUC,GAErBhH,EAAUN,IAAUwF,EAAqB,uDAEzC,IAAI3F,IAAW,EACXpE,EAAW4H,EAAO,GAAIoC,IAItB1F,OAAQ,WACJF,GAAW,KAIfU,EAAQgH,GAAqB9L,EAAU,+BAgB3C,OAdAI,GAAM2L,SAASF,EAAK,SAAUG,GAC1B,MAAI5H,GAAJ,OAGI4H,MACA5L,GAAMkK,KAAKuB,EAAK,SAAUI,EAAGtI,GACpBS,GACDpE,EAASyH,QAAiB3F,SAAT6B,EAAoBA,IAE1CmB,OAGP9E,GAASyH,SAAQ,IAClB3C,GACI9E,GASPkM,GAAW,SAAUL,EAAKpL,GAE1BoE,EAAUN,IAAUwF,EAAqB,wDACzClF,EAAUN,IAAU6F,EAAkB,oDAEtC,IAAIhG,IAAW,EAEXpE,EAAW4H,EAAO,GAAIoC,IAItB1F,OAAQ,WACJF,GAAW,KAIfU,EAAQgH,GAAqB9L,EAAU,8BAEvCyH,EAAU,WACVzH,EAASyH,SAAQ,GAGrB,IAAIhH,EAAKM,EAAI,EAAG,CACZ,GAAIoL,GAAYzJ,EAAajC,EAC7BmG,GAAaL,GAAa,GAAiBF,EAAkB8F,EAA9B5F,EAE1BK,EAMDa,IALArH,EAAMgM,YAAYP,EAAKpL,EAAM,WACzB4L,GAAe5L,EAAM0L,GACrBG,GAAa7E,EAAS3C,IACvBA,OAKPuH,IAAe5L,EAAM,GACrB6L,GAAa7E,EAAS3C,EAE1B,OAAO9E,IAQPsM,GAAe,SAAUC,EAASzH,GAElC,GAAI0H,IACAnG,gBAAiBA,EACjBC,aAAcA,EACdC,UAAWA,EACXE,gBAAiBA,EACjBC,iBAAkBA,EAClBC,qBAAsBA,EACtB1D,SAAUA,EACVwJ,SAAU1F,EAAO2F,WACjB9M,QAASA,EAGbQ,GAAMgM,YAAY,aAAcI,EAAUD,EAASzH,IAUnDgH,GAAuB,SAAU9L,GAGjC,MAAO,YAMHA,EAASyH,SAAQ,KASrB4E,GAAiB,SAAU5L,EAAM0L,GAEjC,GAAIQ,GAAYlM,EAAKM,EACjB6L,EAAYnM,EAAKK,CAGH,KAAd6L,EACIjG,IAAqBkG,EAAY3J,IACjCwD,EAAkBC,EAAmBC,IAGzCD,EAAmBxD,KAAKuH,IAAI/D,EAAkBkG,GAC1ClG,IAAqBkG,IACrBjG,EAAuBgG,GAE3BtG,GAAmB8F,EACHlJ,EAAZ0J,IAAyBlG,IACzBA,EAAkBmG,EAAYD,IAKjCrG,GAAgBG,IAAoBC,EAAmBC,IACxDL,GAAe,IAYnBuG,GAAqB,SAAUrM,EAAW4E,EAAetB,EAAYH,GAGrE,GAAIS,GAAW5D,EAAU4D,UAAYgB,IAAkBX,CAUvD,OATIL,IACIgB,IAAkBV,GAGdf,GAAQA,EAAKW,QACbX,EAAKW,SAIVF,GAYP4D,GAAsB,SAAUxH,EAAW4E,EAAetB,GAG1D,GAAIa,GAAanE,EAAUmE,UAG3B,IAAIb,IAAeiG,EAEf,MADAL,IAAYK,IACL,CAGX,QAAQ3E,GACJ,IAAKjB,GAEDQ,EAAWmI,EACX,MAEJ,KAAKrI,GAED8G,IACA,MAEJ,KAAKuB,GAEDhD,KAAaR,KAAK,WAEd9I,EAAUoE,aAGdpE,EAAUyE,MACV,MAEJ,SACI,OAAO,EAEf,OAAO,GAiBPyG,GAAuB,SAAUlL,EAAW4E,EAAetB,EAAYH,GAIvE,IAAKkJ,GAAmBrM,EAAW4E,EAAetB,EAAYH,GAAO,CAEjE,GAAIgB,GAAanE,EAAUmE,UAG3B,IAAIb,IAAeiJ,EASf,MARIjJ,KAAeiG,EACX3E,IAAkBV,GAClBlE,EAAU8D,SAEPR,IAAesG,GAEtBV,GAAYqD,IAET,CAGX,QAAQ3H,GACJ,IAAKjB,GAEGiC,EAAmB,KAAO5F,GAC1BmE,EAAWqI,EAAkBxM,EAAUM,EAE3C,MAEJ,KAAKmM,GAID,GAAIrJ,GAAUpD,EAAUoD,OAEpBA,GAAU,IACVA,GAAWV,KAAKgK,IAAItJ,EAASD,EAAK5C,IAIlCuF,GAA4B,IAAZ1C,GAAiBD,EAAK5C,EAAIkC,GAAY2D,EACtDpG,EAAUoE,YAGVpE,EAAUoD,QAAUA,EACpBe,EAAWqI,EAAkBrJ,EAAK7C,EAAImC,GAE1C,MAEJ,SACI,MAAOkK,IAAqB3M,EAAW4E,EAAetB,EAAYH,GAAM,IAGpF,OAAO,GAkBP4E,GAAmB,SAAU/H,EAAW4E,EAAetB,EAAYH,GAInE,IAAKkJ,GAAmBrM,EAAW4E,EAAetB,EAAYH,GAAO,CAEjE,GAAIgB,GAAanE,EAAUmE,UAG3B,IAAIb,IAAe2H,GAAoBrG,IAAkBjB,EAWrD,MAVIL,KAAeiG,EACX3E,IAAkBjB,GAClB3D,EAAU8D,SAEPR,IAAesJ,IAEtBvI,EAAUN,GAAS6F,GAAoB7F,IAAUwI,EAAsB,0EACvErD,GAAY+B,KAGT,CAGX,QAAQrG,GACJ,IAAKjB,GAGD,GAAIL,IAAesG,GAAoBtG,IAAeiJ,EAGlD,GADArD,GAAY+B,GACRjL,EAAUO,GAAK,EAAG,CAElB,GAAIsM,GAAQvK,EAAqBtC,EAAUM,EAAGN,EAAUO,EAAGkC,EAC3D0B,GAAWqI,EAAkBK,EAAMvM,OAEnC6D,GAAWsI,EAAiBzM,EAGpC,MAEJ,KAAKyM,GAID1M,EAAWC,EAAWmD,EACtB,IAAIpB,GAAMvB,EAAyBR,EAAUS,EAEzCT,GAAUO,IAAMwB,GAAOoB,EAAK5C,EAAIkC,GAEhC4C,EAAMI,aACNuF,GAAS7H,EAAK7C,EAAI6C,EAAK5C,GAEvBP,EAAUoE,YAGVD,EAAWqI,EAAkBrJ,EAAK7C,EAAImC,EAE1C,MAEJ,SACI,MAAOkK,IAAqB3M,EAAW4E,EAAetB,EAAYH,GAAM,IAIpF,OAAO,GAYPwJ,GAAuB,SAAU3M,EAAW4E,EAAetB,EAAYH,EAAM2J,GAE7E,GAGI5F,GAHA5C,EAAQtE,EAAUsE,MAClBH,EAAanE,EAAUmE,WACvBM,EAAOzE,EAAUyE,IAGrB,QAAQG,GACJ,IAAKX,GAED8G,IACA,MAEJ,KAAKyB,GAGDtF,EAAUkE,GAASjI,GAAM2F,KAAK,SAAUiE,EAAO9M,GAEtCD,EAAU4D,WACPmJ,EAEA5I,EAAWsI,EAAiBxM,GAG5BkE,EAAW6I,EAAmB7J,KAI1C,MAEJ,KAAK6J,GAGD9F,EAAU2C,GAAU1G,GAAM2F,KAAK,SAAU7I,GAEhCD,EAAU4D,WAEPkJ,EACAzH,EAAMG,aAENH,EAAME,WAEVpB,EAAW8I,EAAiBhN,KAEjCqE,EACH,MAEJ,KAAK2I,GAIG3J,IAAesJ,IACf1D,GAAY0D,GACZ1F,EAAUwE,GAASvI,EAAK7C,EAAG6C,GAAM2F,KAAK,SAAUoE,GACvClN,EAAU4D,YACNsJ,GAASJ,IACV9M,EAAUoD,QAAU,GAGxBe,EAAWsI,EAAiBtJ,IAEhC+F,GAAYU,KAGpB,MAEJ,SAEI,OAAO,EAaf,MAVI1C,KAEIlH,EAAU4D,SACVsD,EAAQpD,SACD9D,EAAU0D,IAAMkB,GAEvBH,EAAKyC,KAIN,EAwCX,OApCAtH,GAAMkK,KAAK,aAAc,SAAU2B,EAAGO,GAClC,GAAImB,EAASnB,GAAW,CACpB,GAAIoB,GAAkBpB,EAAS5M,OAC/B,KAAKgO,GAAqD,IAAlCA,EAAgBC,QAAQ,MAE5C,WADApE,GAAqB,mCAAqCmE,IAI1D3K,KAAauJ,EAASvJ,UAAY8D,EAAO2F,aAAeF,EAASC,SAEjE3C,KAAaR,KAAK,WAEdI,GAAYU,IACbX,EAAqB,iDAGxBpD,EAAkBmG,EAASnG,gBAC3BC,EAAekG,EAASlG,aACxBC,EAAYiG,EAASjG,UACrBE,EAAkB+F,EAAS/F,gBAC3BC,EAAmB8F,EAAS9F,iBAC5BC,EAAuB6F,EAAS7F,qBAChC/G,EAAUgO,EAGVlE,GAAYU,QAIhBkC,IAAa,WAET5C,GAAYU,IACbX,EAAqB,qDAE7BA,EAAqB,wCAEjB1F,EASX,QAAS+J,GAAiBnI,GAKtB,GAJAzD,EAA8ByD,EAAQ1C,SAAU,YAChDd,EAAuBwD,EAAQY,UAAW,aAC1CpE,EAAuBwD,EAAQkB,aAAc,iBAExC8G,EAAShI,EAAQ9D,MAClB,MAAQE,QAAS,yBAA0B4D,QAASA,EAGxD,KAAKgI,EAAShI,EAAQoB,QAClB,MAAQhF,QAAS,mBAAoB4D,QAASA,EAGlD,OAAO,IAAID,GAAUC,GAl4CzB,GAAI1F,GAASN,EAAQ,cACjBK,EAAWL,EAAQ,iBACnBuH,EAAWvH,EAAQ,cACnBqH,EAAcrH,EAAQ,qBAGtBgO,EAAW1N,EAAM0N,SACjBnG,EAAQvH,EAAMuH,MACdI,EAAS3H,EAAM2H,OACf/C,EAAY5E,EAAM4E,UAGlB2F,GAFUvK,EAAM8N,QACD9N,EAAM+N,aACR/N,EAAMuK,YACnBhE,EAAmBvG,EAAMuG,iBAEzBe,EAAiBvH,EAASuH,eAC1ByC,EAAchK,EAASgK,YAGvBhJ,EAA2Bf,EAAMe,yBACjCG,EAAsBlB,EAAMkB,oBAC5BD,EAAuBjB,EAAMiB,qBA0I7B6I,EAAuB,UACvBK,EAAuB,OACvB/E,EAAuB,OACvBoG,EAAuB,OACvBsB,EAAuB,WACvBK,EAAuB,QAIvB1I,EAAyB,SACzBD,EAAyB,MACzBD,EAAyB,QACzBL,EAAyB,QACzBe,EAAyB,OAGzB4H,EAAsB,QAGtBG,EAAoB,OACpBD,EAAoB,QACpBS,EAAoB,OACpBD,EAAoB,QAwKxBnK,GAAmB4K,UAAUzI,aAAe,WAGxC,GAAIvB,GAAID,KAAKC,CACTA,KACAD,KAAKC,EAAI,KACTA,EAAEwD,QAAQzD,KAAK/C,KAOvBoC,EAAmB4K,UAAU1I,aAAe,SAAU2I,GAGlD,GAAIjK,GAAID,KAAKC,CACTA,KACAD,KAAKC,EAAI,KACTA,EAAE0D,OAAOuG,KAOjB7K,EAAmB4K,UAAU3I,aAAe,WAExCtB,KAAKuB,cAAenB,UAAU,EAAMrC,QAAS,wBA8gCjDtC,EAAQiD,aAAeA,EAGvBjD,EAAQqO,gBAAkBA,GAIvB/G,OAAW,SAAStH,EAASC,EAAQC,GACxC,YAiBA,SAASwO,GAAkBC,EAAKC,GAC5B,GAAIC,GAAaF,EAAIP,QAAQ,MAAQ,EAAK,IAAM,GAChD,OAAOO,GAAME,EAAYD,EAQ7B,QAASE,GAAcH,EAAKI,GACxB,GAAI/K,GAAQ2K,EAAIP,QAAQ,KACpBY,EAAe,EASnB,OARIhL,IAAS,IACTgL,EAAeL,EAAIM,OAAOjL,GAC1B2K,EAAMA,EAAIM,OAAO,EAAGjL,IAGI,MAAxB2K,EAAIA,EAAI5L,OAAS,KACjB4L,GAAO,KAEJA,EAAMI,EAAUC,EAO3B,QAASE,GAAkBP,EAAKzI,GAC5B,OACIiJ,OAAQ,MACRC,WAAYT,EACZU,KAAMnJ,EAAQmJ,KACdC,SAAUpJ,EAAQoJ,SAClBC,oBAAqBrJ,EAAQqJ,oBAC7BC,sBAAuBtJ,EAAQsJ,sBAC/BC,kBAAmBvJ,EAAQuJ,mBA+BnC,QAASC,GAAcf,EAAKzI,EAAS4G,EAASzH,GAC1C,MAAOsK,GAAqBhB,EAAKzI,KAAa4G,EAASzH,GAW3D,QAASsK,GAAqBhB,EAAKzI,EAAShC,EAAM4I,EAASzH,GAEvD,GAAI4C,GAAUiH,EAAkBP,EAAKzI,GACjC0J,EAAiBC,EAAa5H,QAAQA,EAAS,SAAU6H,GACzD,GAAIC,GAAWD,EAAQ,kBACvB,IAAIC,EAAU,CACV,GAAI/L,GAAQ2K,EAAIP,QAAQ,QAAS,EACpB,KAATpK,IACA+L,EAAWpB,EAAIqB,UAAU,EAAGhM,EAAQ,GAAK+L,GAIjD,GAAI7L,EAAKwF,OAASoG,EAAQpG,MACtBxF,EAAKwF,MAAQxF,EAAKwF,MAAMU,OAAO0F,EAAQpG,WAGvC,KAAK,GAAIuG,KAAYH,GACD,mBAAZG,IACA/L,EAAK+L,GAAYH,EAAQG,GAKjCF,GACAH,EAAiBD,EAAqBI,EAAU7J,EAAShC,EAAM4I,EAASzH,GAGxEyH,EAAQ5I,IAEbmB,EAAOhD,OAAW6D,EAAQgK,WAAYhK,EAAQiK,SAEjD,QACI/H,MAAO,WACHwH,EAAexH,UAU3B,QAASZ,GAAkBtB,GACvB,GAAI5B,GAAOC,KACPoK,EAAMzI,EAAQoB,MAwClB,OAtCAhD,GAAK2I,WAAamD,EAAiBC,UAAUC,UAAU3B,KACvDrK,EAAK4B,QAAUA,EAQf5B,EAAKL,MAAQ,SAAU6I,EAASzH,GAC5B,GAAIa,GAAU5B,EAAK4B,OACnB,OAAO2J,GAAa5H,QAChBiH,EAAkBJ,EAAcH,EAAK,UAAWzI,GAChD,SAAUhC,GACN,GAAID,GAAQ8G,EAAW7G,EAAKqM,WACxBhO,OAAM0B,GACNoB,GAAQ/C,QAAS,eAAgB2B,MAAOA,IAExC6I,EAAQ7I,IAEboB,EAAOhD,OAAW6D,EAAQgK,WAAYhK,EAAQiK,WAYzD7L,EAAKuG,KAAO,SAAU7G,EAAOC,EAAO6I,EAASzH,GAEzC,GAAImL,GAAe,SAAWxM,EAAQ,SAAWC,CACjD,OAAOyL,GAAahB,EAAkBC,EAAK6B,GAAelM,EAAK4B,QAAS4G,EAASzH,IAG9Ef,EAjLX,GAAI9D,GAAQN,EAAQ,iBAChB2P,EAAe3P,EAAQ,iBAEvB6K,EAAavK,EAAMuK,WACnBqF,EAAmB5P,EAAM4P,gBAmL7BpQ,GAAQwH,iBAAmBA,GAAoBjH,SAAa,SAASP,GACrE,YAYA,SAASyQ,GAAYC,EAAWtO,EAAMuO,GAClC,MAAO,YAEH,MADAD,GAAUtO,GAAMwO,MAAMF,EAAWG,WAC1BF,GA6Bd,QAASpG,KACNhG,KAAKuM,WAAazO,OAClBkC,KAAKwM,MAAQ1O,OACbkC,KAAKyM,MAAQ3O,OACbkC,KAAK0M,WAAY,EACjB1M,KAAK2M,WAAY,EA2GrB,QAASpJ,KACL,MAAIoB,QAAOiI,QAAUjI,OAAOiI,OAAOC,SACxB,GAAIlI,QAAOiI,OAAOC,SAElB,GAAI7G,GA3GnBA,EAAYiE,WAQR3E,KAAM,SAAUwH,EAAkBC,GA8B9B,MA5BID,KACK9M,KAAKwM,MAGNxM,KAAKwM,MAAMtF,KAAK4F,GAFhB9M,KAAKwM,OAASM,IAMlBC,IACK/M,KAAKyM,MAGNzM,KAAKyM,MAAMvF,KAAK6F,GAFhB/M,KAAKyM,OAASM,IAYlB/M,KAAK0M,UACL1M,KAAKyD,QAAQ4I,MAAMrM,KAAMA,KAAKuM,YACvBvM,KAAK2M,WACZ3M,KAAK2D,OAAO0I,MAAMrM,KAAMA,KAAKuM,YAG1BvM,MAOXyD,QAAS,WACL,GAAIzD,KAAKwM,MAAO,CACZ,GAAI1P,GAAGyB,CACP,KAAKzB,EAAI,EAAGyB,EAAMyB,KAAKwM,MAAMhO,OAAYD,EAAJzB,EAASA,IAG1CkD,KAAKwM,MAAM1P,GAAGuP,MAAM,KAAMC,UAO9BtM,MAAKwM,MAAQ1O,OACbkC,KAAK0M,WAAY,EACjB1M,KAAKuM,WAAazO,WAElBkC,MAAK0M,WAAY,EACjB1M,KAAKuM,WAAaD,WAQ1B3I,OAAQ,WAEJ,GAAI3D,KAAKyM,MAAO,CACZ,GAAI3P,GAAGyB,CACP,KAAKzB,EAAI,EAAGyB,EAAMyB,KAAKyM,MAAMjO,OAAYD,EAAJzB,EAASA,IAC1CkD,KAAKyM,MAAM3P,GAAGuP,MAAM,KAAMC,UAG9BtM,MAAKyM,MAAQ3O,OACbkC,KAAK2M,WAAY,EACjB3M,KAAKuM,WAAazO,WAElBkC,MAAK2M,WAAY,EACjB3M,KAAKuM,WAAaD,WAS1B/M,QAAS,WACL,GAAIjC,KAEJ,OADAA,GAAOgI,KAAO4G,EAAYlM,KAAM,OAAQ1C,GACjCA,IAmBf7B,EAAQ8H,eAAiBA,EAGzB9H,EAAQuK,YAAcA,GAAegH,MAAU,SAASvR,EAASC,EAAQC,GACzE,YAkCA,SAASsR,GAAgBC,EAAeC,EAAmBC,GAEvD,GAAItQ,GAAGyB,CACP,KAAKzB,EAAI,EAAGyB,EAAM8O,EAAS7O,OAAYD,EAAJzB,IAAYuQ,EAASvQ,GAAGoQ,GAAeC,EAAmBC,GAAUtQ,KAGvG,GAAIA,IAAMyB,EACN,MAAQR,QAAS,uBApCzB,GAAIuP,GAAgB7R,EAAQQ,MAAYN,EAAQ,yBAC5C4R,EAAgB9R,EAAQ+R,QAAY7R,EAAQ,sBAC5C8R,EAAgBhS,EAAQmQ,SAAYjQ,EAAQ,uBAC5C+R,EAAgBjS,EAAQkS,IAAYhS,EAAQ,kBAC5CiS,EAAgBnS,EAAQoS,KAAYlS,EAAQ,kBAC5BF,GAAQqS,MAAYnS,EAAQ,mBAIhD,IAAIM,GAAQN,EAAQ,cAChBgO,EAAW1N,EAAM0N,SAGjBoE,GADU9R,EAAM+R,QACK/R,EAAM8R,oBAG3BE,GADgBX,EAAWY,cACAX,EAAaU,0BAExCE,GADiBb,EAAWc,eACXX,EAAcU,gBAI/Bd,GAAYO,EAAUS,YAAad,EAAae,YAqBpD7S,GAAQ8S,eAAiB,SAAU5O,GAE/BgF,OAAO6J,MAAM7J,OAAO8J,KAAKC,UAAU/O,KAGvClE,EAAQkT,aAAeZ,EAEvBtS,EAAQmT,gBAMAtI,KAAM,SAAUuI,EAAUzB,GAElByB,GAAYlF,EAASkF,EAASC,OAASD,EAASE,QAAQ,iBACxD9B,EAAgB,OAAQ4B,EAAUzB,IAQ1C4B,MAAO,SAAUtL,EAAS0J,GAEtBH,EAAgB,QAASvJ,EAAS0J,IAGtC6B,sBAAuBhB,EACvBiB,OAAQ,qCAGhBzT,EAAQ0T,mBAUR1T,EAAQ6K,KAAO,SAAU8I,EAAc7G,EAASzH,EAAO0M,EAAS7B,EAAYC,GAExE,GAAIlI,EAOJ,OALIA,GADA0L,YAAwBC,SAAkC,gBAAjBD,IAC7BvE,WAAYuE,GAEdA,EAGP3T,EAAQiI,QAAQA,EAAS6E,EAASzH,EAAO0M,EAAS7B,EAAYC,IAWzEnQ,EAAQiI,QAAU,SAAUA,EAAS6E,EAASzH,EAAO0M,EAAS7B,EAAYC,GAEtErD,EAAUA,GAAW9M,EAAQ8S,eAC7BzN,EAAQA,GAASrF,EAAQkT,aACzBnB,EAAUA,GAAW/R,EAAQmT,eAC7BjD,EAAaA,GAAc+B,EAAS4B,kBACpC1D,EAAWA,GAAYnQ,EAAQ0T,gBAG/BzL,EAAQ6L,eAAiBtT,EAAM+R,QAAQtK,EAAQ6L,eAAgB3B,EAAUS,YAAYkB,gBACrF7L,EAAQuH,sBAAwBhP,EAAM+R,QAAQtK,EAAQuH,sBAAuByC,EAAS4B,kBAAkBrE,uBACxGvH,EAAQwH,kBAAoBjP,EAAM+R,QAAQtK,EAAQwH,kBAAmBwC,EAAS4B,kBAAkBpE,mBAChGxH,EAAQsH,oBAAsB/O,EAAM+R,QAAQtK,EAAQsH,oBAAqB0C,EAAS4B,kBAAkBtE,oBAGpG,IAAIoC,IACAxB,SAAUA,EACV2D,eAAgB7L,EAAQ6L,eACxBtE,sBAAuBvH,EAAQuH,sBAC/BC,kBAAmBxH,EAAQwH,kBAC3BF,oBAAqBtH,EAAQsH,oBAGjC,KAEI,MADAsC,GAAWc,eAAe1K,EAAS8J,EAASJ,GACrCE,EAAWY,cAAcxK,EAAS6E,EAASzH,EAAO0M,EAAS7B,EAAYyB,GAChF,MAAOrM,GAGL,GAAIA,EAAIyO,uBACJ,KAAMzO,EAEND,GAAMC,KAUlBtF,EAAQgU,cAAgB,SAAUC,GAE9B,MAAOvB,GAAe,KAAMuB,IAIhCjU,EAAQqS,MAAM6B,aAAaC,YAAcnU,EAAQmT,eACjDnT,EAAQoU,gBAAmBpC,EAAcoC,gBACzCpU,EAAQ4S,YAAeT,EAAUS,aAC9BP,MAAU,SAASrS,EAASC,EAAQC,GACvC,YA+BA,SAASmU,KAEL,MAAO5Q,MAAKC,MAA4B,OAArB,EAAID,KAAK6Q,WAAqB/D,SAAS,IAAItB,OAAO,GAOzE,QAASsF,GAAeC,GAEpB,MAAOA,GAASH,IAAU,IAAMA,IAAU,IAAMA,IAOpD,QAASF,GAAYxC,GAEjB,MAAOA,GAAQI,QAAQoC,YAO3B,QAASM,GAAgB9C,GACrB,GAAI+C,GAAa/C,EAAQ+C,UACzB,OAAOA,GAAWA,EAAW3R,OAAS,GAS1C,QAAS4R,GAAY5C,EAAS6C,EAAMjD,GAEhC,GAAIkD,GAAWlD,EAAQmD,YAAYC,WAAqB,QACxD,QAASC,iBAAkBC,EAAUL,GAAQF,YAAaG,GAAWK,eAAgBvD,KASzF,QAASwD,GAAgBpD,EAAS7N,EAAMyN,GAEpC,GAAIyD,GAAQzD,EAAQmD,YAAcnD,EAAQmD,aAAeA,EAAYO,EACrE,OAAID,GAAME,YAAcD,EACbE,EAAWrR,EAAMyN,GAD5B,OAUJ,QAASsD,GAAUL,EAAMjD,GACrB,GAAI6D,GAAY,KAAOf,EAAgB9C,EAGvC8D,GAAOb,EAAMjD,EAAS6D,GAGtBE,EAASd,EAAMjD,EAMf,KAHA,GAAIgE,MACAC,EAAU,KAEK,OAAZA,GAAoBjE,EAAQkE,SAAWjB,EAAK7R,QAAQ,CACvD,GAGI+S,GAHAC,EAAcC,EAAYpB,EAAMjD,GAChCsE,EAAkBnB,EAAYiB,EAAY,gBAG9C,IAAIE,GAAmBA,EAAgBX,YAAcD,EAAgB,CACjE1D,EAAQ+C,WAAWjJ,KAAKwK,EAAgBlB,WAAWF,SACnD,KACIiB,EAAkBb,EAAUL,EAAMjD,GACpC,MAAOuE,GACLA,EAAE9C,SAAW+C,EAAavB,EAAMjD,EAAS6D,GACzCM,GAAmBI,GAEvBP,EAAUlK,MAAO2K,kBAAmBN,IACpCnE,EAAQ+C,WAAW2B,MACnBZ,EAAOb,EAAMjD,EAAS,KAAO8C,EAAgB9C,QAC1C,CACH,IAAKsE,GAAiD,qBAA9BA,EAAgBX,UACpC,MAAQhT,QAAS,0BAGrBoT,GAASd,EAAMjD,EAEf,IAAIyB,GAAW+C,EAAavB,EAAMjD,EAAS6D,EAC3C,KACQpC,EAASkD,YAAc,KAAOlD,EAASkD,YAAc,IACrDnC,EAAYxC,EAAQuD,gBAAgBrK,KAAKuI,EAAUzB,EAAQuD,gBAG3D9B,GAAa9Q,QAAS,sBAAuB8Q,SAAUA,GAE7D,MAAO8C,GACL9C,EAAW8C,EAGfP,EAAUlK,KAAK2H,GAGnBwC,EAAUhB,EAAK3F,OAAO0C,EAAQkE,SAAU,GAGxCH,EAASd,EAAMjD,GAEnB,MAAOgE,GASX,QAASK,GAAYpB,EAAMjD,GACvB,GACI4E,GACAC,EACAC,EAHAnD,IAKJ,GACImD,GAAM9E,EAAQkE,SACdW,EAAOd,EAASd,EAAMjD,GACtB4E,EAAQG,EAAoBC,KAAKH,GACnB,OAAVD,EACAjD,EAAQiD,EAAM,IAAMA,EAAM,GAG1B5E,EAAQkE,SAAWY,QAElBD,GAAQD,EAIjB,OAFAK,GAAiBtD,GAEVA,EASX,QAAS6C,GAAavB,EAAMjD,EAAS6D,GAEjC,GAGIc,GACAO,EACAvD,EALAmD,EAAM9E,EAAQkE,SACdiB,EAAQC,EAAoBJ,KAAKjB,EAASd,EAAMjD,GAepD,OATImF,IACAR,EAAaQ,EAAM,GACnBD,EAAaC,EAAM,GACnBxD,EAAU0C,EAAYpB,EAAMjD,GAC5B+D,EAASd,EAAMjD,IAEfA,EAAQkE,SAAWY,GAInBH,WAAYA,EACZO,WAAYA,EACZvD,QAASA,EACTD,KAAMoC,EAAOb,EAAMjD,EAAS,OAAS6D,IAS7C,QAASE,GAASd,EAAMjD,GAEpB,MAAO8D,GAAOb,EAAMjD,EAAS,QASjC,QAAS8D,GAAOb,EAAMjD,EAASqF,GAC3B,GAAI7V,GAAQwQ,EAAQkE,UAAY,EAC5BzU,EAAMwT,EAAK7R,MACf,IAAIiU,EAAK,CAEL,GADA5V,EAAMwT,EAAKxG,QAAQ4I,EAAK7V,GACZ,KAARC,EACA,MAAO,KAEXuQ,GAAQkE,SAAWzU,EAAM4V,EAAIjU,WAE7B4O,GAAQkE,SAAWzU,CAGvB,OAAOwT,GAAK5E,UAAU7O,EAAOC,GAQjC,QAASmU,GAAWrR,EAAMyN,GACtB,IAAKsF,EAAQ/S,GACT,MAAQ5B,QAAS,8BAGrB,IAGIjB,GAAGyB,EAHHoU,EAAgB3C,EAAe,UAC/B4C,EAAajT,EAAKkT,gBAClB/E,EAAQ,EAEZ,KAAKhR,EAAI,EAAGyB,EAAMqU,EAAWpU,OAAYD,EAAJzB,EAASA,IAC1CgR,GAASgF,EAAwBH,GAAe,GACvCI,EAAeH,EAAW9V,GAAIsQ,EAE3CU,IAASgF,EAAwBH,GAAe,EAGhD,IAAIK,GAAwB5F,EAAQmD,YAAYC,UAGhD,OAFAwC,GAAsB1C,SAAWqC,EAE1B7E,EAQX,QAASgF,GAAwBxC,EAAUrK,GACvC,GAAI3I,GAAS,SAAWgT,CAKxB,OAJIrK,KACA3I,GAAU,MAGPA,EAAS,OAWpB,QAASyV,GAAeE,EAAM7F,EAAS8F,GAGnC,GACI5V,GADA6V,EAAYF,EAAKG,gBAErB,IAAIrJ,EAAQoJ,GAAY,CACpB,GAAID,EACA,MAAQnV,QAAS,uDAGrB,IAAIsV,GAAoBrD,EAAe,aACvC1S,GAAS,iBAAmBwT,EAAiB,cAAgBuC,EAAoB,MACjF,IAAIvW,GAAGyB,CACP,KAAKzB,EAAI,EAAGyB,EAAM4U,EAAU3U,OAAYD,EAAJzB,EAASA,IACzCQ,GAAUwV,EAAwBO,GAAmB,GAChDN,EAAeI,EAAUrW,GAAIsQ,GAAS,EAG/C9P,IAAUwV,EAAwBO,GAAmB,OAClD,CACH/V,EAAS,6EACT,IAAIgW,GAAc1P,KAAWwJ,EAC7BkG,GAAY9F,QAAUA,EACtB8F,EAAY5P,QAAUuP,EACtBK,EAAY/C,YAAc,KAE1BnC,EAAe6E,EAAMrD,EAAYxC,GAAUkG,GAC3ChW,GAAUiW,EAAaN,GAG3B,MAAO3V,GAOX,QAASiW,GAAa7P,GAClB,GAAIpG,IAAUoG,EAAQkH,OAASlH,EAAQkH,OAAS,OAAS,IAAMlH,EAAQmH,WAAa,eACpF,KAAK,GAAIhN,KAAQ6F,GAAQqL,QACjBrL,EAAQqL,QAAQlR,KAChBP,EAASA,EAASO,EAAO,KAAO6F,EAAQqL,QAAQlR,GAAQ,OAUhE,OANAP,IAAU,OAENoG,EAAQoL,OACRxR,GAAUoG,EAAQoL,MAGfxR,EAtVX,GAAIrB,GAAWN,EAAQ,iBACnB2R,EAAgB3R,EAAQ,mBACxB4R,EAAe5R,EAAQ,gBAEvBiI,EAAS3H,EAAM2H,OACfmG,EAAU9N,EAAM8N,QAGhBwG,GAFatU,EAAMuX,WAELjG,EAAagD,aAC3B/C,EAAUD,EAAaC,QACvBkF,EAAUpF,EAAWoF,QACrBzE,EAA2BV,EAAaU,yBACxCoE,EAAmB/E,EAAW+E,iBAE9BjE,EAAiBd,EAAWc,eAM5B0C,EAAiB,kBACjB0B,EAAsB,8BACtBL,EAAsB,2CAsU1B1W,GAAQkU,aAAenC,EAAQ4C,EAAaQ,EAAiBE,EAAgB7C,GAG7ExS,EAAQmV,gBAAkBA,EAG1BnV,EAAQ8X,aAAeA,GAAgB/F,QAAY,SAAS/R,EAASC,EAAQC,GAC7E,YAmBA,SAAS4U,GAAYkC,GAEjB,IAAKA,EACD,MAAO,KAGX,IAGI3V,GAAGyB,EAHHkV,EAAmBhB,EAAIiB,MAAM,KAC7BlD,IAGJ,KAAK1T,EAAI,EAAGyB,EAAMkV,EAAiBjV,OAAYD,EAAJzB,EAASA,IAAK,CACrD,GAAI6W,GAAoBF,EAAiB3W,GAAG4W,MAAM,IAClDlD,GAAWgD,EAAWG,EAAkB,KAAOA,EAAkB,GAGrE,OAAS5C,UAAWyC,EAAWC,EAAiB,IAAKjD,WAAYA,GAOrE,QAASoD,GAAoBrD,GACzB,IAAKA,EACD,MAAOzS,OAGX,IACI4N,GADApO,EAASiT,EAAYQ,SAEzB,KAAKrF,IAAY6E,GAAYC,WACzBlT,GAAU,IAAMoO,EAAW,IAAM6E,EAAYC,WAAW9E,EAE5D,OAAOpO,GAUX,QAASuW,GAAuBtD,EAAauD,EAAoB1G,EAASI,GAEtE,GAAIuG,KAQJ,OAPAnQ,GAAOmQ,EAAW3G,GAClBxJ,EAAOmQ,GACHxD,YAAaA,EACbuD,mBAAoBA,EACpBtG,QAASA,IAGNuG,EAQX,QAASC,GAAiBtQ,EAAS7F,EAAMsH,GACrC,GAAKzB,EAAL,CAIA,GAAIqL,GAAUrL,EAAQqL,OACjBA,GAAQlR,KACTkR,EAAQlR,GAAQsH,IASxB,QAAS8O,GAA4BvQ,EAAS9H,GAE1C,GAAI8H,EAAS,CACT,GAAIqL,GAAUrL,EAAQqL,QAClBmF,EAAMnF,EAAQ,gBAClBA,GAAQ,iBAAmBmF,EAAMC,EAAWD,EAAKtY,GAAWA,GASpE,QAASwY,GAA2BjH,EAAmBtP,GAEnD,GAAIkR,GAAU5B,EAAkB4B,OAChC,OAAQA,IAAWA,EAAQlR,IAAUC,OAOzC,QAASuW,GAAelH,GAEpB,MAAOoD,GAAY6D,EAA2BjH,EAAmB,iBAQrE,QAASmH,GAAsBnH,GAE3B,GAAIhI,GAAQiP,EAA2BjH,EAAmB,gBAC1D,IAAIhI,EAAO,CACP,GAAIoP,GAAUC,EAAUpC,KAAKjN,EAC7B,IAAIoP,GAAWA,EAAQ/V,OACnB,MAAO+V,GAAQ,IAe3B,QAASE,GAAejH,EAASqD,GAC7B,MAAOrD,GAAQ0B,OAAOrF,QAAQgH,EAAME,YAAc,EAUtD,QAAS2D,GAAYlH,EAASmH,EAAe9F,EAAUzB,GAEnD,IAAKyB,IAAaA,EAASE,QACvB,OAAO,CAGX,IAAI8B,GAAQwD,EAAexF,GACvBjT,EAAU0Y,EAAsBzF,IAAa,GAC7CC,EAAOD,EAASC,IAEpB,KAAKnF,EAASmF,GACV,OAAO,CAGX,IAAI2F,EAAejH,EAASqD,GAAQ,CAChC,GAAI+D,GAAcf,EAAuBhD,EAAOjV,EAASwR,EAASI,EAGlE,OAFAoH,GAAY/F,SAAWA,EACvBA,EAASlP,KAAOgV,EAAcnH,EAASsB,EAAM8F,GACpB9W,SAAlB+Q,EAASlP,KAGpB,OAAO,EAUX,QAASkV,GAAarH,EAASsH,EAAmBpR,EAAS0J,GACvD,IAAK1J,IAAYA,EAAQqL,QACrB,OAAO,CAGX,IAAI8B,GAAQwD,EAAe3Q,GACvB9H,EAAU0Y,EAAsB5Q,EAEpC,KAAKmN,GAAS4D,EAAejH,EAASqD,GAAQ,CAC1C,GAAIkE,GAAelB,EAAuBhD,EAAOjV,EAASwR,EAASI,EAKnE,IAJAuH,EAAarR,QAAUA,EAEvBA,EAAQoL,KAAOgG,EAAkBtH,EAAS9J,EAAQ/D,KAAMoV,GAEnCjX,SAAjB4F,EAAQoL,KAKR,MAJAmF,GAA4BvQ,EAASqR,EAAajB,oBAAsB,OAExEE,EAAiBtQ,EAAS,eAAgBkQ,EAAoBmB,EAAaxE,cAC3EyD,EAAiBtQ,EAAS,mBAAoB8J,EAAQyB,wBAC/C,EAIf,OAAO,EAUX,QAASzB,GAAQmH,EAAeG,EAAmB5F,EAAQD,GAEvD,OACIC,OAAQA,EACRD,sBAAuBA,EAEvB3I,KAAM,SAAUuI,EAAUzB,GACtB,MAAOsH,GAAY1U,KAAM2U,EAAe9F,EAAUzB,IAGtD4B,MAAO,SAAUtL,EAAS0J,GACtB,MAAOyH,GAAa7U,KAAM8U,EAAmBpR,EAAS0J,KAKlE,QAAS4H,GAAUxH,EAASsB,GACxB,MAAOA,GAGX,QAASmG,GAAczH,EAAS7N,GAC5B,MAAIgK,GAAShK,GACFA,EAAKqM,WAELlO,OApPf,GAAI7B,GAAWN,EAAQ,iBACnBuZ,EAAgBvZ,EAAQ,mBAGxBgO,EAAW1N,EAAM0N,SACjB/F,EAAS3H,EAAM2H,OACf4P,EAAavX,EAAMuX,WACnBW,EAAae,EAAWf,WACxBlG,EAA2B,MA+G3BuG,EAAY,qBAoIhB/Y,GAAQ6S,YAAcd,EAAQwH,EAAWC,EAAe,aAAchH,GACtExS,EAAQ8U,YAAcA,EACtB9U,EAAQmY,oBAAsBA,EAC9BnY,EAAQ+R,QAAUA,EAClB/R,EAAQoY,uBAAyBA,EACjCpY,EAAQuY,iBAAmBA,EAC3BvY,EAAQ2Y,2BAA6BA,EACrC3Y,EAAQ4Y,eAAiBA,EACzB5Y,EAAQ6Y,sBAAwBA,EAChC7Y,EAAQwS,yBAA2BA,GAA4BJ,KAAS,SAASpS,EAASC,EAAQC,GA8ElG,QAASwZ,GAAW3H,EAAS6C,EAAMjD,GAC/B,GAGIgI,GAHA7F,EAAiBvB,EAAQZ,EAAQmC,eAAgB/B,EAAQ+B,gBACzD8F,EAAQjI,EAAQxB,SAChBiC,EAAwB,gBAATwC,GAAqB5B,KAAK6G,MAAMjF,GAAQA,CAEvD1G,GAASyD,EAAQmD,cAAgB5G,EAASyD,EAAQmD,YAAYC,cAC9D4E,EAAsBhI,EAAQmD,YAAYC,WAAW,kBAGzD,IAAI+E,GAAgBC,EAAcJ,EAAqB,EAGvD,OAAsB,KAAlBG,EACO1H,EAEgB,IAAlB0H,EACEE,EAAgC5H,EAAMwH,EAAO9F,GAE7B,IAAlBgG,EAEEG,EAA6B7H,EAAMwH,EAAO9F,GAG1C1B,EA+Df,QAAS8H,GAAenI,EAAS7N,EAAMyN,GAEnC,GAAI0G,GAAqB1G,EAAQ0G,oBAAsB,MACnDjD,EAAQzD,EAAQmD,YAAcnD,EAAQmD,aAAeqF,EAEzD,IAAI/E,GAASA,EAAME,YAAc6E,GAAgB7E,UAAW,CACxD3D,EAAQ0G,mBAAqBK,EAAWL,EAAoB,MAC5D,IAAI+B,GAAUC,EAAyBnW,EACvC,IAAIkW,EACA,MAAOpH,MAAKC,UAAUmH,EAAQE,IAGtC,MAAOjY,QAUX,QAASgY,GAAyBnW,GAC9B,IAAKA,EACD,MAAOA,EAGX,IAAIqW,EAAYrW,GACZ,MAAOA,EAGX,IAAIoK,EAAQpK,GAAO,CACf,GACI7C,GAAGyB,EADH0X,IAEJ,KAAKnZ,EAAI,EAAGyB,EAAMoB,EAAKnB,OAAYD,EAAJzB,EAASA,IACpCmZ,EAAanZ,GAAKgZ,EAAyBnW,EAAK7C,GAGpD,OAAOmZ,GAGX,GAAIJ,KACJ,KAAK,GAAInK,KAAY/L,GACbuW,EAA2BxK,KAC3BmK,EAAQnK,GAAYoK,EAAyBnW,EAAK+L,IAI1D,OAAOmK,GAOX,QAASK,GAA2BC,GAChC,IAAKA,EACD,OAAO,CAGX,IAAoC,IAAhCA,EAAUtM,QAAQ,WAClB,OAAO,CAGX,IAAI/M,GAAGyB,CACP,KAAKzB,EAAI,EAAGyB,EAAM6X,GAAyB5X,OAAYD,EAAJzB,EAASA,IAAK,CAC7D,GAAIe,GAAOuY,GAAyBtZ,EACpC,IAA+B,IAA3BqZ,EAAUtM,QAAQhM,GAClB,OAAO,EAIf,OAAO,EAQX,QAASwY,GAAoBC,EAAMzX,GAC/B,OAASyX,KAAMA,EAAMzX,KAAMA,GAAQ,MAUvC,QAAS6W,GAA6B/V,EAAM0V,EAAO9F,GAC/C,GAAI1Q,EACJ,IAAI5C,EAAMsa,SAAS5W,GACf,IAAK,GAAIkI,KAAOlI,GACZ,GAAIA,EAAK6W,eAAe3O,IACK,KAArBA,EAAIgC,QAAQ,KACZ,GAAI5N,EAAM8N,QAAQpK,EAAKkI,IACnB,IAAK,GAAI/K,GAAI,EAAGA,EAAI6C,EAAKkI,GAAKrJ,SAAU1B,EACpC4Y,EAA6B/V,EAAKkI,GAAK/K,GAAIuY,EAAO9F,OAEnD,IAAItT,EAAMsa,SAAS5W,EAAKkI,IACT,OAAdlI,EAAKkI,KAELhJ,EAAOc,EAAKkI,EAAI,eACXhJ,GAIDA,EAAOA,EAAK4M,UAAU,GACjBgL,GAAmB5X,IAAS6X,GAAkB7X,IAG/C6W,EAA6B/V,EAAKkI,GAAMwN,EAAO9F,IANnDmG,EAA6B/V,EAAKkI,GAAMwN,EAAO9F,QAgBvD,IALA1Q,EAAOc,EAAKkI,EAAM,eAKb8B,EAAS9K,GAiBN0Q,GACAoH,EAAkBhX,EAAMkI,EAAKhJ,EAAK4M,UAAU,QAlB/B,CAGjB,GAAImL,SAAwBjX,GAAKkI,EACV,YAAnB+O,EACAC,EAAQlX,EAAMkI,EAAK,UACO,YAAnB+O,EACPC,EAAQlX,EAAMkI,EAAK,WACO,WAAnB+O,IACHjX,EAAKkI,GAAO,IAAM,EAClBgP,EAAQlX,EAAMkI,EAAK,SAEnBgP,EAAQlX,EAAMkI,EAAK,YAenD,MAAOlI,GAWX,QAASmX,GAAgBnX,EAAMoX,EAAgBC,EAAS3B,EAAO9F,GAC3D,IAAK,GAAI1R,KAAQ8B,GACb,GAA0B,KAAtB9B,EAAKgM,QAAQ,KAAa,CAK1B,IAJA,GAAIoN,GAAUF,EACVG,EAAgBvX,EAAK9B,GACrB6N,EAAWyL,EAAeF,EAAQvL,SAAS7N,GAEzB,OAAb6N,GAA4C5N,SAArBmZ,EAAQG,UACpCH,EAAUI,EAAiBJ,EAAQG,SAAU/B,GAC7C3J,EAAWyL,EAAeF,EAAQvL,SAAS7N,EAG/C,IAAKkM,EAAQmN,GAAgB,CAErBI,EAAiB5L,EAAS7M,MAC1B0Y,EAAgB5X,EAAK9B,EAAK6N,EAAS7M,KAAK4M,UAAU,GAAGC,EAAS7M,KAAKL,OAAO,IAE1EgZ,EAAa7X,EAAK9B,EAAK6N,EAAS7M,KAIpC,KAAM,GAAI/B,GAAI,EAAGA,EAAIoa,EAAc1Y,OAAQ1B,IACvC2a,EAAuCP,EAAcpa,GAAI4O,EAAUsL,EAAS3B,EAAO9F,OAEhFgH,GAASW,IAAqC,OAAlBA,EACnCO,EAAuCP,EAAexL,EAAUsL,EAAS3B,EAAO9F,IAGhFiI,EAAa7X,EAAK9B,EAAK6N,EAAS7M,MAC5B0Q,GACAmI,EAAa/X,EAAM9B,EAAM6N,EAAS7M,QAetD,QAAS4W,GAAgC9V,EAAM0V,EAAO9F,GAElD,IAAK5F,EAAS0L,IAAUtL,EAAQsL,GAC5B,MAAO1V,EAGX,IAAIqX,GAAUrX,EAAKgY,GACfC,EAAcC,EAAkBlY,EAAM0V,EAE1C,QAAQuC,EAAYE,qBAEhB,IAAKC,IACD,MAAyB,QAArBH,EAAY/Y,KACLmZ,EAAsCrY,EAAMiY,EAAaZ,EAAS3B,EAAO9F,GAEzEiI,EAAa7X,EAAK,QAASiY,EAAYK,SAGtD,KAAKC,IACD,MAAOC,GAAoCxY,EAAM0V,EAAOuC,EAAaZ,EAASzH,EAElF,KAAK6I,IACD,MAAOJ,GAAsCrY,EAAMiY,EAAaZ,EAAS3B,EAAO9F,EAEpF,KAAK8I,IACD,MAAOC,GAA0C3Y,EAAM0V,EAAOuC,EAAaZ,EAASzH,EAExF,KAAKgJ,IACD,MAAyB,QAArBX,EAAY/Y,KACLmZ,EAAsCrY,EAAMiY,EAAaZ,EAAS3B,EAAO9F,GAEzEiI,EAAa7X,EAAK,QAASiY,EAAYK,SAGtD,KAAKO,IACD,MAAO7Y,EAEX,KAAK8Y,mBACD,MAAO9Y,GAGf,MAAOA,GAUX,QAASwY,GAAoCxY,EAAM0V,EAAOqD,EAAU1B,EAASzH,GACzE,GAEIzS,GAAEyB,EACFoa,EAHAC,KACAC,EAAQlZ,EAAKwF,KAGjB,KAAKrI,EAAI,EAAGyB,EAAMsa,EAAMra,OAAYD,EAAJzB,EAASA,IAAK,CAC1C,GAAIwB,GAAOua,EAAM/b,EACjB,IAAKkR,EAAQ1P,EAAK,gBAAiB,CAC/B,GAAI2Z,GAAW3Z,EAAK,eAAemN,UAAU,GACzC5M,EAAOwY,EAAkBY,EAAU5C,GACnCyD,GACAC,iBAAmBL,EAASK,iBAC5BjB,oBAAsBY,EAASZ,oBAC/Bja,KAAO6a,EAAS7a,KAChBgB,KAAOA,EACPoZ,SAAWA,EAGfU,GAAQX,EAAsC1Z,EAAMwa,EAAW9B,EAAS3B,EAAO9F,OAE/EoJ,GAAQX,EAAsC1Z,EAAMoa,EAAU1B,EAAS3B,EAAO9F,EAGlFqJ,GAAQ1R,KAAKyR,GAGjB,MADAhZ,GAAKwF,MAAQyT,EACNjZ,EAWX,QAASqY,GAAsCrY,EAAMqZ,EAAYhC,EAAS3B,EAAO9F,GAC7EsH,EAAQlX,EAAK,GAAGqZ,EAAWf,SAG3B,KADA,GAAIgB,GAAUD,EAAWna,KACjBmP,EAAQiL,IAA+Bnb,SAAhBmb,EAAQpR,KAA4C/J,SAArBmb,EAAQ7B,UAClE6B,EAAU5B,EAAiB4B,EAAQ7B,SAAU/B,EAGjD,IAAoBvX,SAAhBmb,EAAQpR,IAAmB,CAC3B,GAAIqR,GAAgBF,EAAWnb,KAAOsb,EAAgBxZ,EAAMsZ,EAC5DtZ,GAAK,aAAeqX,EAAQvL,UAAU,EAAGuL,EAAQoC,YAAY,cAAgBF,EAC7EvZ,EAAK,mBAAqBuZ,EAO9B,MAFApC,GAAgBnX,EAAMqZ,EAAWna,KAAMmY,EAAS3B,EAAO9F,GAEhD5P,EAUX,QAAS8X,GAAuC9X,EAAM+L,EAAUsL,EAAS3B,EAAO9F,GAC5E,GAAI1Q,GAAO6M,EAAS7M,IAChByY,GAAiB5L,EAAS7M,QAC1BA,EAAM6M,EAAS7M,KAAK4M,UAAU,GAAGC,EAAS7M,KAAKL,OAAO,IAG1DqY,EAAQlX,EAAK,GAAG+L,EAAS7M,KAEzB,IAAIwa,GAAeC,EAAkBza,EAAMwW,EACtB,QAAjBgE,GAIJvC,EAAgBnX,EAAM0Z,EAAcrC,EAAS3B,EAAO9F,GAUxD,QAAS+I,GAA0C3Y,EAAM0V,EAAOkE,EAAgBvC,EAASzH,GAIrF,GAFAgI,EAAgB5X,EAAK,GAAI4Z,EAAetB,UAEZ,OAAxBsB,EAAe1a,KAAe,CAC9B,GAGI/B,GAAEyB,EACFoa,EAJAC,KAEAC,EAAQlZ,EAAKwF,KAGjB,KAAKrI,EAAI,EAAGyB,EAAMsa,EAAMra,OAAYD,EAAJzB,EAASA,IAAK,CAC1C,GAAIwB,GAAOua,EAAM/b,EACjB,IAAKkR,EAAQ1P,EAAK,gBAAiB,CAC/B,GAAI2Z,GAAW3Z,EAAK,eAAemN,UAAU,GACzC5M,EAAOwY,EAAkBY,EAAU5C,GACnCyD,GACAC,iBAAmBQ,EAAeR,iBAClCjB,oBAAsByB,EAAezB,oBACrCja,KAAO0b,EAAe1b,KACtBgB,KAAOA,EACPoZ,SAAWA,EAGfU,GAAQX,EAAsC1Z,EAAMwa,EAAW9B,EAAS3B,EAAO9F,OAE/EoJ,GAAQX,EAAsC1Z,EAAMib,EAAgBvC,EAAS3B,EAAO9F,EAGxFqJ,GAAQ1R,KAAKyR,GAEjBhZ,EAAKwF,MAAQyT,EAEjB,MAAOjZ,GAQX,QAASkX,GAAQlX,EAAM9B,EAAMsH,GACzB,GAAIqU,GAAW3b,EAAO,aAEEC,UAAnB6B,EAAK6Z,KACN7Z,EAAK6Z,GAAY,IAAMrU,GAS/B,QAASoS,GAAgB5X,EAAM9B,EAAMoa,GACjC,GAAIuB,GAAW3b,EAAO,aAEEC,UAAnB6B,EAAK6Z,KAEF7Z,EAAK6Z,GADsB,SAA1BvB,EAASxM,UAAU,EAAE,GACL,eAAewM,EAASxM,UAAU,GAAI,IAEtC,eAAewM,EAAU,KAWtD,QAAST,GAAa7X,EAAM9B,EAAMsH,GAC9B,GAAIqU,GAAW3b,EAAO,aAStB,OAPwBC,UAAnB6B,EAAK6Z,KAEF7Z,EAAK6Z,GADmB,SAAvBrU,EAAMsG,UAAU,EAAE,GACF,IAAMtG,EAAMsG,UAAU,GAEtB,IAAMtG,GAGxBxF,EAOX,QAAS+X,GAAa/X,EAAM8Z,EAAa5a,GACxB,aAATA,EACAc,EAAK8Z,GAAgBvE,EAAWwE,UAAU/Z,EAAK8Z,IAAe,GAC9C,uBAAT5a,EACPc,EAAK8Z,GAAgBvE,EAAWyE,oBAAoBha,EAAK8Z,IAAe,GACxD,iBAAT5a,EACPc,EAAK8Z,GAAgBvE,EAAW0E,cAAcja,EAAK8Z,IAAe,GAClD,aAAT5a,IACPc,EAAK8Z,GAAgBvE,EAAW2E,UAAUla,EAAK8Z,IAAe,IAStE,QAAS9C,GAAkBhX,EAAM8Z,EAAa5a,GAC7B,SAATA,EACAc,EAAK8Z,GAAgBvE,EAAWwE,UAAU/Z,EAAK8Z,IAAe,GAC9C,mBAAT5a,EACPc,EAAK8Z,GAAgBvE,EAAWyE,oBAAoBha,EAAK8Z,IAAe,GACxD,aAAT5a,EACPc,EAAK8Z,GAAgBvE,EAAW0E,cAAcja,EAAK8Z,IAAe,GAClD,SAAT5a,IACPc,EAAK8Z,GAAgBvE,EAAW2E,UAAUla,EAAK8Z,IAAe,IAStE,QAASK,GAAc3U,EAAOtG,GAI1B,OAFAsG,EAAQ,GAAK4U,EAAiB5U,EAAOtG,GACrCsG,EAAQ6U,mBAAmB7U,EAAM8U,QAAQ,IAAK,OACtC,GACJ,IAAK,aACD,MAAO,KAAO9U,EAAQ,GAC1B,KAAK,eACD,MAAO,YAAmBA,EAAQ,GACtC,KAAK,qBACD,MAAO,kBAAyBA,EAAQ,GAC5C,KAAK,cACD,MAAOA,GAAQ,GACnB,KAAK,WACD,MAAO,QAAeA,EAAQ,GAClC,KAAK,YACD,MAAOA,GAAQ,GACnB,KAAK,YACD,MAAOA,GAAQ,GACnB,KAAK,aACD,MAAOA,GAAQ,GACnB,KAAK,gBACD,MAAO,aAAoBA,EAAQ,GACvC,KAAK,eACD,MAAO,YAAmBA,EAAQ,GACtC,KAAK,WACD,MAAO,QAAeA,EAAQ,GAClC,KAAK,aACD,MAAO,IAAMA,EAAQ,GACzB,SACI,MAAOA,IASnB,QAAS4U,GAAiB5U,EAAOtG,GAC7B,OAAQA,GACJ,IAAK,aACD,MAAOqb,GAA4B/U,EACvC,SACI,MAAOA,IAQnB,QAASgV,GAAgBC,GAErB,GAAIC,EACU,GAAVD,GACAC,EAAO,IACPD,GAAWA,GAEXC,EAAO,GAGX,IAAIC,GAAQpb,KAAKC,MAAMib,EAAU,GAGjC,OAFAA,IAAqB,GAAKE,EAEnBD,EAAOE,EAAkBD,EAAO,GAAK,IAAMC,EAAkBH,EAAS,GAOjF,QAASI,GAAoBrV,GAEzB,GAAI9G,GAAM8G,GAASsV,GAAWrI,KAAKjN,EACnC,IAAI9G,EAAK,CAEL,GAAIf,GAAS,GAAIod,MAAKlU,EAAWnI,EAAI,IACrC,IAAIA,EAAI,GAAI,CACR,GAAIsc,GAAOnU,EAAWnI,EAAI,GACX,OAAXA,EAAI,KACJsc,GAAQA,EAKZ,IAAIC,GAAUtd,EAAOud,eACrBvd,GAAOwd,cAAcF,EAAUD,GAC/Brd,EAAOyd,UAAY,qBACnBzd,EAAO0d,SAAWb,EAAgBQ,GAEtC,IAAK3c,MAAMV,EAAO2d,WACd,MAAO3d,IAYnB,QAAS4d,GAAyBC,EAAW9F,GACzC,GAAI+F,KAEJ,IAA+B,KAA3BD,EAAUtR,QAAQ,KAAc,CAChC,GAAyB,IAArBsR,EAAU3c,OAGV,MADA4c,GAAItD,oBAAsBU,GACnB4C,CACJ,IAAkB,aAAdD,EAIP,MAFAC,GAAItD,oBAAsBC,GAC1BqD,EAAIC,gBAAiB,EACdD,CACJ,IAAkB,qBAAdD,EAGP,MADAC,GAAItD,oBAAsBwD,GACnBF,CACJ,IAAkB,SAAdD,EAGP,MADAC,GAAItD,oBAAsByD,GACnBH,EAMfA,EAAIvc,KAAOf,OACXsd,EAAInD,SAAWna,MAKf,KAAI,GAFAe,GADA2c,EAAgBL,EAAUzH,MAAM,KAG5B5W,EAAI,EAAGA,EAAI0e,EAAchd,SAAU1B,EAAG,CAC1C,GAAI2e,GAAWD,EAAc1e,EAC7B,IAAqBgB,SAAjBsd,EAAInD,SAAwB,CAE5B,GAA+B,KAA1BwD,EAAS5R,QAAQ,KAAc,CAGhC,IAAM,GADFpK,GAAQgc,EAASjd,OAAS,EACpBkd,EAAS,EAAGA,EAAS,GAAKjc,EAAQ,IAAKA,EAChB,KAAxBgc,EAASE,OAAOlc,GACjBic,IACgC,KAAxBD,EAASE,OAAOlc,IACxBic,GASR,IAAIE,GAAiBH,EAAShQ,UAAUhM,EAAM,EAAEgc,EAASjd,OAAS,EAGlE,IAFAid,EAAWA,EAAShQ,UAAU,EAAEhM,EAAM,GAElCxD,EAAM4f,WAAWJ,EAAU,cAAe,CAM1C,GALAL,EAAItD,oBAAsBO,GAE1B+C,EAAInD,SAAW2D,EAEf/c,EAAOwY,EAAiB+D,EAAInD,SAAU5C,GACxB,OAATxW,EAAe,CAChBuc,EAAIvc,KAAOA,CACX,UAGJ,GADAA,EAAOya,EAAkB8B,EAAInD,SAAU5C,GACzB,OAATxW,EAAe,CAChBuc,EAAIvc,KAAOA,CACX,UAGJuc,EAAIvc,KAAO,IACX,UAGAuc,EAAIU,WAAaF,EAKzB,GAAIG,EAAoBN,GAAW,CAC/BL,EAAInD,SAAWwD,EACfL,EAAIvc,KAAO,KACXuc,EAAItD,oBAAsBC,EAC1B,UAGJ,GAAIiE,GAAYC,EAA6B5G,GAGzC6G,EAAYC,EAAgBH,EAAUE,UAAWT,EACrD,IAAmB,OAAdS,EAAoB,CACrBd,EAAInD,SAAWiE,EAAUE,WACzBhB,EAAIvc,KAAOwY,EAAkB+D,EAAInD,SAAU5C,GAC3C+F,EAAIvd,KAAO4d,EACXL,EAAItD,oBAAsBI,EAE1B,UAIJ,GAAImE,GAAYC,EAAgBN,EAAUK,UAAWZ,EACrD,IAAmB,OAAdY,EAAoB,CACrBjB,EAAInD,SAAWoE,EAAUD,WACzBhB,EAAIvc,KAAOwY,EAAkB+D,EAAInD,SAAU5C,GAC3C+F,EAAIvd,KAAO4d,EACXL,EAAItD,oBAAuBM,EAE3B,eAMD,CAEH,GAAInc,EAAMsgB,SAASd,EAAU,YAAeL,EAAItD,sBAAwBI,GAAmB,CAEvFkD,EAAItD,oBAAsBM,EAE1B,UAIJ,GAA8B,KAA1BqD,EAAS5R,QAAQ,KAAa,CAI9B,GAFAuR,EAAInD,SAAWwD,EACf5c,EAAOwY,EAAiB+D,EAAInD,SAAU5C,GACxB,OAATxW,EAAe,CAChBuc,EAAIvc,KAAOA,CACX,UAGJ,GADAA,EAAOya,EAAkB8B,EAAInD,SAAU5C,GACzB,OAATxW,EAAe,CAChBuc,EAAIvc,KAAOA,CACX,WAOR,GAAKuc,EAAItD,sBAAwBI,IAAoBkD,EAAItD,sBAAwBM,GAAmB,CAChG,GAAI1M,GAAWyL,EAAeiE,EAAIvc,KAAK6M,SAAU+P,EACjD,IAAiB,OAAb/P,EAAmB,CAKnB,GAHA0P,EAAInD,SAAWvM,EAAS7M,KAGpB5C,EAAM4f,WAAWnQ,EAAS7M,KAAM,cAAe,CAC/Cuc,EAAItD,oBAAsBO,EAC1B,IAAImE,GAAS9Q,EAAS7M,KAAK4M,UAAU,GAAKC,EAAS7M,KAAKL,OAAS,EACjE4c,GAAInD,SAAWuE,EACfpB,EAAIvc,KAAOya,EAAkBkD,EAAOnH,GACpC+F,EAAItD,oBAAsBO,OAE1B+C,GAAIvc,KAAOya,EAAkB5N,EAAS7M,KAAMwW,GAC5C+F,EAAItD,oBAAsBS,EAG9B6C,GAAIvd,KAAO4d,EAGf,SAGJ,GAAiB,WAAbA,EAAuB,CACvBL,EAAIqB,UAAYC,EAChB,UACG,GAAIzgB,EAAMsgB,SAASd,EAAU,mBAAoB,CACpDL,EAAIqB,UAAYE,EAChB,UACG,GAAI1gB,EAAMsgB,SAASd,EAAU,UAAW,CAC3CL,EAAIqB,UAAYG,EAChB,UACG,GAAI3gB,EAAMsgB,SAASd,EAAU,iBAAkB,CAClDL,EAAIqB,UAAYI,EAChB,YAMZ,MAAOzB,GAaX,QAASvD,GAAkBlY,EAAM0V,GAC7B,GAAIyH,GAAcnd,EAAKgY,EACvB,KAAKmF,GAAsC,gBAAhBA,GACvB,MAAO,KAGX,IAAIC,GAAgBD,EAAY1D,YAAY,IAC5C,IAAsB,KAAlB2D,EACA,MAAO1G,GAAoBmC,GAG/B,IAAIiD,GAAWqB,EAAYrR,UAAUsR,EAAgB,EACrD,OAAO7B,GAAwBO,EAASpG,GAO5C,QAAS8D,GAAgBxZ,EAAMqd,GAE3B,GAAIC,GAEApe,EADAqe,EAAaF,EAAYnV,IAAI,GAAGsV,WAGpC,IADAF,EAAoB,IACK,GAArBC,EAAW1e,OACXK,EAAOsY,EAAe6F,EAAYtR,SAAUwR,EAAW,GAAGrf,MAAMgB,KAChEoe,GAAqBnD,EAAcna,EAAKud,EAAW,GAAGrf,MAAOgB,OAG7D,KAAK,GADDue,IAAQ,EACHtgB,EAAI,EAAGA,EAAIogB,EAAW1e,OAAQ1B,IAC9BsgB,EAGDA,GAAQ,EAFRH,GAAqB,IAIzBpe,EAAOsY,EAAe6F,EAAYtR,SAAUwR,EAAWpgB,GAAGe,MAAMgB,KAChEoe,GAAqBC,EAAWpgB,GAAGe,KAAO,IAAMic,EAAcna,EAAKud,EAAWpgB,GAAGe,MAAOgB,EAIhG,OADAoe,IAAqB,IAOzB,QAASlB,GAAoB9D,GACzB,MAAOoF,GAAmBpF,IAAaxB,GAAmBwB,IAAavB,GAAkBuB,GA78B7F,GAAIhc,GAAeN,EAAQ,iBACvBuZ,EAAevZ,EAAQ,mBACvB2hB,EAAe3hB,EAAQ,gBAEvB4hB,EAAU,QACVC,EAAwBD,EAAU,IAClC5F,EAAuB,IAAM6F,EAAwB,UAErD7T,EAAW1N,EAAM0N,SACjBqE,EAAU/R,EAAM+R,QAChBjE,EAAU9N,EAAM8N,QAEhBwM,EAAWta,EAAMsa,SAEjB/P,EAAavK,EAAMuK,WACnBgP,EAAgBvZ,EAAMuZ,cACtB0E,EAA8Bje,EAAMie,4BAIpCuD,GADuBvI,EAAWwI,qBACjBxI,EAAWuI,gBAC5BlD,EAAoBrF,EAAWqF,kBAI/BvE,GAHuBd,EAAWyI,qBACxBzI,EAAW1H,QACT0H,EAAW0I,UACT1I,EAAWc,aACzBsB,EAAmBpC,EAAWoC,iBAC9BgC,EAAoBpE,EAAWoE,kBAC/BjC,EAAmBnC,EAAWmC,iBAC9BiF,EAAkBpH,EAAWoH,gBAC7BH,EAAkBjH,EAAWiH,gBAC7BF,EAA+B/G,EAAW+G,6BAC1C9E,EAAiBjC,EAAWiC,eAC5BlJ,EAA2BiH,EAAWjH,yBACtCkG,EAAae,EAAWf,WAExBkJ,EAAqBnI,EAAWmI,mBAChC5G,GAAqBvB,EAAWuB,mBAChCC,GAAoBxB,EAAWwB,kBAE/BwB,GAAmB,IACnBE,GAAoB,IACpBG,GAAuB,IACvBF,GAAyB,IAEzBG,GAAqB,IACrB+C,GAA8B,MAC9BD,GAA+B,OAE/BvD,GAAoB,IAGpB2E,GAAiB,IACjBC,GAA0B,KAC1BC,GAAiB,IACjBC,GAAyB,KAEzBgB,GAAgB,mBAChBjI,GAAkB0H,EAAa/M,YAAYsN,IAE3CzH,IAA4B,YAAa,eAkEzCqE,GAAa,qCAUb1E,GAAe,SAAU9N,EAAG9C,GAS5B,MAAIA,IAA6B,aAApBA,EAAM4V,UACR0C,EAAetY,GAEfA,GA6zBXkJ,GAAciP,EAAa9P,QAAQ2H,EAAYQ,EAAgBkI,GAAe5P,EAClFI,IAAYkB,gBAAiB,EAE7B9T,EAAQoc,kBAAoBA,EAC5Bpc,EAAQ4S,YAAcA,GACtB5S,EAAQ0Z,WAAaA,EACrB1Z,EAAQka,eAAiBA,EACzBla,EAAQ+e,oBAAsBA,GAAuB5O,SAAa,SAASnQ,EAASC,EAAQC,GAC5F,YAwCA,SAASmiB,GAAcC,EAAYC,EAAU3N,EAAM4N,GAE/C,OACIF,WAAYA,EACZC,SAAUA,EACV3N,KAAMA,IAAQ,EACd4N,GAAIA,GAgTZ,QAASC,GAAW7N,GAEhB,IAAKA,EACD,MAAOA,EAGX,IAAIA,EAAK7R,OAAS,EAAG,CACjB,GAAI2f,GAAW9N,EAAK3F,OAAO,EAAG,EAC9B,OAAIyT,KAAaA,EAASC,cACf/N,EAGJA,EAAKsL,OAAO,GAAG0C,cAAgBhO,EAAK3F,OAAO,GAGtD,MAAO2F,GAAKsL,OAAO,GAAG0C,cAQ1B,QAASC,GAAeC,EAAcC,GAElC,GAAIR,GAAWO,EAAaP,QAC5B,KAAKA,EACD,MAAO,KAGX,IAAIlhB,GAAGyB,CACP,KAAKzB,EAAI,EAAGyB,EAAMyf,EAASxf,OAAYD,EAAJzB,EAASA,IAAK,CAC7C,GAAI2hB,GAAcT,EAASlhB,GACvB4hB,GAAmB,CAMvB,IALmD,MAA/CD,EAAY9C,OAAO8C,EAAYjgB,OAAS,KACxCkgB,GAAmB,EACnBD,EAAcA,EAAY/T,OAAO,EAAG+T,EAAYjgB,OAAS,IAGzDggB,IAAkBC,EAAa,CAC/B,GAAIhF,GAAeyE,EAAWO,EAC9B,QAAS1U,QAAS2U,EAAkBjF,aAAcA,IAI1D,MAAO,MAOX,QAASkF,GAAeC,GAEpB,MAAOA,KAAUC,EAOrB,QAASC,GAA4B9X,GAEjC,GAAI+X,GAAYC,EAAahY,GACzB4X,EAAQK,EAAgBjY,GACxBkY,EAAgBC,EAAOnB,SAASe,EACpC,KAAKG,EACD,MAAO,KAGX,IAAIA,EAAcjB,IACd,GAAIW,IAAUM,EAAcjB,GACxB,MAAO,UAER,KAAKU,EAAeC,GACvB,MAAO,KAGX,IAAItgB,MACAyf,EAAamB,EAAcnB,cAoD/B,OAnDAqB,GAAcpY,EAAS,SAAUmP,GAE7B,GAAI4I,GAAYC,EAAa7I,GACzByI,EAAQK,EAAgB9I,GACxBhR,EAAQgR,EAAUhR,KAGtB,IAAIyZ,IAAUS,EAAd,CAMA,GAAIC,GAAa,IACbX,GAAeC,IAAoB,OAAVA,EACzBU,EAAa,GACNV,IAAUW,IACjBD,EAAa,MAGE,OAAfA,IACAA,GAAcP,EAEVhX,EAASgW,EAAYuB,KACrBhhB,EAAK4f,EAAWa,IAAc5Z,OAM1Cqa,EAAiBxY,EAAS,SAAUyY,GAChC,GAAIV,GAAYC,EAAaS,GACzBC,EAAcpB,EAAeY,EAAeH,EAChD,IAAIW,EACA,GAAIA,EAAY3V,QAAS,CACrB,GAAI1L,GAAMC,EAAKohB,EAAYjG,aACtBpb,KACDA,KACAC,EAAKohB,EAAYjG,cAAgBpb,GAErCA,EAAI6I,KAAK4X,EAA4BW,QAErCnhB,GAAKohB,EAAYjG,cAAgBqF,EAA4BW,KAKrEP,EAAc7O,OACd/R,EAAK+R,KAAOsP,EAAa3Y,IAGtB1I,EAQX,QAAS6P,GAAeX,EAAS6C,GAE7B,GAAIuP,GAAMC,EAASxP,GACfyP,EAAOC,EAAqBH,EAChC,OAAOd,GAA4BgB,IAAShiB,OAzehD,GAAI7B,GAAWN,EAAQ,iBACnBqkB,EAAYrkB,EAAQ,eACpB4R,EAAkB5R,EAAQ,gBAK1BoM,EAAW9L,EAAM8L,SAEjBqX,GADenjB,EAAM+N,aACLgW,EAAOZ,eACvBI,EAAmBQ,EAAOR,iBAC1BO,EAAuBC,EAAOD,qBAC9BJ,EAAeK,EAAOL,aACtBX,EAAegB,EAAOhB,aACtBC,EAAkBe,EAAOf,gBAEzBI,GADQW,EAAOC,MACLD,EAAOX,SACjBQ,EAAWG,EAAOH,SAElBK,EAAMF,EAAOG,KAAO,6BACpBC,EAAQF,EAAM,KACdG,EAASD,EAAQ,QACjBvB,EAASuB,EAAQ,OACjBb,EAAiBa,EAAQ,YACzBnS,EAA2BV,EAAaU,yBAExCqS,EAAe,kBAsBfnB,GACAnB,UACIuC,OAAQzC,GACO,OAAQ,UAAW,kBACrB,aAAc,aAAc,gBAEzC0C,aAAc1C,GACC,OAAQ,SAAU,YAAa,gBAE9C2C,WAAY3C,GACG,OAAQ,YAAa,SAAU,OAAQ,OAAQ,iBAAkB,UAAW,WAAY,aAAc,QAAS,OAAQ,MAAO,SAAU,YAAa,iBAAkB,yBAA0B,OAAQ,eAAgB,WAC3N,UAAW,QAAS,QAAS,kBAAmB,WAAY,YAAa,cAAe,SAAU,QAAS,OAAQ,UAAW,aAAc,OAAQ,MAAO,OAAQ,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,kBAAmB,SAAU,QAAS,cAAe,MAAO,QAAS,kBAAmB,2BAA4B,QAAS,0BAA2B,QAAS,gBAAiB,UAAW,UAAW,gBAEla4C,eAAgB5C,EACF,KACF,MACJ,GAER6C,YAAa7C,GACE,SAAU,cACZ,gBAEb8C,MAAO9C,GACQ,aACF,UAAW,QAAS,kBAAmB,gBAEpD+C,IAAK/C,EACS,KACF,MACJ,GAERgD,GAAIhD,EACU,KACF,MACJ,GAERiD,IAAKjD,EACS,KACF,MACJ,GAERkD,GAAIlD,EACU,KACF,MACJ,GAERmD,GAAInD,EACU,KACF,MACJ,GAERoD,GAAIpD,EACU,KACF,MACJ,GAERqD,GAAIrD,EACU,KACF,MACJ,GAERsD,GAAItD,EACU,KACF,MACJ,GAERuD,GAAIvD,EACU,KACF,MACJ,GAERwD,OAAQxD,EACM,KACF,MACJ,GAERyD,KAAMzD,EACQ,KACF,MACJ,GAER0D,KAAM1D,GACS,SACF,QAAS,gBAEtB2D,WAAY3D,EACE,MACD,UAAW,QAAS,QAAS,kBAAmB,WAAY,YAAa,cAAe,SAAU,QAAS,OAAQ,UAAW,aAAc,OAAQ,MAAO,OAAQ,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,kBAAmB,SAAU,QAAS,cAAe,MAAO,QAAS,kBAAmB,2BAA4B,QAAS,0BAA2B,QAAS,gBAAiB,UAAW,YAEvZ4D,YAAa5D,GACE,OAAQ,WAAY,WAAY,aAClC,YAAa,sBAAuB,gBAEjDpD,KAAMoD,EACQ,KACF,MACJ,GAER6D,eAAgB7D,EACF,KACF,MACJ,GAER8D,QAAS9D,EACK,KACF,MACJ,GAER+D,SAAU/D,EACI,KACF,MACJ,GAERgE,gBAAiBhE,GACF,OAAQ,YACV,aAAc,aAAc,gBAAiB,kBAAmB,gBAE7EiE,UAAWjE,GACI,OAAQ,aAAc,6BACxB,6BAA8B,gBAE3CkE,WAAYlE,GACG,OAAQ,WAAY,WAAY,WAAY,cAC9C,OAAQ,YAAa,sBAAuB,gBAEzDmE,WAAYnE,EACE,KACF,MACJ,GAERoE,SAAUpE,GACK,OAAQ,iBAAkB,YAC5B,YAEbqE,MAAOrE,EACO,KACF,MACJ,GAERsE,SAAUtE,GACK,OAAQ,UAAW,eAAgB,kBACrC,aAAc,aAAc,gBAEzCuE,eAAgBvE,GACD,OAAQ,WAAY,YAAa,2BAA4B,gBAE5EwE,KAAMxE,EACQ,KACF,MACJ,GAERyE,GAAIzE,EACU,MACD,QAAS,UAAW,gBAEjC0E,IAAK1E,EACS,KACF,MACJ,GAER2E,KAAM3E,GACS,OAAQ,YAAa,YAAa,QAAS,UAAW,OAAQ,eAAgB,gBAChF,UAEb4E,IAAK5E,EACS,MACD,iBAEb6E,eAAgB7E,GACD,SACF,UAAW,QAAS,QAAS,kBAAmB,WAAY,YAAa,cAAe,SAAU,QAAS,OAAQ,UAAW,aAAc,OAAQ,MAAO,OAAQ,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,kBAAmB,SAAU,QAAS,cAAe,MAAO,QAAS,kBAAmB,2BAA4B,QAAS,0BAA2B,QAAS,gBAAiB,UAAW,UAAW,gBAEla8E,wBAAyB9E,GACV,SACF,UAAW,QAAS,QAAS,kBAAmB,WAAY,YAAa,cAAe,SAAU,QAAS,OAAQ,UAAW,aAAc,OAAQ,MAAO,OAAQ,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,kBAAmB,SAAU,QAAS,cAAe,MAAO,QAAS,kBAAmB,2BAA4B,QAAS,0BAA2B,QAAS,gBAAiB,UAAW,YAEvZ+E,OAAQ/E,GACO,OAAQ,UACX,gBAEZgF,mBAAoBhF,GACL,OAAQ,OAAQ,WAAY,UAAW,mBACzC,yBAA0B,YAAa,gBAEpDiF,0BAA2BjF,GACZ,OAAQ,WAEvBkF,uBAAwBlF,EACV,KACF,MACJ,GAERmF,KAAMnF,EACQ,MACD,gBAEboF,SAAUpF,GACK,WACF,gBAEbqF,KAAMrF,EACQ,KACF,MACJ,GAERsF,UAAWtF,GACI,OAAQ,OAAQ,WAAY,YAAa,YAAa,QAAS,SACjE,gBAEbuF,SAAUvF,GACK,OAAQ,OAAQ,WAAY,YAAa,YAAa,QAAS,UAAW,OAAQ,iBACpF,gBAEbwF,aAAcxF,EACA,KACF,MACJ,GAERyF,YAAazF,GACE,OAAQ,UAEvB0F,cAAe1F,GACA,WAAY,SACd,UAAW,QAAS,QAAS,kBAAmB,WAAY,YAAa,cAAe,SAAU,QAAS,OAAQ,UAAW,aAAc,OAAQ,MAAO,OAAQ,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,kBAAmB,SAAU,QAAS,cAAe,MAAO,QAAS,kBAAmB,2BAA4B,QAAS,0BAA2B,QAAS,gBAAiB,UAAW,UAAW,gBAEla2F,OAAQ3F,EACM,MACD,iBAAkB,YAAa,gBAE5C4F,sBAAuB5F,GACR,WAAY,qBAAsB,gBAEjD6F,WAAY7F,GACG,OAAQ,WAAY,YAAa,YAAa,QAAS,SAEtEzO,OAAQyO,EACM,KACF,MACJ,GAER8F,OAAQ9F,GACO,YAAa,UACf,UAAW,eAAgB,cAAe,eAAgB,kBAAmB,cAAe,YAAa,YAAa,QAAS,kBAAmB,gBAE/J+F,UAAW/F,GACI,OAAQ,SACV,6BAA8B,gBAE3CgG,KAAMhG,GACS,OAAQ,OAAQ,WAAY,gBAAiB,YAAa,WAAY,YAAa,YAAa,QAAS,SAC3G,gBAEbiG,UAAWjG,EACG,KACF,MACJ,GAERkG,eAAgBlG,GACD,OAAQ,iBAAkB,YAAa,UAAW,YAAa,QAAS,SAC1E,gBAEbmG,OAAQnG,EACM,MACD,UAAW,QAAS,QAAS,kBAAmB,WAAY,YAAa,cAAe,SAAU,QAAS,OAAQ,UAAW,aAAc,OAAQ,MAAO,OAAQ,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,kBAAmB,SAAU,QAAS,cAAe,MAAO,QAAS,kBAAmB,2BAA4B,QAAS,0BAA2B,QAAS,gBAAiB,UAAW,UAAW,gBAIlaoG,KAAMpG,GACS,YACF,eAAgB,eACrB,EACFuC,GAEN8D,aAAcrG,GACC,0BAA2B,yBAC7B,YACL,EACFuC,GAEN+D,UAAWtG,GACI,QACF,WAAY,sBAAuB,gBAEhDuG,QAASvG,GACM,YAAa,UAE5BwG,mBAAoBxG,GACL,gBAAiB,YAAa,qBA+JrDriB;EAAQoU,gBAAkBtC,EAAaC,QAAQW,EAAgB,KAAMmS,EAAcrS,GAEnFxS,EAAQ0jB,OAASA,EACjB1jB,EAAQyiB,WAAaA,EACrBziB,EAAQ6iB,eAAiBA,EACzB7iB,EAAQqjB,4BAA8BA,EACtCrjB,EAAQ0S,eAAiBA,GAAkBR,IAAQ,SAASlS,EAASC,EAAQC,GAwB7E,QAAS4oB,GAAY7gB,GAEjB,QAASA,EAAQkH,QAA6B,QAAnBlH,EAAQkH,QASvC,QAAS4Z,GAAaC,GAClB,GAAIC,GAAS/f,OAAOggB,SAASC,cAAc,SAC3CF,GAAOG,MAAMC,QAAU,MAEvB,IAAIC,GAAsBN,EAAIxK,QAAQ,KAAM,SAASA,QAAQ,KAAM,UAAUA,QAAQ,KAAM,QACvF+K,EAAO,mDAAwDD,EAAsB,yCAErFjW,EAAOnK,OAAOggB,SAASM,qBAAqB,QAAQ,EAIxD,OAHAnW,GAAKoW,YAAYR,GAEjBS,EAAkBT,EAAQM,GACnBN,EAMX,QAASU,KACL,GAAIzgB,OAAO0gB,eACP,MAAO,IAAI1gB,QAAO0gB,cAEtB,IAAIC,EACJ,IAAI3gB,OAAO4gB,cACP,IACI,MAAO,IAAI5gB,QAAO4gB,cAAc,sBAClC,MAAOtd,GACL,IACI,MAAO,IAAItD,QAAO4gB,cAAc,sBAClC,MAAO5T,GACL2T,EAAY3T,OAIpB2T,IAAcvnB,QAAS,+BAE3B,MAAMunB,GAOV,QAASE,GAAcf,GACnB,MAAkC,KAA3BA,EAAI5a,QAAQ,YACa,IAA5B4a,EAAI5a,QAAQ,aACe,IAA3B4a,EAAI5a,QAAQ,WAOpB,QAAS4b,GAAWhB,GAEhB,IAAKe,EAAcf,GACf,OAAO,CAIX,IAAIiB,GAAW/gB,OAAO+gB,SAClBC,EAAiBD,EAASE,SAAW,KAAOF,EAASG,KAAO,GAChE,OAAwC,KAAhCpB,EAAI5a,QAAQ8b,GAOxB,QAASG,GAAejoB,EAAMkoB,GAC1B,UACWphB,QAAO9G,GAChB,MAAOkD,GACL4D,OAAO9G,GAAQC,OACXioB,IAASC,EAAQ,IACjBA,GAAS,IASrB,QAASC,GAAavB,GAMlB,MALIA,KACAS,EAAkBT,EAAQ,IAC1BA,EAAOwB,WAAWC,YAAYzB,IAG3B,KAOX,QAAS0B,GAAoBC,EAAKtX,GAE9B,GACIjS,GAAGyB,EADH+nB,EAAkBD,EAAIE,wBAAwB7S,MAAM,QAExD,KAAK5W,EAAI,EAAGyB,EAAM+nB,EAAgB9nB,OAAYD,EAAJzB,EAASA,IAC/C,GAAIwpB,EAAgBxpB,GAAI,CACpB,GAAI0pB,GAASF,EAAgBxpB,GAAG4W,MAAM,KACtC3E,GAAQyX,EAAO,IAAMA,EAAO,IASxC,QAASrB,GAAkBT,EAAQM,GAC/B,GAAIyB,GAAiB/B,EAAoB,cAAIA,EAAOgC,cAAc/B,SAAWD,EAAOiC,gBAAgBhC,QACpG8B,GAAcG,OACdH,EAAczX,MAAMgW,GACpByB,EAAcxgB,QAjJlB,GAAIhK,GAAWN,EAAQ,iBAGnBqS,EAAU/R,EAAM+R,QAChBxK,EAAQvH,EAAMuH,MAEdwiB,EAAQ,CA8IZvqB,GAAQ6T,mBACJrE,sBAAuB,YAEvBC,kBAAmB,eAEnBF,qBAAqB,EAQrBtH,QAAS,WAEL,GAAI3D,GAAOC,IAGX,OAAO,UAAS0D,EAAS6E,EAASzH,GAElC,GAGI4jB,GAHApnB,KACA+oB,EAAM,KACNQ,GAAO,CAGXvpB,GAAOuG,MAAQ,WACX6gB,EAASuB,EAAavB,GAClBmC,IAIJA,GAAO,EACHR,IACAA,EAAIxiB,QACJwiB,EAAM,MAGVvlB,GAAQ/C,QAAS,qBAGrB,IASIF,GATAipB,EAAgB,WAChBpC,EAASuB,EAAavB,GACjBmC,IACDA,GAAO,EACPR,EAAM,KACNvlB,GAAQ/C,QAAS,wBAKrB0mB,EAAM/gB,EAAQmH,WACdG,EAAsBgD,EAAQtK,EAAQsH,oBAAsBjL,EAAKiL,qBACjEC,EAAwB+C,EAAQtK,EAAQuH,sBAAuBlL,EAAKkL,uBACpEC,EAAoB8C,EAAQtK,EAAQwH,kBAAmBnL,EAAKmL,kBAChE,KAAKF,GAAuBya,EAAWhB,GAAM,CAiCzC,GA/BA4B,EAAMjB,IACNiB,EAAIU,mBAAqB,WACrB,IAAIF,GAAgB,OAARR,GAAmC,IAAnBA,EAAIW,WAAhC,CAKA,GAAI1U,GAAa+T,EAAI/T,WACjBP,EAAasU,EAAIY,MACF,QAAflV,IACAA,EAAa,IACbO,EAAa,aAGjB,IAAIvD,KACJqX,GAAoBC,EAAKtX,EAEzB,IAAIF,IAAahE,WAAY4Z,EAAK1S,WAAYA,EAAYO,WAAYA,EAAYvD,QAASA,EAASD,KAAMuX,EAAIa,aAE9GL,IAAO,EACPR,EAAM,KACFtU,GAAc,KAAqB,KAAdA,EACrBxJ,EAAQsG,GAER/N,GAAQ/C,QAAS,sBAAuB2F,QAASA,EAASmL,SAAUA,MAI5EwX,EAAIO,KAAKljB,EAAQkH,QAAU,MAAO6Z,GAAK,EAAM/gB,EAAQoH,KAAMpH,EAAQqH,UAG/DrH,EAAQqL,QACR,IAAKlR,IAAQ6F,GAAQqL,QACjBsX,EAAIc,iBAAiBtpB,EAAM6F,EAAQqL,QAAQlR,GAK/C6F,GAAQ0jB,YACRf,EAAIgB,QAAU3jB,EAAQ0jB,UACtBf,EAAIiB,UAAYR,GAGpBT,EAAIkB,KAAK7jB,EAAQoL,UACd,CACH,IAAKyV,EAAY7gB,GACb,MAAQ3F,QAAS,yDAGrB,IAAIgoB,GAAOC,CACXA,IAAS,CACT,IAEIwB,GAFAC,EAAW1B,EAAK/Z,WAChB0b,GAAY,CAEhB7pB,GAAO,eAAiB4pB,EACxB9iB,OAAO9G,GAAQ,SAAU8B,GAErB,GADA+kB,EAASuB,EAAavB,IACjBmC,EAAM,CACPa,GAAY,EACZ/iB,OAAOgjB,aAAaH,GACpB1B,EAAejoB,EAAMkoB,GAIjBphB,OAAO4gB,gBACP5lB,EAAOgF,OAAO8J,KAAK6G,MAAM3Q,OAAO8J,KAAKC,UAAU/O,IAInD,IAAIoP,EAMAA,GALC7D,GAA0C,gBAArBA,GAKV0c,eAAgB1c,EAAkBO,UAAU,GAAIoc,gBAAiB,QAJjED,eAAgB,0CAA2CC,gBAAiB,OAQ5FrkB,EAAM,WACFyiB,EAAavB,GACbnc,GAAUuG,KAAMnP,EAAMoS,WAAY,IAAKhD,QAASA,OAM5D,IAAIqY,GAAa1jB,EAAiB,UAAIA,EAAQ0jB,UAAY,IAC1DI,GAAY7iB,OAAOmjB,WAAWhB,EAAeM,EAE7C,IAAIW,GAAoB9c,EAAwB,WAAapN,CACzDqN,KACA6c,GAAqB,IAAM7c,EAG/B,IAAI8c,GAASvD,EAAI5a,QAAQ,IACV,MAAXme,EACAvD,EAAMA,EAAM,IAAMsD,EACXC,IAAWvD,EAAIjmB,OAAS,EAC/BimB,GAAYsD,EAEZtD,EAAMA,EAAM,IAAMsD,EAGtBrD,EAASF,EAAaC,GAG1B,MAAOnnB,QAOf7B,EAAQ8oB,YAAcA,EACtB9oB,EAAQ+pB,cAAgBA,EACxB/pB,EAAQgqB,WAAaA,GAAcwC,WAAe,SAASxsB,EAASC,EAAQC,GAC5E,YA8JA,SAASusB,GAActc,EAAUuc,GAG7B,IAAKvc,EACD,MAAO,KAGX,IAAI7B,GAAQ6B,GAAW,CACnB,GAAI9O,GAAGyB,EAAKjB,CACZ,KAAKR,EAAI,EAAGyB,EAAMqN,EAASpN,OAAYD,EAAJzB,EAASA,IAExC,GADAQ,EAAS4qB,EAActc,EAAS9O,GAAIqrB,GAEhC,MAAO7qB,EAIf,OAAO,MAEP,MAAIsO,GAASwc,aACFF,EAActc,EAASwc,aAAajJ,OAAQgJ,GAGhDA,EAASvc,GAUxB,QAASyc,GAAmBC,EAAIrK,GAc5B,MAVIqK,GADO,IAAPA,EACK,GAEA,IAAM/N,EAAkB+N,EAAGtc,WAAY,GAE5CiS,EAAK,IACM,KAAPqK,IACAA,EAAK,QAETA,GAAM/N,EAAkB0D,EAAGjS,WAAY,IAEpCsc,EAGX,QAASC,GAAyBpjB,GAC9B,MAAO,SAAYA,EAAMqjB,UAAY,KAQzC,QAAS9K,GAAqBvY,GAE1B,GAAqB,gBAAVA,GACP,MAAOA,EAGX,IAAIsjB,GAAYC,EAAiBvjB,GAC7BwjB,EAAShL,EAAqBxY,EAAM6V,SACxC,IAAIyN,GAAwB,MAAXE,EAAgB,CAE7BxjB,EAAQ,GAAIuV,MAAKvV,EAAM8V,UAEvB,IAAI2N,GAAWC,EAAcF,GACzBrO,EAAQnV,EAAM2jB,cAAiBF,EAAS3rB,EAAI2rB,EAASG,EACrD3O,EAAUjV,EAAM0V,gBAAmB+N,EAAS3rB,EAAI2rB,EAASI,CAE7D7jB,GAAM8jB,YAAY3O,EAAOF,OACjBqO,KAERE,EAAS,GAGb,IAAIO,GAAO/jB,EAAMgkB,iBACbC,EAAQjkB,EAAMkkB,cAAgB,EAC9BhP,EAAO,EACC,IAAR6O,IACAA,IAASA,EAAO,GAChB7O,EAAO,IAGX,IAAIiO,GAAKD,EAAmBljB,EAAMmkB,qBAAsBnkB,EAAMokB,KAE9D,OAAOlP,GACHE,EAAkB2O,EAAM,GAAK,IAC7B3O,EAAkB6O,EAAO,GAAK,IAC9B7O,EAAkBpV,EAAMqkB,aAAc,GAAK,IAC3CjP,EAAkBpV,EAAM2jB,cAAe,GAAK,IAC5CvO,EAAkBpV,EAAM0V,gBAAiB,GAAK,IAC9CN,EAAkBpV,EAAMskB,gBAAiB,GACzCnB,EAAKK,EAOb,QAASlL,GAAetY,GAEpB,GAAImjB,GAAKnjB,EAAMmjB,GAEXjO,EAAO,EACF,GAALiO,IACAjO,EAAO,IACPiO,GAAMA,EAGV,IAAIoB,GAAOxqB,KAAKC,MAAMmpB,EAAK,MAC3BA,IAAM,MAAWoB,CACjB,IAAIpP,GAAQpb,KAAKC,MAAMmpB,EAAK,KAC5BA,IAAM,KAAUhO,CAChB,IAAIF,GAAUlb,KAAKC,MAAMmpB,EAAK,IAC9BA,IAAM,IAAQlO,CACd,IAAIuP,GAAUzqB,KAAKC,MAAMmpB,EAAK,IAG9B,OAFAA,IAAgB,IAAVqB,EAECtP,EAAO,IACPE,EAAkBmP,EAAM,GAAK,KAC7BnP,EAAkBD,EAAO,GAAK,IAC9BC,EAAkBH,EAAS,GAAK,IAChCG,EAAkBoP,EAAS,GAC3BtB,EAAmBC,EAAInjB,EAAM8Y,IAAM,IAS9C,QAAS1D,GAAkBpV,EAAOykB,EAAOC,GAErC,IADA,GAAIvsB,GAAS6H,EAAM6G,SAAS,IACrB1O,EAAOkB,OAASorB,GACfC,EACAvsB,GAAU,IAEVA,EAAS,IAAMA,CAIvB,OAAOA,GAOX,QAASqgB,GAAqBiL,GAE1B,MAASA,IAAyB,MAAbA,GAAiC,WAAbA,GAAsC,WAAbA,EAA+BA,EAAN,IAO/F,QAASkB,GAAkB7R,GAEvB,GAAwB,gBAAbA,GAAuB,CAC9B,GAAIpb,GAAMob,EAASpO,QAAQ,IAAK,GAChC,IAAwC,IAApCoO,EAASpO,QAAQ,gBAAwBhN,EAAM,EAC/C,MAAOob,GAASxM,UAAU,GAAI5O,GAGtC,MAAO,MAWX,QAASqR,GAAcxK,EAAS6E,EAASzH,EAAO0M,EAAS7B,EAAYyB,GAEjE,MAAOzB,GAAWjI,QAAQA,EAAS,SAAUmL,GACzC,IACQA,EAASE,SACTsD,EAAiBxD,EAASE,SAGRjR,SAAlB+Q,EAASlP,MAA8C,MAAxBkP,EAASkD,YACxCvE,EAAQlH,KAAKuI,EAAUzB,GAE7B,MAAOrM,GAQL,MAPoBjD,UAAhBiD,EAAI2C,UACJ3C,EAAI2C,QAAUA,GAEG5F,SAAjBiD,EAAI8N,WACJ9N,EAAI8N,SAAWA,OAEnB/N,GAAMC,GAIV,IACIwH,EAAQsG,EAASlP,KAAMkP,GACzB,MAAO9N,GAEL,KADAA,GAAIyO,wBAAyB,EACvBzO,IAEXD,GAOP,QAAS4R,GAAQvN,GAEb,MAAOyY,GAAUzY,IAAU4E,GAAQ5E,EAAM0N,iBAW7C,QAASkX,GAAa5kB,EAAO8S,GAEzB,GAAI+R,GAAU7kB,GAASA,EAAM8kB,SAAW9kB,CACxC,SAAS6kB,GACJ1S,EAAiBW,KAChBA,GAAYlO,GAAQigB,KAAapM,EAAUoM,EAAQ,IAO7D,QAAS1S,GAAiBW,GACtB,MAAOiS,IAAiBC,KAAKlS,GAOjC,QAAS2F,GAAUzY,GAEf,QAASA,GACLoR,GAASpR,KACR4E,GAAQ5E,KACRilB,GAAOjlB,GAOhB,QAASujB,GAAiBvjB,GACtB,MAA4B,uBAApBA,EAAM4V,YAAwC5V,EAAM4V,WAAa5V,EAAM6V,SAOnF,QAASqP,GAAWllB,GAEhB,IAAKA,IAAUyY,EAAUzY,GACrB,OAAO,CAEX,IAAIyG,GAAWzG,EAAMmlB,eACjBtuB,EAAWmJ,EAAMolB,cACrB,QAAQ3e,EAAS/M,QAAU7C,EAASoO,IAOxC,QAASogB,GAAQrlB,GAEb,MAAOyY,GAAUzY,IAAUA,EAAMmlB,YAAc,OAASnlB,GAAMmlB,WAQlE,QAASG,GAAOtlB,EAAO8S,GAEnB,GAAIyS,GAAWvlB,GAASA,EAAM8kB,SAAW9kB,CACzC,OAAO4E,IAAQ2gB,KACTpT,EAAiBW,IAClB2F,EAAU8M,EAAS,IAQ5B,QAASjU,GAAmBwB,GAExB,MAAOlQ,IAAS4iB,GAAmB1S,IACJ,KAA1BA,EAASpO,QAAQ,MAAe9B,GAAS6iB,GAAgB3S,GAQlE,QAASvB,GAAkBuB,GACvB,MAAOlQ,IAAS8iB,GAAkB5S,IACH,KAA1BA,EAASpO,QAAQ,MAAe9B,GAAS+iB,GAAe7S,GASjE,QAAS8S,GAAc5lB,GAEnB,IAAKA,IAAUyY,EAAUzY,GACrB,OAAO,CAEX,IAAIyG,GAAWzG,EAAMmlB,WACjBU,EAAgB7lB,EAAM8lB,eAC1B,QAAQrf,KAAcof,KAAmBA,EAAcE,UAQ3D,QAASlV,GAAY7Q,GAEjB,MAAOilB,IAAOjlB,IACO,gBAAVA,IACU,gBAAVA,IACU,iBAAVA,GAOf,QAASkY,GAAmBpF,GAExB,MAAOlQ,IAASojB,GAAmBlT,GAQvC,QAASmT,GAAuBjmB,EAAOkmB,GAEnC,MAAIhB,GAAWllB,GACJ,WAEPqlB,EAAQrlB,GACD,QAEPslB,EAAOtlB,GACA,OAEPkmB,GAAiBA,EAAcC,aACjB,OAAVnmB,GAA4BrH,SAAVqH,GAAwBslB,EAAOtlB,GAG9C,OAFI,QAIR,KAQX,QAASgS,GAAe3G,EAAY3S,GAEhC,MAAO0tB,IAAK/a,EAAY,SAAU9E,GAC9B,MAAOA,GAAS7N,OAASA,IAUjC,QAAS2tB,GAAiB3tB,EAAM+N,EAAU0K,GAEtC,MAAO,GAAS4R,EAActc,EAAU,SAAUuT,GAC9C,MAAOsM,GAAe5tB,EAAMshB,EAAQ7I,KACnC,KAQT,QAAS6F,GAAgBuP,EAAY7tB,GAEjC,MAAO0tB,IAAKG,EAAY,SAAUxP,GAC9B,MAAOA,GAAUre,OAASA,IASlC,QAASye,GAAgBqP,EAAY9tB,GAEjC,MAAO0tB,IAAKI,EAAY,SAAUtP,GAC9B,MAAOA,GAAUxe,OAASA,IASlC,QAASyb,GAAkBzb,EAAM+N,GAE7B,MAAO4f,GAAiB3tB,EAAM+N,EAAU,eAQ5C,QAASyL,GAAiBxZ,EAAM+N,GAE5B,MAAO4f,GAAiB3tB,EAAM+N,EAAU,cAQ5C,QAASqQ,GAA6BrQ,GAElC,MAAOsc,GAActc,EAAU,SAAUuT,GACrC,MAAI5I,IAAS4I,EAAOyM,iBACTzM,EAAOyM,gBADlB,SAWR,QAASC,GAAsBhuB,EAAM+N,GAEjC,MAAO4f,GAAiB3tB,EAAM+N,EAAU,mBAQ5C,QAASkgB,GAAqBC,EAAiBluB,GAC3C,MAAO0tB,IAAKQ,EAAiB,SAAUC,GACnC,MAAOA,GAAenuB,OAASA,IASvC,QAASouB,GAA6BC,EAAoBtgB,GAEtD,GAAItO,GAAS,IACb,IAAI4uB,EAAoB,CACpB,GAAIC,GAAMD,EAAmBZ,aACzBc,EAAclE,EAActc,EAAU,SAAUuT,GAEhD,GAAIkN,GAAWC,EAAgBnN,EAAOoN,UAAWJ,GAC7CK,EAAerN,EAAOiN,WAC1B,IAAIC,GAAYG,EAAc,CAC1B,GAAI1vB,GAAGyB,CACP,KAAKzB,EAAI,EAAGyB,EAAMiuB,EAAahuB,OAAYD,EAAJzB,EAASA,IAC5C,GAAI0vB,EAAa1vB,GAAGe,OAASwuB,EACzB,MAAOG,GAAa1vB,GAIhC,MAAO,OAGX,IAAIsvB,EAAa,CACb,GAAIvvB,GAAMuvB,EAAYvvB,IAAI,EACtBA,GAAI4vB,OAASP,EAAmBQ,SAChC7vB,EAAMuvB,EAAYvvB,IAAI,IAG1BS,EAAST,EAAIgC,MAGrB,MAAOvB,GAUX,QAASqvB,GAAkCT,EAAoBU,EAAqBhhB,GAEhF,GAAIsgB,EAAoB,CACpB,GAAIC,GAAMD,EAAmBZ,aACzBuB,EAAiB3E,EAActc,EAAU,SAAUuT,GAEnD,IAAK,GADD2N,GAAa3N,EAAOyM,gBACf9uB,EAAI,EAAGA,EAAIgwB,EAAWtuB,OAAQ1B,IAAK,CACxC,GAAIiwB,GAAkBD,EAAWhwB,GAAG+vB,cACpC,IAAIE,EACA,IAAK,GAAIC,GAAI,EAAGA,EAAID,EAAgBvuB,OAAQwuB,IACxC,GAAID,EAAgBC,GAAGZ,aAAeD,EAClC,MAAOY,GAAgBC,GAKvC,MAAO,OAEX,IAAIH,GAAkBA,EAAehwB,IAAI,IAAMgwB,EAAehwB,IAAI,GAC9D,MAAQgwB,GAAehwB,IAAI,GAAGqf,WAAa0Q,EAAuBC,EAAehwB,IAAI,GAAGqf,UAAY2Q,EAAehwB,IAAI,GAAGqf,UAGlI,MAAO,MAQX,QAAS+Q,GAAiBC,EAAethB,GAErC,GAAIuhB,GAAOjF,EAActc,EAAU,SAAUuT,GACzC,GAAInD,GAAYmD,EAAOyM,gBACnBF,EAAa1P,EAAUE,SAC3B,IAAIwP,EACA,IAAK,GAAIsB,GAAI,EAAGA,EAAItB,EAAWltB,OAAQwuB,IACnC,GAAItB,EAAWsB,GAAGnvB,MAAQqvB,EACtB,OAAShR,UAAWwP,EAAWsB,GAAII,cAAepR,EAAUne,KAAMmuB,eAAgBhQ,EAAUgQ,eAIxG,OAAO,OAGX,OAAOmB,GAQX,QAASb,GAAgBrO,EAAIzE,GAEzB,MAA6B,KAAzBA,EAAS3P,QAAQoU,IAA4C,MAA/BzE,EAASmC,OAAOsC,EAAGzf,QAC1Cgb,EAAS9O,OAAOuT,EAAGzf,OAAS,GAGhC,KASX,QAASitB,GAAe5tB,EAAMshB,EAAQ7I,GAElC,GAAIzY,GAAQshB,EAAQ,CAEhB,GAAIkN,GAAWC,EAAgBnN,EAAOoN,UAAW1uB,EACjD,IAAIwuB,EACA,MAAOd,IAAKpM,EAAO7I,GAAO,SAAUhY,GAChC,MAAOA,GAAKT,OAASwuB,IAIjC,MAAO,MAQX,QAASlY,GAAWkZ,EAAMC,GAEtB,GAAID,IAASC,EACT,MAAOD,EAUX,KAAK,GAPDE,GAAYF,EAAK3Z,MAAM,KACvB8Z,EAAaF,EAAM5Z,MAAM,KAEzBnV,EAAOgvB,EAAU/uB,QAAUgvB,EAAWhvB,OACtC+uB,EAAU/uB,OACVgvB,EAAWhvB,OAEN1B,EAAI,EAAOyB,EAAJzB,EAASA,IAAK,CAC1B,GAAI2wB,GAAcF,EAAUzwB,IAAM0J,GAAW+mB,EAAUzwB,IACnD4wB,EAAeF,EAAW1wB,IAAM0J,GAAWgnB,EAAW1wB,GAC1D,IAAI2wB,EAAcC,EACd,MAAOL,EAEX,IAAkBK,EAAdD,EACA,MAAOH,IAkCnB,QAASjb,GAAiBtD,GAEtB,IAAK,GAAIlR,KAAQkR,GAAS,CACtB,GAAI4e,GAAY9vB,EAAKwgB,cACjBuP,EAAaC,GAAcF,EAC/B,IAAIC,GAAc/vB,IAAS+vB,EAAY,CACnC,GAAIhwB,GAAMmR,EAAQlR,SACXkR,GAAQlR,GACfkR,EAAQ6e,GAAchwB,IASlC,QAASkwB,GAAU5W,GAEf,MAA6B,iBAAlBA,GACAA,EAGqB,gBAAlBA,IAA8D,SAAhCA,EAAcmH,cAkB9D,QAAS0P,GAAyB5oB,EAAO6oB,EAAYC,GAGjD,GAAIjc,GAAQkc,GAAgB9b,KAAKjN,GAC7BwjB,EAAS,EAAUhL,EAAqB3L,EAAM,IAAM,IAExD,KAAKA,IAAWgc,GAAyB,MAAXrF,EAAiB,CAC3C,GAAIsF,EACA,MAAO,KAEX,OAAQlwB,QAAS,2BAIrB,GAAImrB,GAAO1iB,GAAWwL,EAAM,GAChB,IAARkX,GACAA,GAIJ,IAAIZ,GAAKtW,EAAM,GACXiM,EAAK,CACT,IAAKqK,EAEE,CACH,GAAIA,EAAG9pB,OAAS,EAAG,CACf,GAAIyvB,EACA,MAAO,KAEX,OAAQlwB,QAAS,oDAGrBkgB,EAAK1D,EAAkB+N,EAAG7c,UAAU,GAAI,GAAG,GAC3C6c,EAAK/N,EAAkB+N,EAAG7c,UAAU,EAAG,GAAI,GAAG,GAE9C6c,EAAK9hB,GAAW8hB,GAChBrK,EAAKzX,GAAWyX,OAbhBqK,GAAK,CAiBT,IAAIhO,GAAQ9T,GAAWwL,EAAM,IACzBoI,EAAU5T,GAAWwL,EAAM,IAC3B2X,EAAUnjB,GAAWwL,EAAM,KAAO,CACtC,IAAe,MAAX2W,EAAgB,CAGhB,GAAIC,GAAWC,EAAcF,GACzBwF,GAAcvF,EAAU,CAC5BtO,IAASsO,EAASG,EAAIoF,EACtB/T,GAAWwO,EAASI,EAAImF,EAI5B,GAAI7wB,GAAS,GAAIod,KAQjB,IAPApd,EAAO8wB,eACHlF,EACA1iB,GAAWwL,EAAM,IAAM,EACvBxL,GAAWwL,EAAM,KAErB1U,EAAO2rB,YAAY3O,EAAOF,EAASuP,EAASrB,GAExCtqB,MAAMV,EAAO2d,WAAY,CACzB,GAAIgT,EACA,MAAO,KAEX,OAAQlwB,QAAS,2BAYrB,MATIiwB,KACA1wB,EAAOyd,UAAY,qBACnBzd,EAAO0d,SAAW2N,GAGlB1K,IACA3gB,EAAOisB,KAAOtL,GAGX3gB,EAQX,QAASoc,GAAUxC,EAAe+W,GAC9B,GAAIjc,GAAQkF,EAAcxD,MAAM,IAEhC,OAAoB,IAAhB1B,EAAMxT,QAAeyvB,EACd,KAEJ,GAAIvT,MACPlU,GAAWwL,EAAM,IACjBxL,GAAWwL,EAAM,IAAM,EACvBxL,GAAWwL,EAAM,GACjB,EAAE,EAAE,EAAE,IAYd,QAASqc,GAAenX,GACpB,GAAIlF,GAAQsc,GAAiBlc,KAAK8E,EAGlC,QACI6R,EAAKviB,GAAWwL,EAAM,IACtBgX,EAAKxiB,GAAWwL,EAAM,IACtB9R,EAAKsG,GAAWwL,EAAM,IACtBsW,GAAM9hB,GAAWwL,EAAM,KAa/B,QAAS2H,GAAoBzC,EAAe+W,GAGxC,MAAOF,GAAyB7W,GAAe,EAAM+W,GAsBzD,QAASrU,GAAc2U,GAEnB,GAAIvc,GAAQwc,GAAYpc,KAAKmc,EAE7B,IAAc,OAAVvc,EACA,MAAQjU,QAAS,0BAGrB,IAAI0wB,GAAQzc,EAAM,IAAM,IACpB0c,EAAS1c,EAAM,IAAM,IACrB0X,EAAOljB,GAAWwL,EAAM,IAAM,GAC9BsI,EAAQ9T,GAAWwL,EAAM,IAAM,GAC/BoI,EAAU5T,GAAWwL,EAAM,IAAM,GACjC2X,EAAUgF,WAAW3c,EAAM,IAAM,EAErC,IAAc,MAAVyc,GAA4B,MAAXC,EACjB,MAAQ3wB,QAAS,8BAGrB,IAAIuqB,GAAKtW,EAAM,GACXiM,EAAK,CACT,IAAKqK,EAEE,CACH,GAAIA,EAAG9pB,OAAS,EACZ,MAAQT,QAAS,kDAGrBkgB,GAAK1D,EAAkB+N,EAAG7c,UAAU,GAAI,GAAG,GAC3C6c,EAAK/N,EAAkB+N,EAAG7c,UAAU,EAAG,GAAI,GAAG,GAE9C6c,EAAK9hB,GAAW8hB,GAChBrK,EAAKzX,GAAWyX,OAVhBqK,GAAK,CAaTA,IAAgB,IAAVqB,EAA2B,IAAVvP,EAA0B,KAARE,EAAyB,MAAPoP,EAE1C,MAAb1X,EAAM,KACNsW,GAAMA,EAGV,IAAIhrB,IAAWgrB,GAAIA,EAAIvN,UAAW,WAKlC,OAHIkD,KACA3gB,EAAO2gB,GAAKA,GAET3gB,EAOX,QAASurB,GAAcD,GAEnB,GAAIuF,GAAYvF,EAASnd,UAAU,EAAG,EACtC0iB,GAA2B,MAAdA,EAAqB,EAAI,EAEtC,IAAIS,GAAcpoB,GAAWoiB,EAASnd,UAAU,IAC5CojB,EAAgBroB,GAAWoiB,EAASnd,UAAUmd,EAAS/e,QAAQ,KAAO,GAC1E,QAAS5M,EAAGkxB,EAAWpF,EAAG6F,EAAa5F,EAAG6F,GAQ9C,QAASzgB,GAAe1K,EAAS8J,EAASJ,GAGjC1J,EAAQkH,SACTlH,EAAQkH,OAAS,OAGhBlH,EAAQqL,QAGTsD,EAAiB3O,EAAQqL,SAFzBrL,EAAQqL,WAKmBjR,SAA3B4F,EAAQqL,QAAQ+f,SAChBprB,EAAQqL,QAAQ+f,OAASthB,EAAQ0B,QAGjCvF,EAASjG,EAAQ/D,OAA0B7B,SAAjB4F,EAAQoL,MAClCtB,EAAQwB,MAAMtL,EAAS0J,GAGtBzD,EAASjG,EAAQqL,QAAQ,uBAC1BrL,EAAQqL,QAAQ,oBAAsBvB,EAAQyB,uBAAyB,OAGrDnR,SAAlB4F,EAAQqrB,QACRrrB,EAAQqrB,OAAQ,GAYxB,QAASC,GAAiB1wB,EAAM2wB,EAAO9G,GAEnC,GAAI7pB,GAAwB,gBAATA,GACf,IAAK,GAAIT,KAAQS,GAAM,CACnB,GAAI6G,GAAQ7G,EAAKT,GACbP,EAAS0xB,EAAiB7pB,EAAOtH,EAAMsqB,EAC3C7qB,GAAS6qB,EAAStqB,EAAMP,EAAQ2xB,GAC5B3xB,IAAW6H,IACGrH,SAAVqH,QACO7G,GAAKT,GAEZS,EAAKT,GAAQP,GAM7B,MAAOgB,GASX,QAAS4wB,GAAS5wB,EAAM6pB,GAEpB,MAAOA,GAAS,GAAI6G,EAAiB1wB,EAAM,GAAI6pB,IAtoCnD,GAAIlsB,GAAWN,EAAQ,iBAGnBgO,EAAW1N,EAAM0N,SACjB5B,GAAW9L,EAAM8L,SACjBwjB,GAAOtvB,EAAMsvB,KACbxhB,GAAU9N,EAAM8N,QAChBqgB,GAASnuB,EAAMmuB,OACf7T,GAAWta,EAAMsa,SACjB/P,GAAavK,EAAMuK,WAUnB2oB,GAAmB,SAAUhqB,EAAOyG,GACpC,GAAIwjB,IAAkBjqB,GAASA,EAAMmlB,gBAAmBzrB,IACxD,OAAOuwB,KAAkBxjB,EAAWA,EAAS/M,KAAO,OAGpDwwB,GAAM,OACNC,GAAcD,GAAM,UACpBE,GAAWF,GAAM,OACjBG,GAAYH,GAAM,QAClBI,GAAYJ,GAAM,QAClBK,GAAYL,GAAM,QAClBM,GAAYN,GAAM,QAClBO,GAAaP,GAAM,SACnBQ,GAAaR,GAAM,SACnBS,GAAcT,GAAM,UACpBU,GAAaV,GAAM,SAEnBW,GAAaX,GAAM,SACnBY,GAAWZ,GAAM,OACjBa,GAAqBb,GAAM,iBAC3Bc,GAAed,GAAM,WACrBe,GAAWf,GAAM,OACjBgB,GAAgBhB,GAAM,OAEtBiB,GAAY,YACZC,GAAgBlB,GAAMiB,GACtBE,GAAsBD,GAAgB,QACtCE,GAA2BF,GAAgB,aAC3CG,GAAwBH,GAAgB,UACxCI,GAA2BJ,GAAgB,aAC3CK,GAA6BL,GAAgB,eAC7CM,GAAgCN,GAAgB,kBAChDO,GAA2BP,GAAgB,aAE3CQ,GAAkBT,GAAY,QAC9BU,GAAuBV,GAAY,aACnCW,GAAoBX,GAAY,UAChCY,GAAuBZ,GAAY,aACnCa,GAAyBb,GAAY,eACrCc,GAA4Bd,GAAY,kBACxCe,GAAuBf,GAAY,aAEnCgB,GAAW,WACXC,GAAelC,GAAMiC,GACrBE,GAAqBD,GAAe,QACpCE,GAA0BF,GAAe,aACzCG,GAAuBH,GAAe,UACtCI,GAA0BJ,GAAe,aACzCK,GAA4BL,GAAe,eAC3CM,GAA+BN,GAAe,kBAC9CO,GAA0BP,GAAe,aAEzCQ,GAAiBT,GAAW,QAC5BU,GAAsBV,GAAW,aACjCW,GAAmBX,GAAW,UAC9BY,GAAsBZ,GAAW,aACjCa,GAAwBb,GAAW,eACnCc,GAA2Bd,GAAW,kBACtCe,GAAsBf,GAAW,aAEjCgB,GAAgB,QAChBC,GAAqB,aACrBC,GAAkB,UAClBC,GAAqB,aACrBC,GAA0B,kBAC1BC,GAAuB,eACvBC,GAA6B,qBAE7BzH,IACA4E,GACAL,GACAC,GACAL,GACAO,GACAD,GACAK,GACAC,GACAC,GACAE,GACAP,GACAM,GACAb,GACAE,GACAD,GACAQ,IAGAnF,IACA0G,GACAC,GACAC,GACAC,GACAC,GACAC,GACAC,GACAC,IAGAhH,IACAwG,GACAS,GACAC,GACAC,GACAC,GACAC,GACAC,GACAC,IAGA1H,IACA4F,GACAC,GACAC,GACAC,GACAC,GACAC,GACAC,GACAC,IAGAlG,IACA0F,GACAS,GACAC,GACAC,GACAC,GACAC,GACAC,GACAC,IAuOAnH,GAAmB,qBAuanB2D,IAEAgF,eAAgB,eAChBC,mBAAoB,mBACpBC,iBAAkB,iBAClBC,gBAAiB,gBAGjB9jB,OAAU,SACV+jB,iBAAkB,iBAClBC,WAAY,WACZC,gBAAiB,gBACjBC,kBAAmB,kBACnBC,mBAAoB,mBACpBC,OAAU,SACVC,aAAc,aACdC,4BAA6B,4BAG7BC,KAAQ,OACR/N,SAAY,WACZgO,iBAAkB,iBAClBC,qBAAsB,qBACtBC,cAAe,eAuCf1F,GAAkB,4EA4GlBI,GAAmB,kCAwCnBE,GAAc,+FAsJlB/yB,GAAQ0zB,iBAAmBA,GAC3B1zB,EAAQu0B,WAAaA,GACrBv0B,EAAQ6zB,YAAcA,GACtB7zB,EAAQ8zB,SAAWA,GACnB9zB,EAAQw0B,SAAWA,GACnBx0B,EAAQy0B,mBAAqBA,GAC7Bz0B,EAAQ00B,aAAeA,GACvB10B,EAAQq0B,YAAcA,GACtBr0B,EAAQo0B,WAAaA,GACrBp0B,EAAQ80B,cAAgBA,GACxB90B,EAAQ+0B,oBAAsBA,GAC9B/0B,EAAQg1B,yBAA2BA,GACnCh1B,EAAQi1B,sBAAwBA,GAChCj1B,EAAQk1B,yBAA2BA,GACnCl1B,EAAQm1B,2BAA6BA,GACrCn1B,EAAQo1B,8BAAgCA,GACxCp1B,EAAQq1B,yBAA2BA,GACnCr1B,EAAQ81B,aAAeA,GACvB91B,EAAQ+1B,mBAAqBA,GAC7B/1B,EAAQg2B,wBAA0BA,GAClCh2B,EAAQi2B,qBAAuBA,GAC/Bj2B,EAAQk2B,wBAA0BA,GAClCl2B,EAAQm2B,0BAA4BA,GACpCn2B,EAAQo2B,6BAA+BA,GACvCp2B,EAAQq2B,wBAA0BA,GAClCr2B,EAAQ20B,SAAWA,GACnB30B,EAAQg0B,UAAYA,GACpBh0B,EAAQi0B,UAAYA,GACpBj0B,EAAQk0B,UAAYA,GACpBl0B,EAAQ+zB,UAAYA,GACpB/zB,EAAQm0B,WAAaA,GACrBn0B,EAAQs0B,WAAaA,GACrBt0B,EAAQ40B,cAAgBA,GACxB50B,EAAQ62B,cAAgBA,GACxB72B,EAAQ82B,mBAAqBA,GAC7B92B,EAAQ+2B,gBAAkBA,GAC1B/2B,EAAQg3B,mBAAqBA,GAC7Bh3B,EAAQi3B,wBAA0BA,GAClCj3B,EAAQk3B,qBAAuBA,GAC/Bl3B,EAAQm3B,2BAA6BA,GACrCn3B,EAAQysB,cAAgBA,EACxBzsB,EAAQiiB,qBAAuBA,EAC/BjiB,EAAQ8sB,yBAA2BA,EACnC9sB,EAAQgiB,eAAiBA,EACzBhiB,EAAQ8e,kBAAoBA,EAC5B9e,EAAQkiB,qBAAuBA,EAC/BliB,EAAQquB,kBAAoBA,EAC5BruB,EAAQyS,cAAgBA,EACxBzS,EAAQiX,QAAUA,EAClBjX,EAAQsuB,aAAeA,EACvBtuB,EAAQ6b,iBAAmBA,EAC3B7b,EAAQmiB,UAAYA,EACpBniB,EAAQitB,iBAAmBA,EAC3BjtB,EAAQ4uB,WAAaA,EACrB5uB,EAAQ+uB,QAAUA,EAClB/uB,EAAQgvB,OAASA,EACjBhvB,EAAQgb,mBAAqBA,EAC7Bhb,EAAQib,kBAAoBA,EAC5Bjb,EAAQsvB,cAAgBA,EACxBtvB,EAAQua,YAAcA,EACtBva,EAAQ4hB,mBAAqBA,EAC7B5hB,EAAQ6d,kBAAoBA,EAC5B7d,EAAQwgB,6BAA+BA,EACvCxgB,EAAQowB,sBAAwBA,EAChCpwB,EAAQ0gB,gBAAkBA,EAC1B1gB,EAAQ6gB,gBAAkBA,EAC1B7gB,EAAQ4b,iBAAmBA,EAC3B5b,EAAQqwB,qBAAuBA,EAC/BrwB,EAAQwwB,6BAA+BA,EACvCxwB,EAAQkxB,kCAAoCA,EAC5ClxB,EAAQgwB,eAAiBA,EACzBhwB,EAAQ0b,eAAiBA,EACzB1b,EAAQ+vB,iBAAmBA,EAC3B/vB,EAAQwxB,iBAAmBA,EAC3BxxB,EAAQ0Y,WAAaA,EACrB1Y,EAAQ2vB,uBAAyBA,EACjC3vB,EAAQ4W,iBAAmBA,EAC3B5W,EAAQqyB,UAAYA,EAGpBryB,EAAQie,UAAYA,EACpBje,EAAQke,oBAAsBA,EAC9Ble,EAAQme,cAAgBA,EACxBne,EAAQ4yB,eAAiBA,EAEzB5yB,EAAQ+K,WAAaA,GACrB/K,EAAQ2S,eAAiBA,EACzB3S,EAAQ6wB,gBAAkBA,EAC1B7wB,EAAQyzB,SAAWA,GAGhB9yB,MAAU,SAASX,EAASC,EAAQC,GASvCF,EAAQo4B,sBAAwB,OAOhCp4B,EAAQ0H,YAAc,SAAUtF,EAAMuF,GAG7BA,IACDA,EAAY3H,EAAQo4B,uBAGN,SAAdzwB,IACAA,EAAa0wB,SAASC,cAAiB,MAAQ,SAGnD,IAAIC,GAAUC,EAAW7wB,EACzB,IAAI4wB,EACA,MAAOA,GAAQE,OAAOr2B,EAG1B,OAAQE,QAAS,yBAA0BF,KAAMA,EAAMuF,UAAWA,IAGtE3H,EAAQq4B,SAAiBA,SAAiBn4B,EAAQ,kBAClDF,EAAQ04B,eAAiBA,eAAiBx4B,EAAQ,wBAClDF,EAAQ24B,YAAiBA,YAAiBz4B,EAAQ,oBAElD,IAAIs4B,IACAI,UAAWF,eACXG,IAAKR,SACLS,OAAQH,YAGZ34B,GAAQw4B,WAAaA,GAKlBK,IAAQ,SAAS74B,EAASC,EAAQC,GACrC,YAmBA,SAAS64B,KACL,GAAIC,IAAaC,EAAG10B,KAAKib,UAAW0Z,EAAG,gBAEvC,KAAK,GAAI92B,KAAQmC,MACby0B,EAAS52B,GAAQmC,KAAKnC,EAE1B,OAAO42B,GAUX,QAASG,GAAmB3sB,EAAG9C,GAC3B,GAAIA,GAAqB,kBAAZA,EAAMwvB,EAAuB,CACtC,GAAIF,GAAW,GAAI/Z,MAAKvV,EAAMuvB,EAC9B,KAAK,GAAI72B,KAAQsH,GACA,MAATtH,GAAyB,MAATA,IAChB42B,EAAS52B,GAAQsH,EAAMtH,GAG/BsH,GAAQsvB,EAEZ,MAAOtvB,GAQX,QAAS0vB,GAAmBz4B,EAAOyL,GAC/B,MAAOzL,GAAMyB,KAAO,MAAQgK,EAQhC,QAASitB,GAAqB14B,EAAOyL,GACjC,MAAOA,GAAIoS,QAAQ7d,EAAMyB,KAAO,MAAO,IAQ3C,QAASi2B,GAASj2B,GACdmC,KAAKnC,KAAOA,EAlEhB,GAAI5B,GAAQN,EAAQ,iBAGhBoS,EAAqB9R,EAAM8R,mBAC3BvK,EAAQvH,EAAMuH,MAEduxB,EAAe,IAoEnBjB,GAASI,OAAS,SAAUr2B,GAExB,GAAIi2B,EAASC,cAET,MADAgB,GAAeA,GAAgBpwB,OAAOowB,aAC/B,GAAIjB,GAASj2B,EAGxB,OAAQE,QAAS,6CAOrB+1B,EAASC,YAAc,WACnB,QAASpvB,OAAOowB,cAWpBjB,EAAS7pB,UAAU+qB,IAAM,SAAUntB,EAAK1C,EAAOoD,EAASzH,GACpDA,EAAQA,GAASd,KAAK2O,YACtB,IAAIvS,GAAQ4D,IACZA,MAAK+H,SAASF,EAAK,SAAUG,GACpBA,EAGDxE,EAAM1C,GAAS/C,QAAS,qBAAsB8J,IAAKA,IAFnDzL,EAAMgM,YAAYP,EAAK1C,EAAOoD,EAASzH,IAI5CA,IAWPgzB,EAAS7pB,UAAU7B,YAAc,SAAUP,EAAK1C,EAAOoD,EAASzH,GAG5D,GAFAA,EAAQA,GAASd,KAAK2O,aAElB9G,YAAeotB,OACfn0B,GAAQ/C,QAAS,oCACd,CACH,GAAIm3B,GAAUL,EAAmB70B,KAAM6H,GACnCstB,EAAgBza,KAAKzQ,UAAUmrB,MACnC,KACI,GAAIC,GAAclwB,CACErH,UAAhBu3B,IAEA3a,KAAKzQ,UAAUmrB,OAASZ,EACxBa,EAAc1wB,OAAO8J,KAAKC,UAAUvJ,IAGxC4vB,EAAaO,QAAQJ,EAASG,GAC9B7xB,EAAM+E,EAASV,EAAK1C,GAExB,MAAOwM,GACY,KAAXA,EAAE4jB,MAA4B,aAAb5jB,EAAE6jB,OACnBhyB,EAAM1C,GAASjD,KAAM,qBAAsBiD,MAAO6Q,IAElDnO,EAAM1C,EAAO6Q,GAGrB,QACI+I,KAAKzQ,UAAUmrB,OAASD,KAWpCrB,EAAS7pB,UAAUnG,MAAQ,SAAUyE,EAASzH,GAE1CA,EAAQA,GAASd,KAAK2O,YACtB,KAEI,IADA,GAAI7R,GAAI,EAAGyB,EAAMw2B,EAAav2B,OACvBD,EAAM,GAASA,EAAJzB,GAAS,CACvB,GAAIo4B,GAAUH,EAAaltB,IAAI/K,GAC3B+K,EAAMitB,EAAqB90B,KAAMk1B,EACjCA,KAAYrtB,GACZktB,EAAaU,WAAWP,GACxB32B,EAAMw2B,EAAav2B,QAEnB1B,IAGR0G,EAAM+E,GAEV,MAAOoJ,GACHnO,EAAM1C,EAAO6Q,KAOrBmiB,EAAS7pB,UAAUhE,MAAQ,aAS3B6tB,EAAS7pB,UAAUlC,SAAW,SAAUF,EAAKU,EAASzH,GAClDA,EAAQA,GAASd,KAAK2O,YACtB,KACI,GAAIumB,GAAUL,EAAmB70B,KAAM6H,GACnC1C,EAAQ4vB,EAAaW,QAAQR,EACjC1xB,GAAM+E,EAAmB,OAAVpD,GACjB,MAAOwM,GACLnO,EAAM1C,EAAO6Q,KAIrBmiB,EAAS7pB,UAAU0E,aAAeZ,EAOlC+lB,EAAS7pB,UAAU0rB,WAAa,SAAUptB,EAASzH,GAE/CA,EAAQA,GAASd,KAAK2O,YAEtB,IACI7R,GAAGyB,EADH0rB,IAGJ,KACI,IAAKntB,EAAI,EAAGyB,EAAMw2B,EAAav2B,OAAYD,EAAJzB,EAASA,IAAK,CACjD,GAAIo4B,GAAUH,EAAaltB,IAAI/K,GAC3B+K,EAAMitB,EAAqB90B,KAAMk1B,EACjCA,KAAYrtB,GACZoiB,EAAQ/iB,KAAKW,GAGrBrE,EAAM+E,EAAS0hB,GAEnB,MAAOtY,GACHnO,EAAM1C,EAAO6Q,KAKrBmiB,EAAS7pB,UAAU7G,UAAY,MAQ/B0wB,EAAS7pB,UAAU3D,KAAO,SAAUuB,EAAKU,EAASzH,GAI9C,GAFAA,EAAQA,GAASd,KAAK2O,aAElB9G,YAAeotB,OACfn0B,GAAQ/C,QAAS,oCAEjB,KACI,GAAIm3B,GAAUL,EAAmB70B,KAAM6H,GACnC1C,EAAQ4vB,EAAaW,QAAQR,EAG7B/vB,GAFU,OAAVA,GAA4B,cAAVA,EAEVR,OAAO8J,KAAK6G,MAAMnQ,EAAOyvB,GAGzB92B,OAEZ0F,EAAM+E,EAASV,EAAK1C,GACtB,MAAOwM,GACLnO,EAAM1C,EAAO6Q,KAWzBmiB,EAAS7pB,UAAU2rB,OAAS,SAAU/tB,EAAKU,EAASzH,GAGhD,GAFAA,EAAQA,GAASd,KAAK2O,aAElB9G,YAAeotB,OACfn0B,GAAQ/C,QAAS,8BAEjB,KACI,GAAIm3B,GAAUL,EAAmB70B,KAAM6H,EACvCktB,GAAaU,WAAWP,GACxB1xB,EAAM+E,GACR,MAAOoJ,GACLnO,EAAM1C,EAAO6Q,KAazBmiB,EAAS7pB,UAAU4rB,OAAS,SAAUhuB,EAAK1C,EAAOoD,EAASzH,GACvDA,EAAQA,GAASd,KAAK2O,YACtB,IAAIvS,GAAQ4D,IACZA,MAAK+H,SAASF,EAAK,SAAUG,GACrBA,EACA5L,EAAMgM,YAAYP,EAAK1C,EAAOoD,EAASzH,GAEvC0C,EAAM1C,GAAS/C,QAAS,gBAAiB8J,IAAKA,KAEnD/G,IAGPpF,EAAOD,QAAUq4B,GAAYO,UAAc,SAAS54B,EAASC,EAAQC,GACrE,YAsBA,SAASm6B,GAASh1B,EAAO6N,GAErB,MAAO,UAAUgD,GACb,GAAIokB,GAAYj1B,GAAS6N,CACzB,IAAKonB,EAAL,CAKA,GAA0C,kCAAtCC,OAAO/rB,UAAU+B,SAASiqB,KAAKtkB,GAC/B,MAAe,MAAXA,EAAE4jB,SACFQ,IAAYl4B,KAAM,qBAAsBiD,MAAO6Q,QAGnDokB,GAAUpkB,EAId,IAAIukB,EACJ,KACI,GAAIC,GAASxkB,EAAEykB,OAAOt1B,OAAS6Q,CAC/BukB,GAAUC,EAAOt4B,KACnB,MAAOw4B,GACLH,EAAsB,YAAXvkB,EAAE9S,KAAsB,mBAAqB,eAE5Dk3B,GAAYl4B,KAAMq4B,EAASp1B,MAAO6Q,MAS1C,QAAS2kB,GAAYl6B,EAAOmM,EAASzH,GAEjC,GAAIy1B,GAAYn6B,EAAMyB,KAClB24B,EAAS,YAAcD,EAEvB7yB,EAAU+yB,EAAU7P,KAAK4P,EAC7B9yB,GAAQgzB,UAAY51B,EACpB4C,EAAQizB,QAAU71B,EAElB4C,EAAQkzB,gBAAkB,WACtB,GAAIC,GAAKnzB,EAAQpG,MACZu5B,GAAGC,iBAAiB/uB,SAASwuB,IAC9BM,EAAGE,kBAAkBR,IAI7B7yB,EAAQszB,UAAY,SAAUC,GAC1B,GAAIJ,GAAKnzB,EAAQpG,MACjB,KAAKu5B,EAAGC,iBAAiB/uB,SAASwuB,GAAY,CAE1C,GAAI,cAAgBM,GAAI,CACpB,GAAIK,GAAiBL,EAAGM,WAAW,MAUnC,OATAD,GAAeF,UAAY,WACvB,GAAII,GAAcF,EAAeE,WACjCA,GAAY/2B,WAAa,WACrBkI,EAAQsuB,IAEZA,EAAGE,kBAAkBR,EAAW,MAAM,IAE1CW,EAAeP,QAAU71B,OACzBo2B,EAAeR,UAAY51B,GAS/B,MAFAm2B,GAAMb,OAAOt1B,OAAUjD,KAAM,wBAC7BiD,GAAMm2B,GAIVJ,EAAGQ,gBAAkB,SAASJ,GAC1BA,EAAMb,OAAOnwB,SAEjBsC,EAAQsuB,IAUhB,QAASS,GAAgBl7B,EAAOm7B,EAAMhvB,EAASzH,GAE3C,GAAIy1B,GAAYn6B,EAAMyB,KAClB25B,EAAUp7B,EAAMy6B,GAChB7xB,EAAgB8wB,EAASh1B,EAAO1E,EAAMuS,aAE1C,OAAI6oB,OACAjvB,GAAQivB,EAAQJ,YAAYb,EAAWgB,QAI3CjB,GAAYl6B,EAAO,SAAUy6B,GACzBz6B,EAAMy6B,GAAKA,EACXtuB,EAAQsuB,EAAGO,YAAYb,EAAWgB,KACnCvyB,GASP,QAASmvB,GAAet2B,GACpBmC,KAAKnC,KAAOA,EApIhB,GAAI5B,GAAQN,EAAQ,iBAGhBoS,EAAqB9R,EAAM8R,mBAI3B0oB,GAHQx6B,EAAMuH,MAGFvH,EAAMyI,YAAcC,OAAO8yB,cAAgB9yB,OAAO+yB,iBAAmB/yB,OAAOgzB,aAAehzB,OAAO8xB,UAAY34B,QAC1H85B,EAAc37B,EAAMyI,YAAcC,OAAOizB,aAAejzB,OAAOkzB,kBAAoB/5B,OACnFg6B,EAAiB77B,EAAMyI,YAAcC,OAAOmzB,gBAAkBnzB,OAAOozB,4BAErEC,EAAiBF,EAAeG,WAAa,WAC7CC,EAAkBJ,EAAeK,YAAc,WAgInDhE,GAAeD,OAAS,SAAUr2B,GAC9B,GAAIs2B,EAAeJ,cACf,MAAO,IAAII,GAAet2B,EAG9B,OAAQE,QAAS,+CAOrBo2B,EAAeJ,YAAc,WACzB,QAAS0C,GAUbtC,EAAelqB,UAAU+qB,IAAM,SAAUntB,EAAK1C,EAAOoD,EAASzH,GAC1D,GAAIjD,GAAOmC,KAAKnC,KACZ8Q,EAAe3O,KAAK2O,aACpBypB,KACAC,IAEAxwB,aAAeotB,QACfmD,EAAOvwB,EACPwwB,EAASlzB,IAETizB,GAAQvwB,GACRwwB,GAAUlzB,IAGdmyB,EAAgBt3B,KAAMk4B,EAAiB,SAAUd,GAC7CA,EAAYkB,QAAUxC,EAASh1B,EAAO6N,EAAc9G,EAAK,OACzDuvB,EAAY/2B,WAAa,WACjBwH,YAAeotB,OACf1sB,EAAQ6vB,EAAMC,GAEd9vB,EAAQV,EAAK1C,GAIrB,KAAK,GAAIrI,GAAI,EAAGA,EAAIs7B,EAAK55B,QAAU1B,EAAIu7B,EAAO75B,OAAQ1B,IAClDs6B,EAAYmB,YAAY16B,GAAMm3B,KAAMN,EAAG2D,EAAOv7B,IAAMs7B,EAAKt7B,KAE9DgE,IAUPqzB,EAAelqB,UAAU7B,YAAc,SAAUP,EAAK1C,EAAOoD,EAASzH,GAClE,GAAIjD,GAAOmC,KAAKnC,KACZ8Q,EAAe3O,KAAK2O,aACpBypB,KACAC,IAEAxwB,aAAeotB,QACfmD,EAAOvwB,EACPwwB,EAASlzB,IAETizB,GAAQvwB,GACRwwB,GAAUlzB,IAGdmyB,EAAgBt3B,KAAMk4B,EAAiB,SAAUd,GAC7CA,EAAYkB,QAAUxC,EAASh1B,EAAO6N,GACtCyoB,EAAY/2B,WAAa,WACjBwH,YAAeotB,OACf1sB,EAAQ6vB,EAAMC,GAEd9vB,EAAQV,EAAK1C,GAIrB,KAAK,GAAIrI,GAAI,EAAGA,EAAIs7B,EAAK55B,QAAU1B,EAAIu7B,EAAO75B,OAAQ1B,IAAK,CACvD,GAAI07B,IAAW9D,EAAG2D,EAAOv7B,GACzBs6B,GAAYmB,YAAY16B,GAAM46B,IAAID,EAAQJ,EAAKt7B,MAEpDgE,IAQPqzB,EAAelqB,UAAUnG,MAAQ,SAAUyE,EAASzH,GAChD,GAAIjD,GAAOmC,KAAKnC,KACZ8Q,EAAe3O,KAAK2O,YACxB2oB,GAAgBt3B,KAAMk4B,EAAiB,SAAUd,GAC7CA,EAAYT,QAAUb,EAASh1B,EAAO6N,GACtCyoB,EAAY/2B,WAAa,WACrBkI,KAGJ6uB,EAAYmB,YAAY16B,GAAMiG,SAC/BhD,IAMPqzB,EAAelqB,UAAUhE,MAAQ,WAEzBjG,KAAK62B,KACL72B,KAAK62B,GAAG5wB,QACRjG,KAAK62B,GAAK,OAUlB1C,EAAelqB,UAAUlC,SAAW,SAAUF,EAAKU,EAASzH,GACxD,GAAIjD,GAAOmC,KAAKnC,KACZ8Q,EAAe3O,KAAK2O,YACxB2oB,GAAgBt3B,KAAMg4B,EAAgB,SAAUZ,GAC5C,GAAImB,GAAcnB,EAAYmB,YAAY16B,GACtC6F,EAAU60B,EAAYG,IAAI7wB,EAE9BuvB,GAAY/2B,WAAa,WACrBkI,IAAU7E,EAAQpG,SAEtB85B,EAAYT,QAAUb,EAASh1B,EAAO6N,IACvC7N,IAGPqzB,EAAelqB,UAAU0E,aAAeZ,EAOxComB,EAAelqB,UAAU0rB,WAAa,SAAUptB,EAASzH,GACrD,GAAIjD,GAAOmC,KAAKnC,KACZ8Q,EAAe3O,KAAK2O,YACxB2oB,GAAgBt3B,KAAMk4B,EAAiB,SAAUd,GAC7C,GAAInN,KAEJmN,GAAY/2B,WAAa,WACrBkI,EAAQ0hB,GAGZ,IAAIvmB,GAAU0zB,EAAYmB,YAAY16B,GAAM86B,YAE5Cj1B,GAAQizB,QAAUb,EAASh1B,EAAO6N,GAClCjL,EAAQszB,UAAY,SAAUC,GAC1B,GAAI2B,GAAS3B,EAAMb,OAAO94B,MACtBs7B,KACA3O,EAAQ/iB,KAAK0xB,EAAO/wB,KAEpB+wB,EAAO,YAAY3C,KAAK2C,MAGjC93B,IAKPqzB,EAAelqB,UAAU7G,UAAY,YASrC+wB,EAAelqB,UAAU3D,KAAO,SAAUuB,EAAKU,EAASzH,GACpD,GAAIjD,GAAOmC,KAAKnC,KACZ8Q,EAAe3O,KAAK2O,aACpBypB,EAAQvwB,YAAeotB,OAASptB,GAAOA,EAE3CyvB,GAAgBt3B,KAAMg4B,EAAgB,SAAUZ,GAC5C,GAAIiB,KAEJjB,GAAYT,QAAUb,EAASh1B,EAAO6N,EAAc9G,EAAK,QACzDuvB,EAAY/2B,WAAa,WACjBwH,YAAeotB,OACf1sB,EAAQ6vB,EAAMC,GAEd9vB,EAAQ6vB,EAAK,GAAIC,EAAO,IAIhC,KAAK,GAAIv7B,GAAI,EAAGA,EAAIs7B,EAAK55B,OAAQ1B,IAAK,CAElC,GAAIy7B,GAAcnB,EAAYmB,YAAY16B,GACtC6F,EAAU60B,EAAYG,IAAIzC,KAAKsC,EAAaH,EAAKt7B,GACrD4G,GAAQszB,UAAY,SAAUC,GAC1B,GAAIuB,GAASvB,EAAMb,OAAO94B,MAC1B+6B,GAAOnxB,KAAKsxB,EAASA,EAAO9D,EAAI52B,WAGzCgD,IASPqzB,EAAelqB,UAAU2rB,OAAS,SAAU/tB,EAAKU,EAASzH,GAEtD,GAAIjD,GAAOmC,KAAKnC,KACZ8Q,EAAe3O,KAAK2O,aACpBypB,EAAQvwB,YAAeotB,OAASptB,GAAOA,EAE3CyvB,GAAgBt3B,KAAMk4B,EAAiB,SAAUd,GAC7CA,EAAYT,QAAUb,EAASh1B,EAAO6N,GACtCyoB,EAAY/2B,WAAa,WACrBkI,IAGJ,KAAK,GAAIzL,GAAI,EAAGA,EAAIs7B,EAAK55B,OAAQ1B,IAAK,CAElC,GAAIy7B,GAAcnB,EAAYmB,YAAY16B,EAC1C06B,GAAY,UAAUtC,KAAKsC,EAAaH,EAAKt7B,MAElDgE,IAUPqzB,EAAelqB,UAAU4rB,OAAS,SAAUhuB,EAAK1C,EAAOoD,EAASzH,GAC7D,GAAIjD,GAAOmC,KAAKnC,KACZ8Q,EAAe3O,KAAK2O,aACpBypB,KACAC,IAEAxwB,aAAeotB,QACfmD,EAAOvwB,EACPwwB,EAASlzB,IAETizB,GAAQvwB,GACRwwB,GAAUlzB,IAGdmyB,EAAgBt3B,KAAMk4B,EAAiB,SAAUd,GAC7CA,EAAYkB,QAAUxC,EAASh1B,EAAO6N,GACtCyoB,EAAY/2B,WAAa,WACjBwH,YAAeotB,OACf1sB,EAAQ6vB,EAAMC,GAEd9vB,EAAQV,EAAK1C,GAIrB,KAAK,GAAIrI,GAAI,EAAGA,EAAIs7B,EAAK55B,QAAU1B,EAAIu7B,EAAO75B,OAAQ1B,IAAK,CACvD,GAAI4G,GAAU0zB,EAAYmB,YAAY16B,GAAM86B,WAAWf,EAAYiB,KAAKT,EAAKt7B,KACzE07B,GAAW9D,EAAG2D,EAAOv7B,GACzB4G,GAAQo1B,MAASjxB,IAAKuwB,EAAKt7B,GAAIqI,MAAOqzB,GACtC90B,EAAQszB,UAAY,SAAUC,GAC1B,GAAI2B,GAAS3B,EAAMb,OAAO94B,MACtBs7B,GACAA,EAAO/C,OAAOoB,EAAMb,OAAO0C,KAAK3zB,OAEhCiyB,EAAYvzB,WAIzB/C,IAIPpF,EAAOD,QAAU04B,GAAkBI,OAAW,SAAS94B,EAASC,EAAQC,GACxE,YAgBA,SAASy4B,GAAYv2B,GAiBjB,QAASk7B,GAAiBlxB,EAAK/G,GAE3B,GAAIk4B,EAUJ,OARInxB,aAAeotB,SACf+D,EAAgB,gCAGRl7B,SAAR+J,GAA6B,OAARA,KACrBmxB,EAAgB,eAGhBA,GACAx1B,EAAM1C,GAAS/C,QAASi7B,KACjB,IAEJ,EA/BX,GAAIC,MACApgB,KACAuf,IAEJp4B,MAAKnC,KAAOA,CAEZ,IAAIq7B,GAAmB,SAAUp4B,GAC7B,MAAOA,IAASd,KAAK2O,aAmCzB3O,MAAKg1B,IAAM,SAAUntB,EAAK1C,EAAOoD,EAASzH,GACtCA,EAAQo4B,EAAiBp4B,GAErBi4B,EAAiBlxB,EAAK/G,KACjBs3B,EAAK5hB,eAAe3O,GAGrB/G,GAAQ/C,QAAS,qBAAsB8J,IAAKA,IAF5C7H,KAAKoI,YAAYP,EAAK1C,EAAOoD,EAASzH,KAelDd,KAAKoI,YAAc,SAAUP,EAAK1C,EAAOoD,EAASzH,GAI9C,GAFAA,EAAQo4B,EAAiBp4B,GAErBi4B,EAAiBlxB,EAAK/G,GAAQ,CAC9B,GAAIrB,GAAQ24B,EAAKvwB,EACH/J,UAAV2B,IAEIA,EADAw5B,EAAMz6B,OAAS,EACPy6B,EAAMx6B,OAAO,EAAG,GAEhBoa,EAAMra,QAGtBqa,EAAMpZ,GAAS0F,EACfizB,EAAKvwB,GAAOpI,EACZ+D,EAAM+E,EAASV,EAAK1C,KAQ5BnF,KAAK8D,MAAQ,SAAUyE,GACnBsQ,KACAuf,KACAa,KACAz1B,EAAM+E,IAQVvI,KAAK+H,SAAW,SAAUF,EAAKU,GAC3B,GAAIP,GAAYowB,EAAK5hB,eAAe3O,EACpCrE,GAAM+E,EAASP,IAOnBhI,KAAK21B,WAAa,SAAUptB,GAExB,GAAI0hB,KACJ,KAAK,GAAIpsB,KAAQu6B,GACbnO,EAAQ/iB,KAAKrJ,EAEjB2F,GAAM+E,EAAS0hB,IASnBjqB,KAAKsG,KAAO,SAAUuB,EAAKU,EAASzH,GAGhC,GAFAA,EAAQo4B,EAAiBp4B,GAErBi4B,EAAiBlxB,EAAK/G,GAAQ,CAC9B,GAAIrB,GAAQ24B,EAAKvwB,EACjBrE,GAAM+E,EAASV,EAAKgR,EAAMpZ,MAUlCO,KAAK41B,OAAS,SAAU/tB,EAAKU,EAASzH,GAGlC,GAFAA,EAAQo4B,EAAiBp4B,GAErBi4B,EAAiBlxB,EAAK/G,GAAQ,CAC9B,GAAIrB,GAAQ24B,EAAKvwB,EACH/J,UAAV2B,IACIA,IAAUoZ,EAAMra,OAAS,EACzBqa,EAAM/G,OAEN+G,EAAMpZ,GAAS3B,OACfm7B,EAAM/xB,KAAKzH,UAER24B,GAAKvwB,GAGS,IAAjBgR,EAAMra,SACNy6B,OAIRz1B,EAAM+E,KAYdvI,KAAK61B,OAAS,SAAUhuB,EAAK1C,EAAOoD,EAASzH,GACzCA,EAAQo4B,EAAiBp4B,GACrBi4B,EAAiBlxB,EAAK/G,KAClBs3B,EAAK5hB,eAAe3O,GACpB7H,KAAKoI,YAAYP,EAAK1C,EAAOoD,EAASzH,GAEtCA,GAAQ/C,QAAS,gBAAiB8J,IAAKA,MA3LvD,GAAI5L,GAAQN,EAAQ,iBAGhBoS,EAAqB9R,EAAM8R,mBAC3BvK,EAAQvH,EAAMuH,KAkMlB4wB,GAAYF,OAAS,SAAUr2B,GAC3B,MAAO,IAAIu2B,GAAYv2B,IAO3Bu2B,EAAYL,YAAc,WACtB,OAAO,GAKXK,EAAYnqB,UAAUhE,MAAQ,aAG9BmuB,EAAYnqB,UAAU0E,aAAeZ,EAIrCqmB,EAAYnqB,UAAU7G,UAAY,SAIlC1H,EAAOD,QAAU24B,GAAen4B,MAAU,SAASR,GACnD,YAKA,SAASiJ,KACL,MAAyB,mBAAXC,QAqBlB,QAASgF,GAASxE,GACd,MAAiB,QAAVA,GAA4BrH,SAAVqH,EAQ7B,QAAS4C,GAAS1J,EAAKC,GACnB,GAAIxB,GAAGyB,CACP,KAAKzB,EAAI,EAAGyB,EAAMF,EAAIG,OAAYD,EAAJzB,EAASA,IACnC,GAAIuB,EAAIvB,KAAOwB,EACX,OAAO,CAGf,QAAO,EAQX,QAAS0P,GAAQmrB,EAAGC,GAChB,MAAct7B,UAANq7B,EAAmBA,EAAIC,EAMnC,QAAS51B,GAAM2kB,GAEX,GAAyB,IAArB7b,UAAU9N,OAEV,WADAmG,QAAOmjB,WAAWK,EAAU,EAIhC,IAAIkR,GAAOpE,MAAMhrB,UAAUqvB,MAAMrD,KAAK3pB,UAAW,EACjD3H,QAAOmjB,WAAW,WACdK,EAAS9b,MAAMrM,KAAMq5B,IACtB,GAQP,QAASx4B,GAAU04B,EAAWx7B,EAAS4B,GAGnC,IAAK45B,EACD,MAAQx7B,QAAS,iBAAmBA,EAAS4B,KAAMA,GAS3D,QAASiE,GAAOwyB,EAAQiC,GACpB,IAAK,GAAIx6B,KAAQw6B,GACbjC,EAAOv4B,GAAQw6B,EAAOx6B,EAG1B,OAAOu4B,GAGX,QAAS7K,GAAKltB,EAAK8pB,GAOf,GAAI9pB,EAAK,CACL,GAAIvB,GAAGyB,CACP,KAAKzB,EAAI,EAAGyB,EAAMF,EAAIG,OAAYD,EAAJzB,EAASA,IACnC,GAAIqrB,EAAS9pB,EAAIvB,IACb,MAAOuB,GAAIvB,GAIvB,MAAO,MAGX,QAASiN,GAAQ5E,GAMb,MAAiD,mBAA1C6wB,OAAO/rB,UAAU+B,SAASiqB,KAAK9wB,GAO1C,QAASilB,GAAOjlB,GACZ,MAAiD,kBAA1C6wB,OAAO/rB,UAAU+B,SAASiqB,KAAK9wB,GAQ1C,QAASoR,GAASpR,GACd,MAAwB,gBAAVA,GAOlB,QAASqB,GAAWrB,GAChB,MAAOq0B,UAASr0B,EAAO,IAS3B,QAASs0B,GAAeC,EAAKC,EAASC,GAC9BF,EAAIljB,eAAemjB,KACnBD,EAAIE,GAAWF,EAAIC,SACZD,GAAIC,IAOnB,QAAS5rB,GAAmBjN,GACxB,KAAMA,GAOV,QAAS0S,GAAWf,GAChB,MAAIA,GAAIonB,KACGpnB,EAAIonB,OAGRpnB,EAAIwH,QAAQ,aAAc,IAUrC,QAASzX,GAAiB2C,EAAO20B,GAC7B,MAAkBh8B,UAAVqH,EAAuBA,EAAQ20B,EAiB3C,QAASC,GAAW3vB,GAChB,GAAI9M,IAAW08B,YAAY,EAE3B,IAAI5vB,EAAK,CACL,GAAImK,GAAU0lB,EAAS7nB,KAAKhI,EAC5B,IAAImK,EAAS,CACT,GAAIzX,GAAGyB,CACP,KAAKzB,EAAI,EAAGyB,EAAM27B,EAAa17B,OAAYD,EAAJzB,EAASA,IACxCyX,EAAQzX,EAAI,KACZQ,EAAO48B,EAAap9B,IAAMyX,EAAQzX,EAAI,IAI9CQ,EAAO68B,SACP78B,EAAO08B,YAAa,GAI5B,MAAO18B,GAOX,QAAS88B,GAAeC,GACpB,MAAO,GAAGx0B,OACNw0B,EAAQF,QAAU,GAClBE,EAAQC,WAAa,GACrBD,EAAQE,MAAQ,GAChBF,EAAQG,OAAS,GACjBH,EAAQ5e,UAAY,IAiB5B,QAAS5P,GAAiBzB,GACtB,GAAIiwB,GAAUN,EAAW3vB,GACrB+vB,EAASE,EAAQF,OACjBG,EAAYD,EAAQC,SAExB,IAAIH,IACAE,EAAQF,OAASA,EAAO9b,cACpBic,GAAW,CACX,GAAI/lB,GAAUkmB,EAAkBroB,KAAKkoB,EACjC/lB,KACA8lB,EAAQC,UAAY,MACnB/lB,EAAQ,GAAKA,EAAQ,GAAK,IAAM,IAChCA,EAAQ,GAAG8J,eACX9J,EAAQ,GAAK,IAAMA,EAAQ,GAAK,KAO7C,MAFAnK,GAAMgwB,EAAeC,GAEdjwB,EAAI6P,QAAQygB,EAAkB,SAAUjoB,GAC3C,MAAOA,GAAI4L,gBASnB,QAASrU,GAAaI,EAAKuwB,GACvB,IAAKA,EACD,MAAOvwB,EAGX,IAAIiwB,GAAUN,EAAW3vB,EACzB,IAAIiwB,EAAQL,WACR,MAAO5vB,EAGX,IAEImwB,GAFAK,EAAWb,EAAWY,GACtBE,IA2BJ,OAxBIR,GAAQC,WACRO,EAASP,UAAYD,EAAQC,UAC7BC,EAAOF,EAAQE,KACfM,EAASL,MAAQH,EAAQG,QAEpBH,EAAQE,MAKLA,EAD2B,MAA3BF,EAAQE,KAAK5e,OAAO,GACb0e,EAAQE,KAERO,EAAqBT,EAAQE,KAAMK,EAASL,MAEvDM,EAASL,MAAQH,EAAQG,QARzBD,EAAOK,EAASL,KAChBM,EAASL,MAAQH,EAAQG,OAASI,EAASJ,OAS/CK,EAASP,UAAYM,EAASN,WAGlCO,EAASN,KAAOQ,EAAmBR,GAEnCM,EAASV,OAASS,EAAST,OAC3BU,EAASpf,SAAW4e,EAAQ5e,SAErB2e,EAAeS,GAQ1B,QAASC,GAAqBE,EAASC,GACnC,GACIp+B,GADA09B,EAAO,GAYX,OATIU,KACAp+B,EAAMo+B,EAAS7hB,YAAY,KAC3BmhB,EAAOU,EAASxvB,UAAU,EAAG5O,GAEQ,MAAjC09B,EAAK5e,OAAO4e,EAAK/7B,OAAS,KAC1B+7B,GAAc,MAIfA,EAAOS,EAOlB,QAASD,GAAmBR,GAKxB,IAJA,GAEI19B,GAFAS,EAAS,GACTkN,EAAU,GAGP+vB,GACwB,IAAvBA,EAAK1wB,QAAQ,OAAqC,IAAtB0wB,EAAK1wB,QAAQ,KACzC0wB,EAAOA,EAAKtgB,QAAQ,aAAc,IACH,IAAxBsgB,EAAK1wB,QAAQ,QACpB0wB,EAAOA,EAAKtgB,QAAQ,aAAc,KAClCpd,EAAMS,EAAO8b,YAAY,KAErB9b,EADQ,KAART,EACS,GAEAS,EAAOmO,UAAU,EAAG5O,IAEH,IAAvB09B,EAAK1wB,QAAQ,MACpB0wB,EAAOA,EAAKtgB,QAAQ,YAAa,MAEjCzP,EAAU+vB,EACV19B,EAAM09B,EAAK1wB,QAAQ,IAAK,GACZ,KAARhN,IACA2N,EAAU+vB,EAAK9uB,UAAU,EAAG5O,IAEhCS,GAAkBkN,EAClB+vB,EAAOA,EAAKtgB,QAAQzP,EAAS,IAGrC,OAAOlN,GAGX,QAAS4c,GAA4BzH,GACjC,GAAIpU,KACJ,IAAoBP,SAAhB6G,OAAOu2B,KACP78B,EAAM88B,EAAa1oB,OAGnB,KAAK,GADD2oB,GAAYz2B,OAAOu2B,KAAKzoB,GACnB3V,EAAI,EAAGA,EAAIs+B,EAAU58B,OAAQ1B,IAClCuB,EAAI6I,KAAKk0B,EAAUC,WAAWv+B,GAKtC,KAAK,GAFDw+B,GAAW,GACXC,EAAY,mBACPvO,EAAI,EAAGA,EAAI3uB,EAAIG,OAAQwuB,IAAK,CACjC,GAAI2H,GAAIt2B,EAAI2uB,EACZsO,IAAYC,EAAU5G,GAAK,GAC3B2G,GAAYC,EAAc,GAAJ5G,GAE1B,MAAO2G,GAGX,QAASH,GAAa1oB,GAElB,IAAK,GADD+oB,GAAe,GACV1+B,EAAI,EAAGA,EAAI2V,EAAIjU,OAAQ1B,IAAK,CACjC,GAAI2+B,GAAmBC,EAAoBjpB,EAAI3V,IAC3C6+B,EAAc,EACO,QAArBF,IACAE,EAAcF,EAAiBzvB,SAAS,GACxCwvB,GAAgBI,EAAiBD,IAGzC,GAAIE,MACAC,EAAgBtC,SAASgC,EAAah9B,OAAS,EAAG,GACtD,KAAK1B,EAAI,EAAOg/B,EAAJh/B,EAAmBA,IAAK,CAChC,GAAIi/B,GAAWvC,SAASgC,EAAa/vB,UAAc,EAAJ3O,EAAiB,GAATA,EAAI,IAAS,EACpE++B,GAAU30B,KAAK60B,GAEnB,MAAOF,GAGX,QAASH,GAAoBM,GACzB,GAAIC,GAAYD,EAAUX,WAAW,GACjCa,EAAW,GACXC,EAAyB,CAC7B,OAAIF,IAAa,IAAmB,IAAbA,EACZA,EAAYC,EACZD,GAAa,IAAmB,KAAbA,EACnBA,EAAYC,EAAWC,EACvBF,GAAa,IAAmB,IAAbA,EACnBA,EAAY,EACC,KAAbD,EACA,GACa,KAAbA,EACA,GAEA,KAIf,QAASJ,GAAiBJ,GACtB,KAAOA,EAAah9B,OAAS,GACzBg9B,EAAe,IAAMA,CAEzB,OAAOA,GAIX,QAASx+B,GAAyB2C,GAC9B,MAAIA,IAAQA,EAAKwF,MACNxF,EAAKwF,MAAM3G,OAGf,EAGX,QAASrB,GAAoBwC,EAAM/C,EAAOC,GACtC,GAAaiB,SAAT6B,GAAqC7B,SAAf6B,EAAKwF,MAC3B,MAAOxF,EAGC,GAAR/C,IACAA,EAAQ,EAGZ,IAAI4B,GAASxB,EAAyB2C,EACzB9C,GAAT2B,IACA3B,EAAM2B,EAGV,IAAIqX,KACJ,KAAK,GAAInK,KAAY/L,GAEbkW,EAAQnK,GADI,SAAZA,EACoB/L,EAAK+L,GAAU4tB,MAAM18B,EAAOC,GAE5B8C,EAAK+L,EAIjC,OAAOmK,GAGX,QAAS3Y,GAAqByC,EAAMy8B,GAChC,MAAmBt+B,UAAfs+B,GAAiDt+B,SAArBs+B,EAAWj3B,MAChCxF,EAGE7B,SAAT6B,GAAmD,IAA7Bq2B,OAAOoC,KAAKz4B,GAAMnB,OACjC49B,EAGQt+B,SAAf6B,EAAKwF,OACLxF,EAAKwF,MAAQi3B,EAAWj3B,MACjBxF,IAGXA,EAAKwF,MAAQxF,EAAKwF,MAAMU,OAAOu2B,EAAWj3B,OAEnCxF,GAGX,QAAS4c,GAAS8f,EAAOC,GACrB,MAA+D,KAAxDD,EAAMxyB,QAAQyyB,EAAQD,EAAM79B,OAAS89B,EAAO99B,QAGvD,QAASqd,GAAYwgB,EAAOC,GACxB,MAAiC,KAA1BD,EAAMxyB,QAAQyyB,GAGzB,QAAS9mB,GAAc+mB,EAAQC,GAC3B,GAAIC,GAAaD,CACjB,KAAK7yB,EAAS4yB,GACV,MAAOE,EAGX,IAAIC,GAAmBH,EAAOle,aAC9B,QAAQqe,GACJ,IAAK,OACDD,EAAa,CACb,MACJ,KAAK,UACDA,EAAa,CACb,MACJ,KAAK,OACDA,EAAa,EAMrB,MAAOA,GAlgBX,GAAIE,GAAgB,SAAUC,GAE1B,MAAIj4B,QAAO4gB,cACA,GAAI5gB,QAAO4gB,cAAcqX,GAE7B,MAmLP3C,EAAW,0DACXC,GAAgB,SAAU,YAAa,OAAQ,QAAS,YA6CxDO,EAAoB,6CAGpBC,EAAmB,gBA+RvBj/B,GAAQiJ,UAAYA,EACpBjJ,EAAQkhC,cAAgBA,EACxBlhC,EAAQkO,SAAWA,EACnBlO,EAAQsM,SAAWA,EACnBtM,EAAQuS,QAAUA,EAClBvS,EAAQ+H,MAAQA,EAChB/H,EAAQoF,UAAYA,EACpBpF,EAAQmI,OAASA,EACjBnI,EAAQ8vB,KAAOA,EACf9vB,EAAQs+B,WAAaA,EACrBt+B,EAAQsO,QAAUA,EAClBtO,EAAQ2uB,OAASA,EACjB3uB,EAAQ8a,SAAWA,EACnB9a,EAAQuO,aAAeA,EACvBvO,EAAQoQ,iBAAmBA,EAC3BpQ,EAAQ+K,WAAaA,EACrB/K,EAAQg+B,eAAiBA,EACzBh+B,EAAQsS,mBAAqBA,EAC7BtS,EAAQ+X,WAAaA,EACrB/X,EAAQ+G,iBAAmBA,EAC3B/G,EAAQ0/B,aAAeA,EACvB1/B,EAAQye,4BAA8BA,EACtCze,EAAQuB,yBAA2BA,EACnCvB,EAAQ0B,oBAAsBA,EAC9B1B,EAAQyB,qBAAuBA,EAC/BzB,EAAQogB,WAAaA,EACrBpgB,EAAQ8gB,SAAWA,EACnB9gB,EAAQ+Z,cAAgBA,GAAiBtZ,IAAQ,SAAST,EAASC,EAAQC,GAC3E,YA2BA,SAASkhC,GAA+BxsB,GACpC,GAAIysB,GAAK,aACT,OAAOA,GAAG3S,KAAK9Z,GAOnB,QAAS0sB,GAAa1sB,GAGlB,GAAI2sB,GAAK,OACT,OAAgB,QAAT3sB,GAAiB2sB,EAAG7S,KAAK9Z,GAOpC,QAAS4sB,GAA4BC,GAGjC,KAAsB,OAAfA,GAA+C,IAAxBA,EAAWC,UAAgB,CACrD,GAAIv/B,GAAMw/B,EAAkBF,EAAY,QAASjd,EACjD,IAAY,aAARriB,EACA,OAAO,CACJ,IAAY,YAARA,EACP,KAEAs/B,GAAaA,EAAWhX,WAIhC,OAAO,EAOX,QAASmX,GAAmBC,GACxB,GAAIC,GAAWD,EAAaC,QAC5B,OAAmB,SAAZA,GAAsD,IAA/BA,EAAS1zB,QAAQ,UAQnD,QAAS2zB,GAAgB9D,EAAK77B,EAAMsH,GAGhC,IACIu0B,EAAI+D,YAAY5/B,EAAMsH,GACxB,MAAO8C,KAQb,QAASy1B,KACL,GAAIC,GAAShB,EAAc,yBAO3B,OANIgB,KACAH,EAAgBG,EAAQ,eAAe,GACvCH,EAAgBG,EAAQ,kBAAmB,KAC3CH,EAAgBG,EAAQ,yBAAyB,GACjDH,EAAgBG,EAAQ,mBAAmB,IAExCA,EAUX,QAASC,KACL,IACI,GAAIC,GAAQlB,EAAc,yBAI1B,OAHIkB,KACAA,EAAM9O,OAAQ,GAEX8O,EACT,MAAO51B,GACL,MAAOy1B,MAUf,QAASI,GAAWztB,GAChB,GAAIikB,GAAMsJ,GACV,KAAKtJ,EACD,MAAO,KAGXA,GAAIyJ,QAAQ1tB,EACZ,IAAI2tB,GAAa1J,EAAI0J,UAIrB,OAH6B,KAAzBA,EAAWC,WACXC,EAAoBF,EAAW9zB,OAAQ8zB,EAAWG,QAAS9tB,GAExDikB,EAQX,QAAS4J,GAAoBE,EAAmBD,EAASE,GAKrD,KAHiC,gBAAtBD,KACPA,GAAsBrgC,QAASqgC,IAE7Bx6B,EAAOw6B,GAAqBD,QAASA,GAAW,GAAIE,aAAcA,GAAgB,KAQ5F,QAASxe,GAASxP,GACd,GAAIiuB,GAAYxgC,MAEZwgC,GADAriC,EAAMyI,YACMC,OAAO45B,WAAa,GAAI55B,QAAO45B,UAE/B,IAAK5iC,EAAQ,UAAmB,UAEhD,IAAI24B,EAEJ,KAAKgK,EAKD,MAJAhK,GAAMwJ,EAAWztB,GACZikB,GACD4J,EAAoB,gCAEjB5J,CAGX,KACIA,EAAMgK,EAAUE,gBAAgBnuB,EAAM,YACxC,MAAOsB,GACLusB,EAAoBvsB,EAAG,GAAItB,GAG/B,GAAIrJ,GAAUstB,EAAImK,gBACd7f,EAAQ5X,EAAQ03B,aAChB3f,EAAYC,EAAahY,EAG7B,IAAkB,gBAAd+X,GAA+BH,IAAU+f,EAAqB,CAC9D,GAAIC,GAAiB7e,EAAqB/Y,EAAS23B,EAAqB,cACpER,EAAUS,EAAiBC,EAAaD,GAAkB,EAC9DV,GAAoBve,EAAa3Y,IAAY,GAAIm3B,EAAS9tB,GAM9D,GAAkB,OAAd0O,GAAsBH,IAAUkgB,GAAWC,EAA0B/3B,EAAS83B,EAAS,MAAO,CAI9F,IAHA,GAAI50B,GAAS,GACT80B,KACApG,EAAS5xB,EAAQi4B,WACdrG,GACqB,IAApBA,EAAOuE,WACPjzB,GAAUyV,EAAaiZ,IAAW,IAEtCoG,EAAS93B,KAAK0xB,EAAOsG,aACrBtG,EAASA,EAAOqG,YAAcD,EAASG,OAE3Cj1B,IAAUyV,EAAa3Y,IAAY,GACnCk3B,EAAoBh0B,EAAQ,GAAImG,GAGpC,MAAOikB,GAQX,QAAS8K,GAAiBnvB,EAAQpS,GAC9B,MAAOoS,GAASA,EAAS,IAAMpS,EAAOA,EAO1C,QAASwhC,GAAcC,EAASC,GAC5B,GAAI1C,EAA+B0C,EAAS5/B,MAAO,CAC/C,GAAI6/B,GAAOC,EAAiBH,EAASrf,EAAO,QACvCuf,KACDA,EAAOE,EAAgBJ,EAAQK,cAAe1f,EAAOmf,EAAiB,MAAO,UAC7EQ,EAAeN,EAASE,IAE5BA,EAAKr6B,MAAQ,WAGjB,MADAm6B,GAAQpa,YAAYqa,GACbD,EAOX,QAASlgB,GAAcpY,EAAS64B,GAC5B,GACI/iC,GAAGyB,EADHwf,EAAa/W,EAAQ+W,UAEzB,KAAKjhB,EAAI,EAAGyB,EAAMwf,EAAWvf,OAAYD,EAAJzB,EAASA,IAC1C+iC,EAAoB9hB,EAAWzf,KAAKxB,IAU5C,QAASsgC,GAAkBkC,EAASvgB,EAAWH,GAE3C,GAAIzI,GAAYspB,EAAiBH,EAASvgB,EAAWH,EACrD,OAAOzI,GAAY0oB,EAAa1oB,GAAa,KASjD,QAASspB,GAAiBH,EAASvgB,EAAWH,GAE1C,GAAIb,GAAauhB,EAAQvhB,UACzB,OAAIA,GAAW+hB,eACJ/hB,EAAW+hB,eAAelhB,GAAS,KAAMG,GAG7ChB,EAAWgiB,iBAAiBhhB,EAAWH,IAAU,KAQ5D,QAASohB,GAAWV,EAAStoB,GAEzB,GAAI2jB,GAAO8E,EAAiBH,EAAS,OAAQrf,EAC7C,QAAQ0a,EAAO3wB,EAAa2wB,EAAKx1B,MAAO6R,GAAWA,IAAY,KAQnE,QAASwI,GAAiB8f,EAASW,GAE/BC,EAAYZ,GAAsB,EAAO,SAAU7f,GAK/C,MAJuB,KAAnBA,EAAM0d,UACN8C,EAAkBxgB,IAGf,IAYf,QAAS0gB,GAAqBrgB,EAAM4e,EAAcnE,GAC9C,GACIz9B,GAAGyB,EADHyT,EAAQuoB,EAAK7mB,MAAM,IAEvB,KAAK5W,EAAI,EAAGyB,EAAMyT,EAAMxT,OAAYD,EAAJzB,EAASA,IACrCgjB,EAAOA,GAAQC,EAAqBD,EAAM4e,EAAc1sB,EAAMlV,GAElE,OAAOgjB,IAAQ,KAanB,QAASsgB,GAAkBtgB,EAAM4e,EAAcnE,GAG3C,GAAI8F,GAAmB9F,EAAKnhB,YAAY,KACpCknB,EAAW/F,EAAK9uB,UAAU40B,EAAmB,GAC7CE,EAAahG,EAAK9uB,UAAU,EAAG40B,GAE/BG,EAAOD,EAAaJ,EAAqBrgB,EAAM4e,EAAc6B,GAAczgB,CAC/E,OAAI0gB,GAC2B,MAAvBF,EAAS3kB,OAAO,GACT8jB,EAAiBe,EAAMF,EAAS70B,UAAU,GAAIizB,GAElD3e,EAAqBygB,EAAM9B,EAAc4B,GAE7C,KASX,QAASvgB,GAAqBuf,EAASZ,EAAc3f,GAEjD,MAAO0hB,GAA8BnB,EAASZ,EAAc3f,GAAwB,GASxF,QAASggB,GAA0BO,EAASZ,EAAc3f,GACtD,GAAIugB,EAAQoB,uBAAwB,CAChC,GAAIpjC,GAASgiC,EAAQoB,uBAAuBhC,EAAc3f,EAC1D,OAAOzhB,GAAOkB,OAAS,EAAIlB,EAAO,GAAK,KAE3C,MAAOmjC,GAA8BnB,EAASZ,EAAc3f,GAAwB,GAYxF,QAAS0hB,GAA8BnB,EAASZ,EAAc3f,EAAW4hB,GAErE,GAAIC,GAAe,IAYnB,OAXAV,GAAYZ,EAASqB,EAAW,SAAUlhB,GACtC,GAAuB,IAAnBA,EAAM0d,SAAgB,CACtB,GAAI0D,IAAuBnC,GAAgBzf,EAAgBQ,KAAWif,EAClEoC,GAAsB/hB,GAAaC,EAAaS,KAAWV,CAE3D8hB,IAAuBC,IACvBF,EAAenhB,GAGvB,MAAwB,QAAjBmhB,IAEJA,EAOX,QAASjhB,GAAaohB,GAElB,GAGIC,GAHA1jC,EAAS,KACTwiB,EAAgC,IAAxBihB,EAAW5D,UAAkB4D,EAAWtC,gBAAmBsC,EAAWtC,gBAAkBsC,EAChGE,EAA2BnhB,EAAK6f,cAAcuB,sBAAuB,CAoCzE,OAjCAhB,GAAYpgB,GAAM,EAAO,SAAUL,GAC/B,GAAuB,IAAnBA,EAAM0d,UAAqC,IAAnB1d,EAAM0d,SAAgB,CAS9C,GAAI9sB,GAAOwuB,EAAapf,GACpB0hB,EAAgBF,IAA6BlE,EAAa1sB,EACzD8wB,KAGiCrjC,SAA9BkjC,IACAA,EAA4B/D,EAA4Bnd,IAG5DqhB,EAAgBH,GAGhBG,IACK7jC,EAGDA,GAAU+S,EAFV/S,EAAS+S,GAOrB,OAAO,IAEJ/S,EAOX,QAAS0hB,GAAasgB,GAElB,MAAOA,GAAQvgB,WAAaugB,EAAQ8B,SAOxC,QAASniB,GAAgBqgB,GAErB,MAAOA,GAAQZ,cAAgB,KAOnC,QAASG,GAAaS,GAElB,MAAyB,KAArBA,EAAQnC,SACDxd,EAAa2f,GAEjBA,EAAQ+B,UAWnB,QAASnB,GAAYZ,EAASqB,EAAWW,GAKrC,IAHA,GAAIC,MACA9hB,EAAQ6f,EAAQL,WAChBuC,GAAU,EACP/hB,GAAS+hB,GACZA,EAAUF,EAAgB7hB,GACtB+hB,IACIb,GAAalhB,EAAMwf,YACnBsC,EAASr6B,KAAKuY,EAAMwf,YAExBxf,EAAQA,EAAMyf,aAAeqC,EAASpC,SAWlD,QAASsC,GAAkBnC,EAASZ,EAAc3f,GAG9C,IADA,GAAI2iB,GAAUpC,EAAQJ,YACfwC,GAAS,CACZ,GAAyB,IAArBA,EAAQvE,SAAgB,CACxB,GAAI0D,IAAuBnC,GAAgBzf,EAAgByiB,KAAahD,EACpEoC,GAAsB/hB,GAAaC,EAAa0iB,KAAa3iB,CAEjE,IAAI8hB,GAAuBC,EACvB,MAAOY,GAGfA,EAAUA,EAAQxC,YAEtB,MAAO,MAaX,QAASyC,KACL,GAAIC,GAAiBj9B,OAAOggB,SAASid,cACrC,OAAQA,IAAkBA,EAAeC,eACtCD,EAAeC,eAAe,KAAM,KAAM,MAC1CjE,IAUP,QAASkE,GAAkBC,EAAQC,GAC/B,IAAKj4B,EAAQi4B,GACT,MAAOpC,GAAemC,EAAQC,EAGlC,IAAIllC,GAAGyB,CACP,KAAKzB,EAAI,EAAGyB,EAAMyjC,EAASxjC,OAAYD,EAAJzB,EAASA,IACxCklC,EAASllC,IAAM8iC,EAAemC,EAAQC,EAASllC,GAEnD,OAAOilC,GAUX,QAASnC,GAAemC,EAAQtiB,GAG5B,GADA5e,EAAUkhC,IAAWtiB,EAAO,6DACxBA,EAAO,CACP,GAAqB,gBAAVA,GACP,MAAO4f,GAAc0C,EAAQE,EAAWF,EAAOpC,cAAelgB,GAE3C,KAAnBA,EAAM0d,SACN4E,EAAOG,mBAAqBH,EAAOG,mBAAmBziB,GAASsiB,EAAOI,iBAAiB1iB,GAEvFsiB,EAAO7c,YAAYzF,GAG3B,MAAOsiB,GAUX,QAASrC,GAAgBpL,EAAKoK,EAAc0D,EAAej9B,GAEvD,GAAIgR,GACAme,EAAI+N,mBAAqB/N,EAAI+N,kBAAkB3D,EAAc0D,IAC7D9N,EAAIgO,WAAW,EAAGF,EAAe1D,GAAgB5gC,OAGrD,OADAqY,GAAUhR,MAAQA,GAAS,GACpBgR,EAYX,QAASosB,GAAcjO,EAAKoK,EAAc0D,EAAeJ,GACrD,GAAIh7B,GACAstB,EAAIkO,iBAAmBlO,EAAIkO,gBAAgBC,cAAeL,IAC1D9N,EAAIgO,WAAW,EAAGF,EAAeK,eAAiB3kC,OAEtD,OAAOgkC,GAAkB96B,EAASg7B,OAStC,QAASU,GAAoBpO,EAAKoK,EAAczuB,GAC5C,MAAOyvB,GAAgBpL,EAAKjV,EAAS+f,EAAiB,QAASnvB,GAASyuB,GAQ5E,QAASiE,GAAerO,EAAKjkB,GASzB,IAPA,GAAIlL,GAAQ,MAAQkL,EAAO,OACvBuyB,EAAU/iB,EAAS1a,GACnB09B,EAAWD,EAAQnE,gBACnBqE,GAAY,cAAgBxO,GAAOA,EAAIyO,WAAWF,GAAU,GAAQA,GACpEpnB,EAAW6Y,EAAI0O,yBAEfC,EAAgBH,EAAS7D,WACtBgE,GACHxnB,EAASyJ,YAAY+d,GACrBA,EAAgBA,EAAc/D,WAElC,OAAOzjB,GAQX,QAASwmB,GAAW3N,EAAKjkB,GACrB,MAAOikB,GAAI4O,eAAe7yB,GAgB9B,QAAS8yB,GAAiB7O,EAAKxU,EAAM4e,EAAczuB,EAAQsqB,GACvD,GAMIz9B,GAAGyB,EANHV,EAAO,GACPmU,EAAQuoB,EAAK7mB,MAAM,KACnB0vB,EAAcrjB,EACdsjB,EAAad,EACbe,EAAUxjB,CAGd,KAAKhjB,EAAI,EAAGyB,EAAMyT,EAAMxT,OAAYD,EAAJzB,EAASA,IAAK,CAC1Ce,EAAOmU,EAAMlV,GACU,MAAnBe,EAAK8d,OAAO,KACZ9d,EAAOA,EAAK4N,UAAU,GACtB23B,EAAc3D,EACd4D,EAAa3D,EAGjB,IAAI6D,GAAYH,EAAYE,EAAS5E,EAAc7gC,EAC9C0lC,KACDA,EAAYF,EAAW/O,EAAKoK,EAAcU,EAAiBnvB,EAAQpS,IACnE+hC,EAAe0D,EAASC,IAE5BD,EAAUC,EAEd,MAAOD,GAOX,QAASE,GAAalE,GAClB,GAAImE,GAAgB9+B,OAAO++B,aAC3B,IAAID,EAAe,CACf,GAAIE,GAAa,GAAIF,EACrB,OAAOE,GAAWC,kBAAkBtE,GAGxC,GAAIA,EAAQpjC,IACR,MAAOojC,GAAQpjC,GAGnB,OAAQ6B,QAAS,iCAOrB,QAAS8lC,GAAwBvE,GAC7B,GACIxiC,GADAklC,EAAW1C,EAAQwE,WAChBvlC,EAAMyjC,EAASxjC,MACtB,IAAY,IAARD,EACA,MAAO,EAQX,IAAI+1B,GAAMgL,EAAQK,cACdlkB,EAAW6Y,EAAI0O,yBACfe,EAAezP,EAAI1P,cAAc,IAIrC,KAFAnJ,EAASyJ,YAAY6e,GAEhBjnC,EAAI,EAAOyB,EAAJzB,EAASA,IACjBinC,EAAa7e,YAAY8c,EAASllC,GAGtC,IAAIZ,GAAMsnC,EAAa/nB,EAIvB,KAHAvf,EAAMA,EAAIwO,OAAO,EAAGxO,EAAIsC,OAAS,GAG5B1B,EAAI,EAAOyB,EAAJzB,EAASA,IACjBwiC,EAAQpa,YAAY6e,EAAaD,WAAWhnC,GAGhD,OAAOZ,GAzuBX,GAAID,GAAWN,EAAQ,cAEnBghC,EAAgB1gC,EAAM0gC,cACtB97B,EAAY5E,EAAM4E,UAClB+C,EAAS3H,EAAM2H,OACfmG,EAAU9N,EAAM8N,QAChBC,EAAe/N,EAAM+N,aAGrBmW,EAAO,UACP6jB,EAAQ7jB,EAAO,cAEf2e,EAAUkF,EAAQ,aAClB3kB,EAAU2kB,EAAQ,cAClB/jB,EAAQ+jB,EAAQ,qBAEhBrF,EAAsBxe,EAAO,+CA+uBjC1kB,GAAQ0kB,KAAOA,EACf1kB,EAAQuoC,MAAQA,EAChBvoC,EAAQwkB,MAAQA,EAChBxkB,EAAQ4jB,QAAUA,EAElB5jB,EAAQohC,+BAAiCA,EACzCphC,EAAQ4hC,mBAAqBA,EAC7B5hC,EAAQmkC,eAAiBA,EACzBnkC,EAAQqmC,kBAAoBA,EAC5BrmC,EAAQgkC,iBAAmBA,EAC3BhkC,EAAQ2jB,cAAgBA,EACxB3jB,EAAQ2hC,kBAAoBA,EAC5B3hC,EAAQukC,WAAaA,EACrBvkC,EAAQ+jB,iBAAmBA,EAC3B/jB,EAAQ0kC,qBAAuBA,EAC/B1kC,EAAQ2kC,kBAAoBA,EAC5B3kC,EAAQskB,qBAAuBA,EAC/BtkB,EAAQsjC,0BAA4BA,EACpCtjC,EAAQkkB,aAAeA,EACvBlkB,EAAQujB,aAAeA,EACvBvjB,EAAQwjB,gBAAkBA,EAC1BxjB,EAAQojC,aAAeA,EACvBpjC,EAAQkmC,OAASA,EACjBlmC,EAAQikC,gBAAkBA,EAC1BjkC,EAAQ8mC,cAAgBA,EACxB9mC,EAAQknC,eAAiBA,EACzBlnC,EAAQ0nC,iBAAmBA,EAC3B1nC,EAAQinC,oBAAsBA,EAC9BjnC,EAAQwmC,WAAaA,EACrBxmC,EAAQokB,SAAWA,EACnBpkB,EAAQ2jC,iBAAmBA,EAC3B3jC,EAAQ+nC,aAAeA,EACvB/nC,EAAQooC,wBAA0BA,EAClCpoC,EAAQgmC,kBAAoBA;GAGxBwC,WAEAtoC,QAAU,SAAS4+B,GACnB,GAAI18B,GAAO08B,EAAK9uB,UAAU8uB,EAAKnhB,YAAY,KAAK,EAAEmhB,EAAK/7B,OAAO,EAC9D,IAAIylC,QAAQpmC,GAAS,MAAOomC,SAAQpmC,GAAMpC,OAI1C,IAFAwoC,QAAQpmC,IAAUpC,YAClByoC,QAAQC,IAAItmC,GACC,QAATA,GAIJ,MADAvB,OAAMuB,GAAMo4B,KAAKj2B,KAAKikC,QAAQpmC,GAAMpC,QAAQwoC,QAAQpmC,GAAMlC,SACnDsoC,QAAQpmC,GAAMpC,QAGzBkJ,QAAOy/B,WACP5oC,KAAKy6B,KAAKj2B,KAAK2E,OAAOy/B,QAAQz/B,OAAOy/B,QAAQzoC", "sourcesContent": ["/*\r\n * Licensed to the Apache Software Foundation (ASF) under one\r\n * or more contributor license agreements.  See the NOTICE file\r\n * distributed with this work for additional information\r\n * regarding copyright ownership.  The ASF licenses this file\r\n * to you under the Apache License, Version 2.0 (the\r\n * \"License\"); you may not use this file except in compliance\r\n * with the License.  You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing,\r\n * software distributed under the License is distributed on an\r\n * \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n * KIND, either express or implied.  See the License for the\r\n * specific language governing permissions and limitations\r\n * under the License.\r\n */\r\nvar init = function(exports, module, require) {\r\n  \r\n\r\n// version information \r\nexports.version = { major: 4, minor: 0, build: 0 };\r\n\r\n// core stuff, always needed\r\nexports.deferred = require('./lib/deferred.js');\r\nexports.utils = require('./lib/utils.js');\r\n\r\n// only needed for xml metadata \r\nexports.xml = require('./lib/xml.js');\r\n\r\n// only need in browser case\r\nexports.oData = require('./lib/odata.js');\r\nexports.store = require('./lib/store.js');\r\nexports.cache = require('./lib/cache.js');\r\n\r\n\r\n\r\n\r\n};\r\n\r\nvar datas = {\"cache\" : function(exports, module, require) {\r\n'use strict';\r\n\r\n /** @module cache */\r\n\r\n//var odatajs = require('./odatajs/utils.js');\r\nvar utils =  require('./utils.js');\r\nvar deferred = require('./deferred.js');\r\nvar storeReq = require('./store.js');\r\nvar cacheSource = require('./cache/source.js');\r\n\r\n\r\nvar assigned = utils.assigned;\r\nvar delay = utils.delay;\r\nvar extend = utils.extend;\r\nvar djsassert = utils.djsassert;\r\nvar isArray = utils.isArray;\r\nvar normalizeURI = utils.normalizeURI;\r\nvar parseInt10 = utils.parseInt10;\r\nvar undefinedDefault = utils.undefinedDefault;\r\n\r\nvar createDeferred = deferred.createDeferred;\r\nvar DjsDeferred = deferred.DjsDeferred;\r\n\r\n\r\nvar getJsonValueArraryLength = utils.getJsonValueArraryLength;\r\nvar sliceJsonValueArray = utils.sliceJsonValueArray;\r\nvar concatJsonValueArray = utils.concatJsonValueArray;\r\n\r\n\r\n\r\n/** Appends a page's data to the operation data.\r\n * @param {Object} operation - Operation with  (i)ndex, (c)ount and (d)ata.\r\n * @param {Object} page - Page with (i)ndex, (c)ount and (d)ata.\r\n */\r\nfunction appendPage(operation, page) {\r\n\r\n    var intersection = intersectRanges(operation, page);\r\n    var start = 0;\r\n    var end = 0;\r\n    if (intersection) {\r\n        start = intersection.i - page.i;\r\n        end = start + (operation.c - getJsonValueArraryLength(operation.d));\r\n    }\r\n\r\n    operation.d = concatJsonValueArray(operation.d, sliceJsonValueArray(page.d, start, end));\r\n}\r\n\r\n/** Returns the {(i)ndex, (c)ount} range for the intersection of x and y.\r\n * @param {Object} x - Range with (i)ndex and (c)ount members.\r\n * @param {Object} y - Range with (i)ndex and (c)ount members.\r\n * @returns {Object} The intersection (i)ndex and (c)ount; undefined if there is no intersection.\r\n */\r\nfunction intersectRanges(x, y) {\r\n\r\n    var xLast = x.i + x.c;\r\n    var yLast = y.i + y.c;\r\n    var resultIndex = (x.i > y.i) ? x.i : y.i;\r\n    var resultLast = (xLast < yLast) ? xLast : yLast;\r\n    var result;\r\n    if (resultLast >= resultIndex) {\r\n        result = { i: resultIndex, c: resultLast - resultIndex };\r\n    }\r\n\r\n    return result;\r\n}\r\n\r\n/** Checks whether val is a defined number with value zero or greater.\r\n * @param {Number} val - Value to check.\r\n * @param {String} name - Parameter name to use in exception.\r\n * @throws Throws an exception if the check fails\r\n */\r\nfunction checkZeroGreater(val, name) {\r\n\r\n    if (val === undefined || typeof val !== \"number\") {\r\n        throw { message: \"'\" + name + \"' must be a number.\" };\r\n    }\r\n\r\n    if (isNaN(val) || val < 0 || !isFinite(val)) {\r\n        throw { message: \"'\" + name + \"' must be greater than or equal to zero.\" };\r\n    }\r\n}\r\n\r\n/** Checks whether val is undefined or a number with value greater than zero.\r\n * @param {Number} val - Value to check.\r\n * @param {String} name - Parameter name to use in exception.\r\n * @throws Throws an exception if the check fails\r\n */\r\nfunction checkUndefinedGreaterThanZero(val, name) {\r\n\r\n    if (val !== undefined) {\r\n        if (typeof val !== \"number\") {\r\n            throw { message: \"'\" + name + \"' must be a number.\" };\r\n        }\r\n\r\n        if (isNaN(val) || val <= 0 || !isFinite(val)) {\r\n            throw { message: \"'\" + name + \"' must be greater than zero.\" };\r\n        }\r\n    }\r\n}\r\n\r\n/** Checks whether val is undefined or a number\r\n * @param {Number} val - Value to check.\r\n * @param {String} name - Parameter name to use in exception.\r\n * @throws Throws an exception if the check fails\r\n */\r\nfunction checkUndefinedOrNumber(val, name) {\r\n    if (val !== undefined && (typeof val !== \"number\" || isNaN(val) || !isFinite(val))) {\r\n        throw { message: \"'\" + name + \"' must be a number.\" };\r\n    }\r\n}\r\n\r\n/** Performs a linear search on the specified array and removes the first instance of 'item'.\r\n * @param {Array} arr - Array to search.\r\n * @param {*} item - Item being sought.\r\n * @returns {Boolean} true if the item was removed otherwise false\r\n */\r\nfunction removeFromArray(arr, item) {\r\n\r\n    var i, len;\r\n    for (i = 0, len = arr.length; i < len; i++) {\r\n        if (arr[i] === item) {\r\n            arr.splice(i, 1);\r\n            return true;\r\n        }\r\n    }\r\n\r\n    return false;\r\n}\r\n\r\n/** Estimates the size of an object in bytes.\r\n * Object trees are traversed recursively\r\n * @param {Object} object - Object to determine the size of.\r\n * @returns {Number} Estimated size of the object in bytes.\r\n */\r\nfunction estimateSize(object) {\r\n    var size = 0;\r\n    var type = typeof object;\r\n\r\n    if (type === \"object\" && object) {\r\n        for (var name in object) {\r\n            size += name.length * 2 + estimateSize(object[name]);\r\n        }\r\n    } else if (type === \"string\") {\r\n        size = object.length * 2;\r\n    } else {\r\n        size = 8;\r\n    }\r\n    return size;\r\n}\r\n\r\n/** Snaps low and high indices into page sizes and returns a range.\r\n * @param {Number} lowIndex - Low index to snap to a lower value.\r\n * @param {Number} highIndex - High index to snap to a higher value.\r\n * @param {Number} pageSize - Page size to snap to.\r\n * @returns {Object} A range with (i)ndex and (c)ount of elements.\r\n */\r\nfunction snapToPageBoundaries(lowIndex, highIndex, pageSize) {\r\n    lowIndex = Math.floor(lowIndex / pageSize) * pageSize;\r\n    highIndex = Math.ceil((highIndex + 1) / pageSize) * pageSize;\r\n    return { i: lowIndex, c: highIndex - lowIndex };\r\n}\r\n\r\n// The DataCache is implemented using state machines.  The following constants are used to properly\r\n// identify and label the states that these machines transition to.\r\nvar CACHE_STATE_DESTROY  = \"destroy\";\r\nvar CACHE_STATE_IDLE     = \"idle\";\r\nvar CACHE_STATE_INIT     = \"init\";\r\nvar CACHE_STATE_READ     = \"read\";\r\nvar CACHE_STATE_PREFETCH = \"prefetch\";\r\nvar CACHE_STATE_WRITE    = \"write\";\r\n\r\n// DataCacheOperation state machine states.\r\n// Transitions on operations also depend on the cache current of the cache.\r\nvar OPERATION_STATE_CANCEL = \"cancel\";\r\nvar OPERATION_STATE_END    = \"end\";\r\nvar OPERATION_STATE_ERROR  = \"error\";\r\nvar OPERATION_STATE_START  = \"start\";\r\nvar OPERATION_STATE_WAIT   = \"wait\";\r\n\r\n// Destroy state machine states\r\nvar DESTROY_STATE_CLEAR = \"clear\";\r\n\r\n// Read / Prefetch state machine states\r\nvar READ_STATE_DONE   = \"done\";\r\nvar READ_STATE_LOCAL  = \"local\";\r\nvar READ_STATE_SAVE   = \"save\";\r\nvar READ_STATE_SOURCE = \"source\";\r\n\r\n/** Creates a new operation object.\r\n * @class DataCacheOperation\r\n * @param {Function} stateMachine - State machine that describes the specific behavior of the operation.\r\n * @param {DjsDeferred} promise - Promise for requested values.\r\n * @param {Boolean} isCancelable - Whether this operation can be canceled or not.\r\n * @param {Number} index - Index of first item requested.\r\n * @param {Number} count - Count of items requested.\r\n * @param {Array} data - Array with the items requested by the operation.\r\n * @param {Number} pending - Total number of pending prefetch records.\r\n * @returns {DataCacheOperation} A new data cache operation instance.\r\n */\r\nfunction DataCacheOperation(stateMachine, promise, isCancelable, index, count, data, pending) {\r\n\r\n   var stateData;\r\n    var cacheState;\r\n    var that = this;\r\n\r\n    that.p = promise;\r\n    that.i = index;\r\n    that.c = count;\r\n    that.d = data;\r\n    that.s = OPERATION_STATE_START;\r\n\r\n    that.canceled = false;\r\n    that.pending = pending;\r\n    that.oncomplete = null;\r\n\r\n    /** Transitions this operation to the cancel state and sets the canceled flag to true.\r\n     * The function is a no-op if the operation is non-cancelable.\r\n     * @method DataCacheOperation#cancel\r\n     */\r\n    that.cancel = function cancel() {\r\n\r\n        if (!isCancelable) {\r\n            return;\r\n        }\r\n\r\n        var state = that.s;\r\n        if (state !== OPERATION_STATE_ERROR && state !== OPERATION_STATE_END && state !== OPERATION_STATE_CANCEL) {\r\n            that.canceled = true;\r\n            that.transition(OPERATION_STATE_CANCEL, stateData);\r\n        }\r\n    };\r\n\r\n    /** Transitions this operation to the end state.\r\n     * @method DataCacheOperation#complete\r\n     */\r\n    that.complete = function () {\r\n\r\n        djsassert(that.s !== OPERATION_STATE_END, \"DataCacheOperation.complete() - operation is in the end state\", that);\r\n        that.transition(OPERATION_STATE_END, stateData);\r\n    };\r\n\r\n    /** Transitions this operation to the error state.\r\n     * @method DataCacheOperation#error\r\n     */\r\n    that.error = function (err) {\r\n        if (!that.canceled) {\r\n            djsassert(that.s !== OPERATION_STATE_END, \"DataCacheOperation.error() - operation is in the end state\", that);\r\n            djsassert(that.s !== OPERATION_STATE_ERROR, \"DataCacheOperation.error() - operation is in the error state\", that);\r\n            that.transition(OPERATION_STATE_ERROR, err);\r\n        }\r\n    };\r\n\r\n    /** Executes the operation's current state in the context of a new cache state.\r\n     * @method DataCacheOperation#run\r\n     * @param {Object} state - New cache state.\r\n     */\r\n    that.run = function (state) {\r\n\r\n        cacheState = state;\r\n        that.transition(that.s, stateData);\r\n    };\r\n\r\n    /** Transitions this operation to the wait state.\r\n     * @method DataCacheOperation#wait\r\n     */\r\n    that.wait = function (data) {\r\n\r\n        djsassert(that.s !== OPERATION_STATE_END, \"DataCacheOperation.wait() - operation is in the end state\", that);\r\n        that.transition(OPERATION_STATE_WAIT, data);\r\n    };\r\n\r\n    /** State machine that describes all operations common behavior.\r\n     * @method DataCacheOperation#operationStateMachine\r\n     * @param {Object} opTargetState - Operation state to transition to.\r\n     * @param {Object} cacheState - Current cache state.\r\n     * @param {Object} [data] - Additional data passed to the state.\r\n     */\r\n    var operationStateMachine = function (opTargetState, cacheState, data) {\r\n\r\n        switch (opTargetState) {\r\n            case OPERATION_STATE_START:\r\n                // Initial state of the operation. The operation will remain in this state until the cache has been fully initialized.\r\n                if (cacheState !== CACHE_STATE_INIT) {\r\n                    stateMachine(that, opTargetState, cacheState, data);\r\n                }\r\n                break;\r\n\r\n            case OPERATION_STATE_WAIT:\r\n                // Wait state indicating that the operation is active but waiting for an asynchronous operation to complete.\r\n                stateMachine(that, opTargetState, cacheState, data);\r\n                break;\r\n\r\n            case OPERATION_STATE_CANCEL:\r\n                // Cancel state.\r\n                stateMachine(that, opTargetState, cacheState, data);\r\n                that.fireCanceled();\r\n                that.transition(OPERATION_STATE_END);\r\n                break;\r\n\r\n            case OPERATION_STATE_ERROR:\r\n                // Error state. Data is expected to be an object detailing the error condition.\r\n                stateMachine(that, opTargetState, cacheState, data);\r\n                that.canceled = true;\r\n                that.fireRejected(data);\r\n                that.transition(OPERATION_STATE_END);\r\n                break;\r\n\r\n            case OPERATION_STATE_END:\r\n                // Final state of the operation.\r\n                if (that.oncomplete) {\r\n                    that.oncomplete(that);\r\n                }\r\n                if (!that.canceled) {\r\n                    that.fireResolved();\r\n                }\r\n                stateMachine(that, opTargetState, cacheState, data);\r\n                break;\r\n\r\n            default:\r\n                // Any other state is passed down to the state machine describing the operation's specific behavior.\r\n\r\n                if (true) {\r\n                    // Check that the state machine actually handled the sate.\r\n                    var handled = stateMachine(that, opTargetState, cacheState, data);\r\n                    djsassert(handled, \"Bad operation state: \" + opTargetState + \" cacheState: \" + cacheState, this);\r\n                } else {\r\n\r\n                    stateMachine(that, opTargetState, cacheState, data);\r\n\r\n                }\r\n\r\n                break;\r\n        }\r\n    };\r\n\r\n\r\n\r\n    /** Transitions this operation to a new state.\r\n     * @method DataCacheOperation#transition\r\n     * @param {Object} state - State to transition the operation to.\r\n     * @param {Object} [data] - \r\n     */\r\n    that.transition = function (state, data) {\r\n        that.s = state;\r\n        stateData = data;\r\n        operationStateMachine(state, cacheState, data);\r\n    };\r\n    \r\n    return that;\r\n}\r\n\r\n/** Fires a resolved notification as necessary.\r\n * @method DataCacheOperation#fireResolved\r\n */\r\nDataCacheOperation.prototype.fireResolved = function () {\r\n\r\n    // Fire the resolve just once.\r\n    var p = this.p;\r\n    if (p) {\r\n        this.p = null;\r\n        p.resolve(this.d);\r\n    }\r\n};\r\n\r\n/** Fires a rejected notification as necessary.\r\n * @method DataCacheOperation#fireRejected\r\n */\r\nDataCacheOperation.prototype.fireRejected = function (reason) {\r\n\r\n    // Fire the rejection just once.\r\n    var p = this.p;\r\n    if (p) {\r\n        this.p = null;\r\n        p.reject(reason);\r\n    }\r\n};\r\n\r\n/** Fires a canceled notification as necessary.\r\n * @method DataCacheOperation#fireCanceled\r\n */\r\nDataCacheOperation.prototype.fireCanceled = function () {\r\n\r\n    this.fireRejected({ canceled: true, message: \"Operation canceled\" });\r\n};\r\n\r\n\r\n/** Creates a data cache for a collection that is efficiently loaded on-demand.\r\n * @class DataCache\r\n * @param options - Options for the data cache, including name, source, pageSize,\r\n * prefetchSize, cacheSize, storage mechanism, and initial prefetch and local-data handler.\r\n * @returns {DataCache} A new data cache instance.\r\n */\r\nfunction DataCache(options) {\r\n\r\n    var state = CACHE_STATE_INIT;\r\n    var stats = { counts: 0, netReads: 0, prefetches: 0, cacheReads: 0 };\r\n\r\n    var clearOperations = [];\r\n    var readOperations = [];\r\n    var prefetchOperations = [];\r\n\r\n    var actualCacheSize = 0;                                             // Actual cache size in bytes.\r\n    var allDataLocal = false;                                            // Whether all data is local.\r\n    var cacheSize = undefinedDefault(options.cacheSize, 1048576);        // Requested cache size in bytes, default 1 MB.\r\n    var collectionCount = 0;                                             // Number of elements in the server collection.\r\n    var highestSavedPage = 0;                                            // Highest index of all the saved pages.\r\n    var highestSavedPageSize = 0;                                        // Item count of the saved page with the highest index.\r\n    var overflowed = cacheSize === 0;                                    // If the cache has overflowed (actualCacheSize > cacheSize or cacheSize == 0);\r\n    var pageSize = undefinedDefault(options.pageSize, 50);               // Number of elements to store per page.\r\n    var prefetchSize = undefinedDefault(options.prefetchSize, pageSize); // Number of elements to prefetch from the source when the cache is idling.\r\n    var version = \"1.0\";\r\n    var cacheFailure;\r\n\r\n    var pendingOperations = 0;\r\n\r\n    var source = options.source;\r\n    if (typeof source === \"string\") {\r\n        // Create a new cache source.\r\n        source = new cacheSource.ODataCacheSource(options);\r\n    }\r\n    source.options = options;\r\n\r\n    // Create a cache local store.\r\n    var store = storeReq.createStore(options.name, options.mechanism);\r\n\r\n    var that = this;\r\n\r\n    that.onidle = options.idle;\r\n    that.stats = stats;\r\n\r\n    /** Counts the number of items in the collection.\r\n     * @method DataCache#count\r\n     * @returns {Object} A promise with the number of items.\r\n     */\r\n    that.count = function () {\r\n\r\n        if (cacheFailure) {\r\n            throw cacheFailure;\r\n        }\r\n\r\n        var deferred = createDeferred();\r\n        var canceled = false;\r\n\r\n        if (allDataLocal) {\r\n            delay(function () {\r\n                deferred.resolve(collectionCount);\r\n            });\r\n\r\n            return deferred.promise();\r\n        }\r\n\r\n        // TODO: Consider returning the local data count instead once allDataLocal flag is set to true.\r\n        var request = source.count(function (count) {\r\n            request = null;\r\n            stats.counts++;\r\n            deferred.resolve(count);\r\n        }, function (err) {\r\n            request = null;\r\n            deferred.reject(extend(err, { canceled: canceled }));\r\n        });\r\n\r\n        return extend(deferred.promise(), {\r\n\r\n             /** Aborts the count operation (used within promise callback)\r\n              * @method DataCache#cancelCount\r\n              */\r\n            cancel: function () {\r\n               \r\n                if (request) {\r\n                    canceled = true;\r\n                    request.abort();\r\n                    request = null;\r\n                }\r\n            }\r\n        });\r\n    };\r\n\r\n    /** Cancels all running operations and clears all local data associated with this cache.\r\n     * New read requests made while a clear operation is in progress will not be canceled.\r\n     * Instead they will be queued for execution once the operation is completed.\r\n     * @method DataCache#clear\r\n     * @returns {Object} A promise that has no value and can't be canceled.\r\n     */\r\n    that.clear = function () {\r\n\r\n        if (cacheFailure) {\r\n            throw cacheFailure;\r\n        }\r\n\r\n        if (clearOperations.length === 0) {\r\n            var deferred = createDeferred();\r\n            var op = new DataCacheOperation(destroyStateMachine, deferred, false);\r\n            queueAndStart(op, clearOperations);\r\n            return deferred.promise();\r\n        }\r\n        return clearOperations[0].p;\r\n    };\r\n\r\n    /** Filters the cache data based a predicate.\r\n     * Specifying a negative count value will yield all the items in the cache that satisfy the predicate.\r\n     * @method DataCache#filterForward\r\n     * @param {Number} index - The index of the item to start filtering forward from.\r\n     * @param {Number} count - Maximum number of items to include in the result.\r\n     * @param {Function} predicate - Callback function returning a boolean that determines whether an item should be included in the result or not.\r\n     * @returns {DjsDeferred} A promise for an array of results.\r\n     */\r\n    that.filterForward = function (index, count, predicate) {\r\n        return filter(index, count, predicate, false);\r\n    };\r\n\r\n    /** Filters the cache data based a predicate.\r\n     * Specifying a negative count value will yield all the items in the cache that satisfy the predicate.\r\n     * @method DataCache#filterBack\r\n     * @param {Number} index - The index of the item to start filtering backward from.\r\n     * @param {Number} count - Maximum number of items to include in the result.\r\n     * @param {Function} predicate - Callback function returning a boolean that determines whether an item should be included in the result or not.\r\n     * @returns {DjsDeferred} A promise for an array of results.\r\n     */\r\n    that.filterBack = function (index, count, predicate) {\r\n        return filter(index, count, predicate, true);\r\n    };\r\n\r\n    /** Reads a range of adjacent records.\r\n     * New read requests made while a clear operation is in progress will not be canceled.\r\n     * Instead they will be queued for execution once the operation is completed.\r\n     * @method DataCache#readRange\r\n     * @param {Number} index - Zero-based index of record range to read.\r\n     * @param {Number} count - Number of records in the range.\r\n     * @returns {DjsDeferred} A promise for an array of records; less records may be returned if the\r\n     * end of the collection is found.\r\n     */\r\n    that.readRange = function (index, count) {\r\n\r\n        checkZeroGreater(index, \"index\");\r\n        checkZeroGreater(count, \"count\");\r\n\r\n        if (cacheFailure) {\r\n            throw cacheFailure;\r\n        }\r\n\r\n        var deferred = createDeferred();\r\n\r\n        // Merging read operations would be a nice optimization here.\r\n        var op = new DataCacheOperation(readStateMachine, deferred, true, index, count, {}, 0);\r\n        queueAndStart(op, readOperations);\r\n\r\n        return extend(deferred.promise(), {\r\n            cancel: function () {\r\n                /** Aborts the readRange operation  (used within promise callback)\r\n                 * @method DataCache#cancelReadRange\r\n                 */\r\n                op.cancel();\r\n            }\r\n        });\r\n    };\r\n\r\n    /** Creates an Observable object that enumerates all the cache contents.\r\n     * @method DataCache#toObservable\r\n     * @returns A new Observable object that enumerates all the cache contents.\r\n     */\r\n    that.ToObservable = that.toObservable = function () {\r\n        if ( !utils.inBrowser()) {\r\n            throw { message: \"Only in broser supported\" };\r\n        }\r\n\r\n        if (!window.Rx || !window.Rx.Observable) {\r\n            throw { message: \"Rx library not available - include rx.js\" };\r\n        }\r\n\r\n        if (cacheFailure) {\r\n            throw cacheFailure;\r\n        }\r\n\r\n        //return window.Rx.Observable.create(function (obs) {\r\n        return new window.Rx.Observable(function (obs) {\r\n            var disposed = false;\r\n            var index = 0;\r\n\r\n            var errorCallback = function (error) {\r\n                if (!disposed) {\r\n                    obs.onError(error);\r\n                }\r\n            };\r\n\r\n            var successCallback = function (data) {\r\n                if (!disposed) {\r\n                    var i, len;\r\n                    for (i = 0, len = data.value.length; i < len; i++) {\r\n                        // The wrapper automatically checks for Dispose\r\n                        // on the observer, so we don't need to check it here.\r\n                        //obs.next(data.value[i]);\r\n                        obs.onNext(data.value[i]);\r\n                    }\r\n\r\n                    if (data.value.length < pageSize) {\r\n                        //obs.completed();\r\n                        obs.onCompleted();\r\n                    } else {\r\n                        index += pageSize;\r\n                        that.readRange(index, pageSize).then(successCallback, errorCallback);\r\n                    }\r\n                }\r\n            };\r\n\r\n            that.readRange(index, pageSize).then(successCallback, errorCallback);\r\n\r\n            return { Dispose: function () { \r\n                obs.dispose(); // otherwise the check isStopped obs.onNext(data.value[i]);\r\n                disposed = true; \r\n                } };\r\n        });\r\n    };\r\n\r\n    /** Creates a function that handles a callback by setting the cache into failure mode.\r\n     * @method DataCache~cacheFailureCallback\r\n     * @param {String} message - Message text.\r\n     * @returns {Function} Function to use as error callback.\r\n     * This function will specifically handle problems with critical store resources\r\n     * during cache initialization.\r\n     */\r\n    var cacheFailureCallback = function (message) {\r\n        \r\n\r\n        return function (error) {\r\n            cacheFailure = { message: message, error: error };\r\n\r\n            // Destroy any pending clear or read operations.\r\n            // At this point there should be no prefetch operations.\r\n            // Count operations will go through but are benign because they\r\n            // won't interact with the store.\r\n            djsassert(prefetchOperations.length === 0, \"prefetchOperations.length === 0\");\r\n            var i, len;\r\n            for (i = 0, len = readOperations.length; i < len; i++) {\r\n                readOperations[i].fireRejected(cacheFailure);\r\n            }\r\n            for (i = 0, len = clearOperations.length; i < len; i++) {\r\n                clearOperations[i].fireRejected(cacheFailure);\r\n            }\r\n\r\n            // Null out the operation arrays.\r\n            readOperations = clearOperations = null;\r\n        };\r\n    };\r\n\r\n    /** Updates the cache's state and signals all pending operations of the change.\r\n     * @method DataCache~changeState\r\n     * @param {Object} newState - New cache state.\r\n     * This method is a no-op if the cache's current state and the new state are the same.\r\n     */\r\n    var changeState = function (newState) {\r\n\r\n        if (newState !== state) {\r\n            state = newState;\r\n            var operations = clearOperations.concat(readOperations, prefetchOperations);\r\n            var i, len;\r\n            for (i = 0, len = operations.length; i < len; i++) {\r\n                operations[i].run(state);\r\n            }\r\n        }\r\n    };\r\n\r\n    /** Removes all the data stored in the cache.\r\n     * @method DataCache~clearStore\r\n     * @returns {DjsDeferred} A promise with no value.\r\n     */\r\n    var clearStore = function () {\r\n        djsassert(state === CACHE_STATE_DESTROY || state === CACHE_STATE_INIT, \"DataCache.clearStore() - cache is not on the destroy or initialize state, current sate = \" + state);\r\n\r\n        var deferred = new DjsDeferred();\r\n        store.clear(function () {\r\n\r\n            // Reset the cache settings.\r\n            actualCacheSize = 0;\r\n            allDataLocal = false;\r\n            collectionCount = 0;\r\n            highestSavedPage = 0;\r\n            highestSavedPageSize = 0;\r\n            overflowed = cacheSize === 0;\r\n\r\n            // version is not reset, in case there is other state in eg V1.1 that is still around.\r\n\r\n            // Reset the cache stats.\r\n            stats = { counts: 0, netReads: 0, prefetches: 0, cacheReads: 0 };\r\n            that.stats = stats;\r\n\r\n            store.close();\r\n            deferred.resolve();\r\n        }, function (err) {\r\n            deferred.reject(err);\r\n        });\r\n        return deferred;\r\n    };\r\n\r\n    /** Removes an operation from the caches queues and changes the cache state to idle.\r\n     * @method DataCache~dequeueOperation\r\n     * @param {DataCacheOperation} operation - Operation to dequeue.\r\n     * This method is used as a handler for the operation's oncomplete event.\r\n    */\r\n    var dequeueOperation = function (operation) {\r\n\r\n        var removed = removeFromArray(clearOperations, operation);\r\n        if (!removed) {\r\n            removed = removeFromArray(readOperations, operation);\r\n            if (!removed) {\r\n                removeFromArray(prefetchOperations, operation);\r\n            }\r\n        }\r\n\r\n        pendingOperations--;\r\n        changeState(CACHE_STATE_IDLE);\r\n    };\r\n\r\n    /** Requests data from the cache source.\r\n     * @method DataCache~fetchPage\r\n     * @param {Number} start - Zero-based index of items to request.\r\n     * @returns {DjsDeferred} A promise for a page object with (i)ndex, (c)ount, (d)ata.\r\n     */\r\n    var fetchPage = function (start) {\r\n\r\n        djsassert(state !== CACHE_STATE_DESTROY, \"DataCache.fetchPage() - cache is on the destroy state\");\r\n        djsassert(state !== CACHE_STATE_IDLE, \"DataCache.fetchPage() - cache is on the idle state\");\r\n\r\n        var deferred = new DjsDeferred();\r\n        var canceled = false;\r\n\r\n        var request = source.read(start, pageSize, function (data) {\r\n            var length = getJsonValueArraryLength(data);\r\n            var page = { i: start, c: length, d: data };\r\n            deferred.resolve(page);\r\n        }, function (err) {\r\n            deferred.reject(err);\r\n        });\r\n\r\n        return extend(deferred, {\r\n            cancel: function () {\r\n                if (request) {\r\n                    request.abort();\r\n                    canceled = true;\r\n                    request = null;\r\n                }\r\n            }\r\n        });\r\n    };\r\n\r\n    /** Filters the cache data based a predicate.\r\n     * @method DataCache~filter\r\n     * @param {Number} index - The index of the item to start filtering from.\r\n     * @param {Number} count - Maximum number of items to include in the result.\r\n     * @param {Function} predicate - Callback function returning a boolean that determines whether an item should be included in the result or not.\r\n     * @param {Boolean} backwards - True if the filtering should move backward from the specified index, falsey otherwise.\r\n     * Specifying a negative count value will yield all the items in the cache that satisfy the predicate.\r\n     * @returns {DjsDeferred} A promise for an array of results.\r\n     */\r\n    var filter = function (index, count, predicate, backwards) {\r\n\r\n        index = parseInt10(index);\r\n        count = parseInt10(count);\r\n\r\n        if (isNaN(index)) {\r\n            throw { message: \"'index' must be a valid number.\", index: index };\r\n        }\r\n        if (isNaN(count)) {\r\n            throw { message: \"'count' must be a valid number.\", count: count };\r\n        }\r\n\r\n        if (cacheFailure) {\r\n            throw cacheFailure;\r\n        }\r\n\r\n        index = Math.max(index, 0);\r\n\r\n        var deferred = createDeferred();\r\n        var returnData = {};\r\n        returnData.value = [];\r\n        var canceled = false;\r\n        var pendingReadRange = null;\r\n\r\n        var readMore = function (readIndex, readCount) {\r\n            if (!canceled) {\r\n                if (count > 0 && returnData.value.length >= count) {\r\n                    deferred.resolve(returnData);\r\n                } else {\r\n                    pendingReadRange = that.readRange(readIndex, readCount).then(function (data) {\r\n                        if (data[\"@odata.context\"] && !returnData[\"@odata.context\"]) {\r\n                            returnData[\"@odata.context\"] = data[\"@odata.context\"];\r\n                        }\r\n                        \r\n                        for (var i = 0, length = data.value.length; i < length && (count < 0 || returnData.value.length < count); i++) {\r\n                            var dataIndex = backwards ? length - i - 1 : i;\r\n                            var item = data.value[dataIndex];\r\n                            if (predicate(item)) {\r\n                                var element = {\r\n                                    index: readIndex + dataIndex,\r\n                                    item: item\r\n                                };\r\n\r\n                                backwards ? returnData.value.unshift(element) : returnData.value.push(element);\r\n                            }\r\n                        }\r\n\r\n                        // Have we reached the end of the collection?\r\n                        if ((!backwards && data.value.length < readCount) || (backwards && readIndex <= 0)) {\r\n                            deferred.resolve(returnData);\r\n                        } else {\r\n                            var nextIndex = backwards ? Math.max(readIndex - pageSize, 0) : readIndex + readCount;\r\n                            readMore(nextIndex, pageSize);\r\n                        }\r\n                    }, function (err) {\r\n                        deferred.reject(err);\r\n                    });\r\n                }\r\n            }\r\n        };\r\n\r\n        // Initially, we read from the given starting index to the next/previous page boundary\r\n        var initialPage = snapToPageBoundaries(index, index, pageSize);\r\n        var initialIndex = backwards ? initialPage.i : index;\r\n        var initialCount = backwards ? index - initialPage.i + 1 : initialPage.i + initialPage.c - index;\r\n        readMore(initialIndex, initialCount);\r\n\r\n        return extend(deferred.promise(), {\r\n            /** Aborts the filter operation (used within promise callback)\r\n            * @method DataCache#cancelFilter\r\n             */\r\n            cancel: function () {\r\n\r\n                if (pendingReadRange) {\r\n                    pendingReadRange.cancel();\r\n                }\r\n                canceled = true;\r\n            }\r\n        });\r\n    };\r\n\r\n    /** Fires an onidle event if any functions are assigned.\r\n     * @method DataCache~fireOnIdle\r\n    */\r\n    var fireOnIdle = function () {\r\n\r\n        if (that.onidle && pendingOperations === 0) {\r\n            that.onidle();\r\n        }\r\n    };\r\n\r\n    /** Creates and starts a new prefetch operation.\r\n     * @method DataCache~prefetch\r\n     * @param {Number} start - Zero-based index of the items to prefetch.\r\n     * This method is a no-op if any of the following conditions is true:\r\n     *     1.- prefetchSize is 0\r\n     *     2.- All data has been read and stored locally in the cache.\r\n     *     3.- There is already an all data prefetch operation queued.\r\n     *     4.- The cache has run out of available space (overflowed).\r\n    */\r\n    var prefetch = function (start) {\r\n        \r\n\r\n        if (allDataLocal || prefetchSize === 0 || overflowed) {\r\n            return;\r\n        }\r\n\r\n        djsassert(state === CACHE_STATE_READ, \"DataCache.prefetch() - cache is not on the read state, current state: \" + state);\r\n\r\n        if (prefetchOperations.length === 0 || (prefetchOperations[0] && prefetchOperations[0].c !== -1)) {\r\n            // Merging prefetch operations would be a nice optimization here.\r\n            var op = new DataCacheOperation(prefetchStateMachine, null, true, start, prefetchSize, null, prefetchSize);\r\n            queueAndStart(op, prefetchOperations);\r\n        }\r\n    };\r\n\r\n    /** Queues an operation and runs it.\r\n     * @param {DataCacheOperation} op - Operation to queue.\r\n     * @param {Array} queue - Array that will store the operation.\r\n     */\r\n    var queueAndStart = function (op, queue) {\r\n\r\n        op.oncomplete = dequeueOperation;\r\n        queue.push(op);\r\n        pendingOperations++;\r\n        op.run(state);\r\n    };\r\n\r\n    /** Requests a page from the cache local store.\r\n     * @method DataCache~readPage    \r\n     * @param {Number} key - Zero-based index of the reuqested page.\r\n     * @returns {DjsDeferred} A promise for a found flag and page object with (i)ndex, (c)ount, (d)ata, and (t)icks.\r\n     */\r\n    var readPage = function (key) {\r\n\r\n        djsassert(state !== CACHE_STATE_DESTROY, \"DataCache.readPage() - cache is on the destroy state\");\r\n\r\n        var canceled = false;\r\n        var deferred = extend(new DjsDeferred(), {\r\n            /** Aborts the readPage operation. (used within promise callback)\r\n             * @method DataCache#cancelReadPage\r\n             */\r\n            cancel: function () {\r\n                canceled = true;\r\n            }\r\n        });\r\n\r\n        var error = storeFailureCallback(deferred, \"Read page from store failure\");\r\n\r\n        store.contains(key, function (contained) {\r\n            if (canceled) {\r\n                return;\r\n            }\r\n            if (contained) {\r\n                store.read(key, function (_, data) {\r\n                    if (!canceled) {\r\n                        deferred.resolve(data !== undefined, data);\r\n                    }\r\n                }, error);\r\n                return;\r\n            }\r\n            deferred.resolve(false);\r\n        }, error);\r\n        return deferred;\r\n    };\r\n\r\n    /** Saves a page to the cache local store.\r\n     * @method DataCache~savePage    \r\n     * @param {Number} key - Zero-based index of the requested page.\r\n     * @param {Object} page - Object with (i)ndex, (c)ount, (d)ata, and (t)icks.\r\n     * @returns {DjsDeferred} A promise with no value.\r\n     */\r\n    var savePage = function (key, page) {\r\n\r\n        djsassert(state !== CACHE_STATE_DESTROY, \"DataCache.savePage() - cache is on the destroy state\");\r\n        djsassert(state !== CACHE_STATE_IDLE, \"DataCache.savePage() - cache is on the idle state\");\r\n\r\n        var canceled = false;\r\n\r\n        var deferred = extend(new DjsDeferred(), {\r\n            /** Aborts the savePage operation. (used within promise callback)\r\n             * @method DataCache#cancelReadPage\r\n             */\r\n            cancel: function () {\r\n                canceled = true;\r\n            }\r\n        });\r\n\r\n        var error = storeFailureCallback(deferred, \"Save page to store failure\");\r\n\r\n        var resolve = function () {\r\n            deferred.resolve(true);\r\n        };\r\n\r\n        if (page.c > 0) {\r\n            var pageBytes = estimateSize(page);\r\n            overflowed = cacheSize >= 0 && cacheSize < actualCacheSize + pageBytes;\r\n\r\n            if (!overflowed) {\r\n                store.addOrUpdate(key, page, function () {\r\n                    updateSettings(page, pageBytes);\r\n                    saveSettings(resolve, error);\r\n                }, error);\r\n            } else {\r\n                resolve();\r\n            }\r\n        } else {\r\n            updateSettings(page, 0);\r\n            saveSettings(resolve, error);\r\n        }\r\n        return deferred;\r\n    };\r\n\r\n    /** Saves the cache's current settings to the local store.\r\n     * @method DataCache~saveSettings    \r\n     * @param {Function} success - Success callback.\r\n     * @param {Function} error - Errror callback.\r\n     */\r\n    var saveSettings = function (success, error) {\r\n\r\n        var settings = {\r\n            actualCacheSize: actualCacheSize,\r\n            allDataLocal: allDataLocal,\r\n            cacheSize: cacheSize,\r\n            collectionCount: collectionCount,\r\n            highestSavedPage: highestSavedPage,\r\n            highestSavedPageSize: highestSavedPageSize,\r\n            pageSize: pageSize,\r\n            sourceId: source.identifier,\r\n            version: version\r\n        };\r\n\r\n        store.addOrUpdate(\"__settings\", settings, success, error);\r\n    };\r\n\r\n    /** Creates a function that handles a store error.\r\n     * @method DataCache~storeFailureCallback    \r\n     * @param {DjsDeferred} deferred - Deferred object to resolve.\r\n     * @returns {Function} Function to use as error callback.\r\n    \r\n     * This function will specifically handle problems when interacting with the store.\r\n     */\r\n    var storeFailureCallback = function (deferred/*, message*/) {\r\n        \r\n\r\n        return function (/*error*/) {\r\n            // var console = windo1w.console;\r\n            // if (console && console.log) {\r\n            //    console.log(message);\r\n            //    console.dir(error);\r\n            // }\r\n            deferred.resolve(false);\r\n        };\r\n    };\r\n\r\n    /** Updates the cache's settings based on a page object.\r\n     * @method DataCache~updateSettings    \r\n     * @param {Object} page - Object with (i)ndex, (c)ount, (d)ata.\r\n     * @param {Number} pageBytes - Size of the page in bytes.\r\n     */\r\n    var updateSettings = function (page, pageBytes) {\r\n\r\n        var pageCount = page.c;\r\n        var pageIndex = page.i;\r\n\r\n        // Detect the collection size.\r\n        if (pageCount === 0) {\r\n            if (highestSavedPage === pageIndex - pageSize) {\r\n                collectionCount = highestSavedPage + highestSavedPageSize;\r\n            }\r\n        } else {\r\n            highestSavedPage = Math.max(highestSavedPage, pageIndex);\r\n            if (highestSavedPage === pageIndex) {\r\n                highestSavedPageSize = pageCount;\r\n            }\r\n            actualCacheSize += pageBytes;\r\n            if (pageCount < pageSize && !collectionCount) {\r\n                collectionCount = pageIndex + pageCount;\r\n            }\r\n        }\r\n\r\n        // Detect the end of the collection.\r\n        if (!allDataLocal && collectionCount === highestSavedPage + highestSavedPageSize) {\r\n            allDataLocal = true;\r\n        }\r\n    };\r\n\r\n    /** State machine describing the behavior for cancelling a read or prefetch operation.\r\n     * @method DataCache~cancelStateMachine    \r\n     * @param {DataCacheOperation} operation - Operation being run.\r\n     * @param {Object} opTargetState - Operation state to transition to.\r\n     * @param {Object} cacheState - Current cache state.\r\n     * @param {Object} [data] - \r\n     * This state machine contains behavior common to read and prefetch operations.\r\n     */\r\n    var cancelStateMachine = function (operation, opTargetState, cacheState, data) {\r\n        \r\n\r\n        var canceled = operation.canceled && opTargetState !== OPERATION_STATE_END;\r\n        if (canceled) {\r\n            if (opTargetState === OPERATION_STATE_CANCEL) {\r\n                // Cancel state.\r\n                // Data is expected to be any pending request made to the cache.\r\n                if (data && data.cancel) {\r\n                    data.cancel();\r\n                }\r\n            }\r\n        }\r\n        return canceled;\r\n    };\r\n\r\n    /** State machine describing the behavior of a clear operation.\r\n     * @method DataCache~destroyStateMachine    \r\n     * @param {DataCacheOperation} operation - Operation being run.\r\n     * @param {Object} opTargetState - Operation state to transition to.\r\n     * @param {Object} cacheState - Current cache state.\r\n    \r\n     * Clear operations have the highest priority and can't be interrupted by other operations; however,\r\n     * they will preempt any other operation currently executing.\r\n     */\r\n    var destroyStateMachine = function (operation, opTargetState, cacheState) {\r\n        \r\n\r\n        var transition = operation.transition;\r\n\r\n        // Signal the cache that a clear operation is running.\r\n        if (cacheState !== CACHE_STATE_DESTROY) {\r\n            changeState(CACHE_STATE_DESTROY);\r\n            return true;\r\n        }\r\n\r\n        switch (opTargetState) {\r\n            case OPERATION_STATE_START:\r\n                // Initial state of the operation.\r\n                transition(DESTROY_STATE_CLEAR);\r\n                break;\r\n\r\n            case OPERATION_STATE_END:\r\n                // State that signals the operation is done.\r\n                fireOnIdle();\r\n                break;\r\n\r\n            case DESTROY_STATE_CLEAR:\r\n                // State that clears all the local data of the cache.\r\n                clearStore().then(function () {\r\n                    // Terminate the operation once the local store has been cleared.\r\n                    operation.complete();\r\n                });\r\n                // Wait until the clear request completes.\r\n                operation.wait();\r\n                break;\r\n\r\n            default:\r\n                return false;\r\n        }\r\n        return true;\r\n    };\r\n\r\n    /** State machine describing the behavior of a prefetch operation.\r\n     * @method DataCache~prefetchStateMachine    \r\n     * @param {DataCacheOperation} operation - Operation being run.\r\n     * @param {Object} opTargetState - Operation state to transition to.\r\n     * @param {Object} cacheState - Current cache state.\r\n     * @param {Object} [data] - \r\n    \r\n     *  Prefetch operations have the lowest priority and will be interrupted by operations of\r\n     *  other kinds. A preempted prefetch operation will resume its execution only when the state\r\n     *  of the cache returns to idle.\r\n     * \r\n     *  If a clear operation starts executing then all the prefetch operations are canceled,\r\n     *  even if they haven't started executing yet.\r\n     */\r\n    var prefetchStateMachine = function (operation, opTargetState, cacheState, data) {\r\n        \r\n\r\n        // Handle cancelation\r\n        if (!cancelStateMachine(operation, opTargetState, cacheState, data)) {\r\n\r\n            var transition = operation.transition;\r\n\r\n            // Handle preemption\r\n            if (cacheState !== CACHE_STATE_PREFETCH) {\r\n                if (cacheState === CACHE_STATE_DESTROY) {\r\n                    if (opTargetState !== OPERATION_STATE_CANCEL) {\r\n                        operation.cancel();\r\n                    }\r\n                } else if (cacheState === CACHE_STATE_IDLE) {\r\n                    // Signal the cache that a prefetch operation is running.\r\n                    changeState(CACHE_STATE_PREFETCH);\r\n                }\r\n                return true;\r\n            }\r\n\r\n            switch (opTargetState) {\r\n                case OPERATION_STATE_START:\r\n                    // Initial state of the operation.\r\n                    if (prefetchOperations[0] === operation) {\r\n                        transition(READ_STATE_LOCAL, operation.i);\r\n                    }\r\n                    break;\r\n\r\n                case READ_STATE_DONE:\r\n                    // State that determines if the operation can be resolved or has to\r\n                    // continue processing.\r\n                    // Data is expected to be the read page.\r\n                    var pending = operation.pending;\r\n\r\n                    if (pending > 0) {\r\n                        pending -= Math.min(pending, data.c);\r\n                    }\r\n\r\n                    // Are we done, or has all the data been stored?\r\n                    if (allDataLocal || pending === 0 || data.c < pageSize || overflowed) {\r\n                        operation.complete();\r\n                    } else {\r\n                        // Continue processing the operation.\r\n                        operation.pending = pending;\r\n                        transition(READ_STATE_LOCAL, data.i + pageSize);\r\n                    }\r\n                    break;\r\n\r\n                default:\r\n                    return readSaveStateMachine(operation, opTargetState, cacheState, data, true);\r\n            }\r\n        }\r\n        return true;\r\n    };\r\n\r\n    /** State machine describing the behavior of a read operation.\r\n     * @method DataCache~readStateMachine    \r\n     * @param {DataCacheOperation} operation - Operation being run.\r\n     * @param {Object} opTargetState - Operation state to transition to.\r\n     * @param {Object} cacheState - Current cache state.\r\n     * @param {Object} [data] - \r\n    \r\n     * Read operations have a higher priority than prefetch operations, but lower than\r\n     * clear operations. They will preempt any prefetch operation currently running\r\n     * but will be interrupted by a clear operation.\r\n     *          \r\n     * If a clear operation starts executing then all the currently running\r\n     * read operations are canceled. Read operations that haven't started yet will\r\n     * wait in the start state until the destory operation finishes.\r\n     */\r\n    var readStateMachine = function (operation, opTargetState, cacheState, data) {\r\n        \r\n\r\n        // Handle cancelation\r\n        if (!cancelStateMachine(operation, opTargetState, cacheState, data)) {\r\n\r\n            var transition = operation.transition;\r\n\r\n            // Handle preemption\r\n            if (cacheState !== CACHE_STATE_READ && opTargetState !== OPERATION_STATE_START) {\r\n                if (cacheState === CACHE_STATE_DESTROY) {\r\n                    if (opTargetState !== OPERATION_STATE_START) {\r\n                        operation.cancel();\r\n                    }\r\n                } else if (cacheState !== CACHE_STATE_WRITE) {\r\n                    // Signal the cache that a read operation is running.\r\n                    djsassert(state == CACHE_STATE_IDLE || state === CACHE_STATE_PREFETCH, \"DataCache.readStateMachine() - cache is not on the read or idle state.\");\r\n                    changeState(CACHE_STATE_READ);\r\n                }\r\n\r\n                return true;\r\n            }\r\n\r\n            switch (opTargetState) {\r\n                case OPERATION_STATE_START:\r\n                    // Initial state of the operation.\r\n                    // Wait until the cache is idle or prefetching.\r\n                    if (cacheState === CACHE_STATE_IDLE || cacheState === CACHE_STATE_PREFETCH) {\r\n                        // Signal the cache that a read operation is running.\r\n                        changeState(CACHE_STATE_READ);\r\n                        if (operation.c >= 0) {\r\n                            // Snap the requested range to a page boundary.\r\n                            var range = snapToPageBoundaries(operation.i, operation.c, pageSize);\r\n                            transition(READ_STATE_LOCAL, range.i);\r\n                        } else {\r\n                            transition(READ_STATE_DONE, operation);\r\n                        }\r\n                    }\r\n                    break;\r\n\r\n                case READ_STATE_DONE:\r\n                    // State that determines if the operation can be resolved or has to\r\n                    // continue processing.\r\n                    // Data is expected to be the read page.\r\n                    appendPage(operation, data);\r\n                    var len = getJsonValueArraryLength(operation.d);\r\n                    // Are we done?\r\n                    if (operation.c === len || data.c < pageSize) {\r\n                        // Update the stats, request for a prefetch operation.\r\n                        stats.cacheReads++;\r\n                        prefetch(data.i + data.c);\r\n                        // Terminate the operation.\r\n                        operation.complete();\r\n                    } else {\r\n                        // Continue processing the operation.\r\n                        transition(READ_STATE_LOCAL, data.i + pageSize);\r\n                    }\r\n                    break;\r\n\r\n                default:\r\n                    return readSaveStateMachine(operation, opTargetState, cacheState, data, false);\r\n            }\r\n        }\r\n\r\n        return true;\r\n    };\r\n\r\n    /** State machine describing the behavior for reading and saving data into the cache.\r\n     * @method DataCache~readSaveStateMachine    \r\n     * @param {DataCacheOperation} operation - Operation being run.\r\n     * @param {Object} opTargetState - Operation state to transition to.\r\n     * @param {Object} cacheState - Current cache state.\r\n     * @param {Object} [data] - \r\n     * @param {Boolean} isPrefetch - Flag indicating whether a read (false) or prefetch (true) operation is running.\r\n     * This state machine contains behavior common to read and prefetch operations.\r\n    */\r\n    var readSaveStateMachine = function (operation, opTargetState, cacheState, data, isPrefetch) {\r\n\r\n        var error = operation.error;\r\n        var transition = operation.transition;\r\n        var wait = operation.wait;\r\n        var request;\r\n\r\n        switch (opTargetState) {\r\n            case OPERATION_STATE_END:\r\n                // State that signals the operation is done.\r\n                fireOnIdle();\r\n                break;\r\n\r\n            case READ_STATE_LOCAL:\r\n                // State that requests for a page from the local store.\r\n                // Data is expected to be the index of the page to request.\r\n                request = readPage(data).then(function (found, page) {\r\n                    // Signal the cache that a read operation is running.\r\n                    if (!operation.canceled) {\r\n                        if (found) {\r\n                            // The page is in the local store, check if the operation can be resolved.\r\n                            transition(READ_STATE_DONE, page);\r\n                        } else {\r\n                            // The page is not in the local store, request it from the source.\r\n                            transition(READ_STATE_SOURCE, data);\r\n                        }\r\n                    }\r\n                });\r\n                break;\r\n\r\n            case READ_STATE_SOURCE:\r\n                // State that requests for a page from the cache source.\r\n                // Data is expected to be the index of the page to request.\r\n                request = fetchPage(data).then(function (page) {\r\n                    // Signal the cache that a read operation is running.\r\n                    if (!operation.canceled) {\r\n                        // Update the stats and save the page to the local store.\r\n                        if (isPrefetch) {\r\n                            stats.prefetches++;\r\n                        } else {\r\n                            stats.netReads++;\r\n                        }\r\n                        transition(READ_STATE_SAVE, page);\r\n                    }\r\n                }, error);\r\n                break;\r\n\r\n            case READ_STATE_SAVE:\r\n                // State that saves a  page to the local store.\r\n                // Data is expected to be the page to save.\r\n                // Write access to the store is exclusive.\r\n                if (cacheState !== CACHE_STATE_WRITE) {\r\n                    changeState(CACHE_STATE_WRITE);\r\n                    request = savePage(data.i, data).then(function (saved) {\r\n                        if (!operation.canceled) {\r\n                            if (!saved && isPrefetch) {\r\n                                operation.pending = 0;\r\n                            }\r\n                            // Check if the operation can be resolved.\r\n                            transition(READ_STATE_DONE, data);\r\n                        }\r\n                        changeState(CACHE_STATE_IDLE);\r\n                    });\r\n                }\r\n                break;\r\n\r\n            default:\r\n                // Unknown state that can't be handled by this state machine.\r\n                return false;\r\n        }\r\n\r\n        if (request) {\r\n            // The operation might have been canceled between stack frames do to the async calls.\r\n            if (operation.canceled) {\r\n                request.cancel();\r\n            } else if (operation.s === opTargetState) {\r\n                // Wait for the request to complete.\r\n                wait(request);\r\n            }\r\n        }\r\n\r\n        return true;\r\n    };\r\n\r\n    // Initialize the cache.\r\n    store.read(\"__settings\", function (_, settings) {\r\n        if (assigned(settings)) {\r\n            var settingsVersion = settings.version;\r\n            if (!settingsVersion || settingsVersion.indexOf(\"1.\") !== 0) {\r\n                cacheFailureCallback(\"Unsupported cache store version \" + settingsVersion)();\r\n                return;\r\n            }\r\n\r\n            if (pageSize !== settings.pageSize || source.identifier !== settings.sourceId) {\r\n                // The shape or the source of the data was changed so invalidate the store.\r\n                clearStore().then(function () {\r\n                    // Signal the cache is fully initialized.\r\n                    changeState(CACHE_STATE_IDLE);\r\n                }, cacheFailureCallback(\"Unable to clear store during initialization\"));\r\n            } else {\r\n                // Restore the saved settings.\r\n                actualCacheSize = settings.actualCacheSize;\r\n                allDataLocal = settings.allDataLocal;\r\n                cacheSize = settings.cacheSize;\r\n                collectionCount = settings.collectionCount;\r\n                highestSavedPage = settings.highestSavedPage;\r\n                highestSavedPageSize = settings.highestSavedPageSize;\r\n                version = settingsVersion;\r\n\r\n                // Signal the cache is fully initialized.\r\n                changeState(CACHE_STATE_IDLE);\r\n            }\r\n        } else {\r\n            // This is a brand new cache.\r\n            saveSettings(function () {\r\n                // Signal the cache is fully initialized.\r\n                changeState(CACHE_STATE_IDLE);\r\n            }, cacheFailureCallback(\"Unable to write settings during initialization.\"));\r\n        }\r\n    }, cacheFailureCallback(\"Unable to read settings from store.\"));\r\n\r\n    return that;\r\n}\r\n\r\n/** Creates a data cache for a collection that is efficiently loaded on-demand.\r\n * @param options \r\n * Options for the data cache, including name, source, pageSize, TODO check doku\r\n * prefetchSize, cacheSize, storage mechanism, and initial prefetch and local-data handler.\r\n * @returns {DataCache} A new data cache instance.\r\n */\r\nfunction createDataCache (options) {\r\n    checkUndefinedGreaterThanZero(options.pageSize, \"pageSize\");\r\n    checkUndefinedOrNumber(options.cacheSize, \"cacheSize\");\r\n    checkUndefinedOrNumber(options.prefetchSize, \"prefetchSize\");\r\n\r\n    if (!assigned(options.name)) {\r\n        throw { message: \"Undefined or null name\", options: options };\r\n    }\r\n\r\n    if (!assigned(options.source)) {\r\n        throw { message: \"Undefined source\", options: options };\r\n    }\r\n\r\n    return new DataCache(options);\r\n}\r\n\r\n\r\n/** estimateSize (see {@link estimateSize}) */\r\nexports.estimateSize = estimateSize;\r\n\r\n/** createDataCache */  \r\nexports.createDataCache = createDataCache;\r\n\r\n\r\n\r\n}, \"source\" : function(exports, module, require) {\r\n'use strict';\r\n\r\n /** @module cache/source */\r\n \r\nvar utils = require(\"./../utils.js\");\r\nvar odataRequest = require(\"./../odata.js\");\r\n\r\nvar parseInt10 = utils.parseInt10;\r\nvar normalizeURICase = utils.normalizeURICase;\r\n\r\n\r\n\r\n\r\n/** Appends the specified escaped query option to the specified URI.\r\n * @param {String} uri - URI to append option to.\r\n * @param {String} queryOption - Escaped query option to append.\r\n */\r\nfunction appendQueryOption(uri, queryOption) {\r\n    var separator = (uri.indexOf(\"?\") >= 0) ? \"&\" : \"?\";\r\n    return uri + separator + queryOption;\r\n}\r\n\r\n/** Appends the specified segment to the given URI.\r\n * @param {String} uri - URI to append a segment to.\r\n * @param {String} segment - Segment to append.\r\n * @returns {String} The original URI with a new segment appended.\r\n */\r\nfunction appendSegment(uri, segment) {\r\n    var index = uri.indexOf(\"?\");\r\n    var queryPortion = \"\";\r\n    if (index >= 0) {\r\n        queryPortion = uri.substr(index);\r\n        uri = uri.substr(0, index);\r\n    }\r\n\r\n    if (uri[uri.length - 1] !== \"/\") {\r\n        uri += \"/\";\r\n    }\r\n    return uri + segment + queryPortion;\r\n}\r\n\r\n/** Builds a request object to GET the specified URI.\r\n * @param {String} uri - URI for request.\r\n * @param {Object} options - Additional options.\r\n */\r\nfunction buildODataRequest(uri, options) {\r\n    return {\r\n        method: \"GET\",\r\n        requestUri: uri,\r\n        user: options.user,\r\n        password: options.password,\r\n        enableJsonpCallback: options.enableJsonpCallback,\r\n        callbackParameterName: options.callbackParameterName,\r\n        formatQueryString: options.formatQueryString\r\n    };\r\n}\r\n\r\n/** Finds the index where the value of a query option starts.\r\n * @param {String} uri - URI to search in.\r\n * @param {String} name - Name to look for.\r\n * @returns {Number} The index where the query option starts.\r\n */\r\nfunction findQueryOptionStart(uri, name) {\r\n    var result = -1;\r\n    var queryIndex = uri.indexOf(\"?\");\r\n    if (queryIndex !== -1) {\r\n        var start = uri.indexOf(\"?\" + name + \"=\", queryIndex);\r\n        if (start === -1) {\r\n            start = uri.indexOf(\"&\" + name + \"=\", queryIndex);\r\n        }\r\n        if (start !== -1) {\r\n            result = start + name.length + 2;\r\n        }\r\n    }\r\n    return result;\r\n}\r\n\r\n/** Gets data from an OData service.\r\n * @param {String} uri - URI to the OData service.\r\n * @param {Object} options - Object with additional well-known request options.\r\n * @param {Function} success - Success callback.\r\n * @param {Function} error - Error callback.\r\n * @returns {Object} Object with an abort method.\r\n */\r\nfunction queryForData (uri, options, success, error) {\r\n    return queryForDataInternal(uri, options, {}, success, error);\r\n}\r\n\r\n/** Gets data from an OData service taking into consideration server side paging.\r\n * @param {String} uri - URI to the OData service.\r\n * @param {Object} options - Object with additional well-known request options.\r\n * @param {Array} data - Array that stores the data provided by the OData service.\r\n * @param {Function} success - Success callback.\r\n * @param {Function} error - Error callback.\r\n * @returns {Object} Object with an abort method.\r\n */\r\nfunction queryForDataInternal(uri, options, data, success, error) {\r\n\r\n    var request = buildODataRequest(uri, options);\r\n    var currentRequest = odataRequest.request(request, function (newData) {\r\n        var nextLink = newData[\"@odata.nextLink\"];\r\n        if (nextLink) {\r\n            var index = uri.indexOf(\".svc/\", 0);\r\n            if (index != -1) {\r\n                nextLink = uri.substring(0, index + 5) + nextLink;\r\n            }\r\n        }\r\n\r\n        if (data.value && newData.value) {\r\n            data.value = data.value.concat(newData.value);\r\n        }\r\n        else {\r\n            for (var property in newData) {\r\n                if (property != \"@odata.nextLink\") {\r\n                    data[property] = newData[property];\r\n                }\r\n            }\r\n        }\r\n\r\n        if (nextLink) {\r\n            currentRequest = queryForDataInternal(nextLink, options, data, success, error);\r\n        }\r\n        else {\r\n            success(data);\r\n        }\r\n    }, error, undefined, options.httpClient, options.metadata);\r\n\r\n    return {\r\n        abort: function () {\r\n            currentRequest.abort();\r\n        }\r\n    };\r\n}\r\n\r\n/** Creates a data cache source object for requesting data from an OData service.\r\n * @class ODataCacheSource\r\n * @param options - Options for the cache data source.\r\n * @returns {ODataCacheSource} A new data cache source instance.\r\n */\r\nfunction ODataCacheSource (options) {\r\n    var that = this;\r\n    var uri = options.source;\r\n    \r\n    that.identifier = normalizeURICase(encodeURI(decodeURI(uri)));\r\n    that.options = options;\r\n\r\n    /** Gets the number of items in the collection.\r\n     * @method ODataCacheSource#count\r\n     * @param {Function} success - Success callback with the item count.\r\n     * @param {Function} error - Error callback.\r\n     * @returns {Object} Request object with an abort method.\r\n     */\r\n    that.count = function (success, error) {\r\n        var options = that.options;\r\n        return odataRequest.request(\r\n            buildODataRequest(appendSegment(uri, \"$count\"), options),\r\n            function (data) {\r\n                var count = parseInt10(data.toString());\r\n                if (isNaN(count)) {\r\n                    error({ message: \"Count is NaN\", count: count });\r\n                } else {\r\n                    success(count);\r\n                }\r\n            }, error, undefined, options.httpClient, options.metadata\r\n        );\r\n    };\r\n    \r\n    /** Gets a number of consecutive items from the collection.\r\n     * @method ODataCacheSource#read\r\n     * @param {Number} index - Zero-based index of the items to retrieve.\r\n     * @param {Number} count - Number of items to retrieve.\r\n     * @param {Function} success - Success callback with the requested items.\r\n     * @param {Function} error - Error callback.\r\n     * @returns {Object} Request object with an abort method.\r\n    */\r\n    that.read = function (index, count, success, error) {\r\n\r\n        var queryOptions = \"$skip=\" + index + \"&$top=\" + count;\r\n        return queryForData(appendQueryOption(uri, queryOptions), that.options, success, error);\r\n    };\r\n\r\n    return that;\r\n}\r\n\r\n\r\n\r\n/** ODataCacheSource (see {@link ODataCacheSource}) */\r\nexports.ODataCacheSource = ODataCacheSource;}, \"deferred\" : function(exports, module, require) {\r\n'use strict';\r\n\r\n/** @module odatajs/deferred */\r\n\r\n\r\n\r\n/** Creates a new function to forward a call.\r\n * @param {Object} thisValue - Value to use as the 'this' object.\r\n * @param {String} name - Name of function to forward to.\r\n * @param {Object} returnValue - Return value for the forward call (helps keep identity when chaining calls).\r\n * @returns {Function} A new function that will forward a call.\r\n */\r\nfunction forwardCall(thisValue, name, returnValue) {\r\n    return function () {\r\n        thisValue[name].apply(thisValue, arguments);\r\n        return returnValue;\r\n    };\r\n}\r\n\r\n/** Initializes a new DjsDeferred object.\r\n * <ul>\r\n * <li> Compability Note A - Ordering of callbacks through chained 'then' invocations <br>\r\n *\r\n * The Wiki entry at http://wiki.commonjs.org/wiki/Promises/A\r\n * implies that .then() returns a distinct object.\r\n *\r\n * For compatibility with http://api.jquery.com/category/deferred-object/\r\n * we return this same object. This affects ordering, as\r\n * the jQuery version will fire callbacks in registration\r\n * order regardless of whether they occur on the result\r\n * or the original object.\r\n * </li>\r\n * <li>Compability Note B - Fulfillment value <br>\r\n *\r\n * The Wiki entry at http://wiki.commonjs.org/wiki/Promises/A\r\n * implies that the result of a success callback is the\r\n * fulfillment value of the object and is received by\r\n * other success callbacks that are chained.\r\n *\r\n * For compatibility with http://api.jquery.com/category/deferred-object/\r\n * we disregard this value instead.\r\n * </li></ul>\r\n * @class DjsDeferred \r\n */\r\n function DjsDeferred() {\r\n    this._arguments = undefined;\r\n    this._done = undefined;\r\n    this._fail = undefined;\r\n    this._resolved = false;\r\n    this._rejected = false;\r\n}\r\n\r\n\r\nDjsDeferred.prototype = {\r\n\r\n    /** Adds success and error callbacks for this deferred object.\r\n     * See Compatibility Note A.\r\n     * @method DjsDeferred#then\r\n     * @param {function} [fulfilledHandler] - Success callback ( may be null)\r\n     * @param {function} [errorHandler] - Error callback ( may be null)\r\n     */\r\n    then: function (fulfilledHandler, errorHandler) {\r\n\r\n        if (fulfilledHandler) {\r\n            if (!this._done) {\r\n                this._done = [fulfilledHandler];\r\n            } else {\r\n                this._done.push(fulfilledHandler);\r\n            }\r\n        }\r\n\r\n        if (errorHandler) {\r\n            if (!this._fail) {\r\n                this._fail = [errorHandler];\r\n            } else {\r\n                this._fail.push(errorHandler);\r\n            }\r\n        }\r\n\r\n        //// See Compatibility Note A in the DjsDeferred constructor.\r\n        //// if (!this._next) {\r\n        ////    this._next = createDeferred();\r\n        //// }\r\n        //// return this._next.promise();\r\n\r\n        if (this._resolved) {\r\n            this.resolve.apply(this, this._arguments);\r\n        } else if (this._rejected) {\r\n            this.reject.apply(this, this._arguments);\r\n        }\r\n\r\n        return this;\r\n    },\r\n\r\n    /** Invokes success callbacks for this deferred object.\r\n     * All arguments are forwarded to success callbacks.\r\n     * @method DjsDeferred#resolve\r\n     */\r\n    resolve: function (/* args */) {\r\n        if (this._done) {\r\n            var i, len;\r\n            for (i = 0, len = this._done.length; i < len; i++) {\r\n                //// See Compability Note B - Fulfillment value.\r\n                //// var nextValue =\r\n                this._done[i].apply(null, arguments);\r\n            }\r\n\r\n            //// See Compatibility Note A in the DjsDeferred constructor.\r\n            //// this._next.resolve(nextValue);\r\n            //// delete this._next;\r\n\r\n            this._done = undefined;\r\n            this._resolved = false;\r\n            this._arguments = undefined;\r\n        } else {\r\n            this._resolved = true;\r\n            this._arguments = arguments;\r\n        }\r\n    },\r\n\r\n    /** Invokes error callbacks for this deferred object.\r\n     * All arguments are forwarded to error callbacks.\r\n     * @method DjsDeferred#reject\r\n     */\r\n    reject: function (/* args */) {\r\n        \r\n        if (this._fail) {\r\n            var i, len;\r\n            for (i = 0, len = this._fail.length; i < len; i++) {\r\n                this._fail[i].apply(null, arguments);\r\n            }\r\n\r\n            this._fail = undefined;\r\n            this._rejected = false;\r\n            this._arguments = undefined;\r\n        } else {\r\n            this._rejected = true;\r\n            this._arguments = arguments;\r\n        }\r\n    },\r\n\r\n    /** Returns a version of this object that has only the read-only methods available.\r\n     * @method DjsDeferred#promise\r\n     * @returns An object with only the promise object.\r\n     */\r\n\r\n    promise: function () {\r\n        var result = {};\r\n        result.then = forwardCall(this, \"then\", result);\r\n        return result;\r\n    }\r\n};\r\n\r\n/** Creates a deferred object.\r\n * @returns {DjsDeferred} A new deferred object. If jQuery is installed, then a jQueryDeferred object is returned, which provides a superset of features.\r\n*/\r\nfunction createDeferred() {\r\n    if (window.jQuery && window.jQuery.Deferred) {\r\n        return new window.jQuery.Deferred();\r\n    } else {\r\n        return new DjsDeferred();\r\n    }\r\n}\r\n\r\n\r\n\r\n\r\n/** createDeferred (see {@link module:datajs/deferred~createDeferred}) */\r\nexports.createDeferred = createDeferred;\r\n\r\n/** DjsDeferred (see {@link DjsDeferred}) */\r\nexports.DjsDeferred = DjsDeferred;}, \"odata\" : function(exports, module, require) {\r\n'use strict';\r\n\r\n /** @module odata */\r\n\r\n// Imports\r\nvar odataUtils    = exports.utils     = require('./odata/odatautils.js');\r\nvar odataHandler  = exports.handler   = require('./odata/handler.js');\r\nvar odataMetadata = exports.metadata  = require('./odata/metadata.js');\r\nvar odataNet      = exports.net       = require('./odata/net.js');\r\nvar odataJson     = exports.json      = require('./odata/json.js');\r\n                    exports.batch     = require('./odata/batch.js');\r\n                    \r\n\r\n\r\nvar utils = require('./utils.js');\r\nvar assigned = utils.assigned;\r\n\r\nvar defined = utils.defined;\r\nvar throwErrorCallback = utils.throwErrorCallback;\r\n\r\nvar invokeRequest = odataUtils.invokeRequest;\r\nvar MAX_DATA_SERVICE_VERSION = odataHandler.MAX_DATA_SERVICE_VERSION;\r\nvar prepareRequest = odataUtils.prepareRequest;\r\nvar metadataParser = odataMetadata.metadataParser;\r\n\r\n// CONTENT START\r\n\r\nvar handlers = [odataJson.jsonHandler, odataHandler.textHandler];\r\n\r\n/** Dispatches an operation to handlers.\r\n * @param {String} handlerMethod - Name of handler method to invoke.\r\n * @param {Object} requestOrResponse - request/response argument for delegated call.\r\n * @param {Object} context - context argument for delegated call.\r\n */\r\nfunction dispatchHandler(handlerMethod, requestOrResponse, context) {\r\n\r\n    var i, len;\r\n    for (i = 0, len = handlers.length; i < len && !handlers[i][handlerMethod](requestOrResponse, context); i++) {\r\n    }\r\n\r\n    if (i === len) {\r\n        throw { message: \"no handler for data\" };\r\n    }\r\n}\r\n\r\n/** Default success handler for OData.\r\n * @param data - Data to process.\r\n */\r\nexports.defaultSuccess = function (data) {\r\n\r\n    window.alert(window.JSON.stringify(data));\r\n};\r\n\r\nexports.defaultError = throwErrorCallback;\r\n\r\nexports.defaultHandler = {\r\n\r\n        /** Reads the body of the specified response by delegating to JSON handlers.\r\n        * @param response - Response object.\r\n        * @param context - Operation context.\r\n        */\r\n        read: function (response, context) {\r\n\r\n            if (response && assigned(response.body) && response.headers[\"Content-Type\"]) {\r\n                dispatchHandler(\"read\", response, context);\r\n            }\r\n        },\r\n\r\n        /** Write the body of the specified request by delegating to JSON handlers.\r\n        * @param request - Reques tobject.\r\n        * @param context - Operation context.\r\n        */\r\n        write: function (request, context) {\r\n\r\n            dispatchHandler(\"write\", request, context);\r\n        },\r\n\r\n        maxDataServiceVersion: MAX_DATA_SERVICE_VERSION,\r\n        accept: \"application/json;q=0.9, */*;q=0.1\"\r\n    };\r\n\r\nexports.defaultMetadata = []; //TODO check why is the defaultMetadata an Array? and not an Object.\r\n\r\n/** Reads data from the specified URL.\r\n * @param urlOrRequest - URL to read data from.\r\n * @param {Function} [success] - \r\n * @param {Function} [error] - \r\n * @param {Object} [handler] - \r\n * @param {Object} [httpClient] - \r\n * @param {Object} [metadata] - \r\n */\r\nexports.read = function (urlOrRequest, success, error, handler, httpClient, metadata) {\r\n\r\n    var request;\r\n    if (urlOrRequest instanceof String || typeof urlOrRequest === \"string\") {\r\n        request = { requestUri: urlOrRequest };\r\n    } else {\r\n        request = urlOrRequest;\r\n    }\r\n\r\n    return exports.request(request, success, error, handler, httpClient, metadata);\r\n};\r\n\r\n/** Sends a request containing OData payload to a server.\r\n * @param {Object} request - Object that represents the request to be sent.\r\n * @param {Function} [success] - \r\n * @param {Function} [error] - \r\n * @param {Object} [handler] - \r\n * @param {Object} [httpClient] - \r\n * @param {Object} [metadata] - \r\n */\r\nexports.request = function (request, success, error, handler, httpClient, metadata) {\r\n\r\n    success = success || exports.defaultSuccess;\r\n    error = error || exports.defaultError;\r\n    handler = handler || exports.defaultHandler;\r\n    httpClient = httpClient || odataNet.defaultHttpClient;\r\n    metadata = metadata || exports.defaultMetadata;\r\n\r\n    // Augment the request with additional defaults.\r\n    request.recognizeDates = utils.defined(request.recognizeDates, odataJson.jsonHandler.recognizeDates);\r\n    request.callbackParameterName = utils.defined(request.callbackParameterName, odataNet.defaultHttpClient.callbackParameterName);\r\n    request.formatQueryString = utils.defined(request.formatQueryString, odataNet.defaultHttpClient.formatQueryString);\r\n    request.enableJsonpCallback = utils.defined(request.enableJsonpCallback, odataNet.defaultHttpClient.enableJsonpCallback);\r\n\r\n    // Create the base context for read/write operations, also specifying complete settings.\r\n    var context = {\r\n        metadata: metadata,\r\n        recognizeDates: request.recognizeDates,\r\n        callbackParameterName: request.callbackParameterName,\r\n        formatQueryString: request.formatQueryString,\r\n        enableJsonpCallback: request.enableJsonpCallback\r\n    };\r\n\r\n    try {\r\n        odataUtils.prepareRequest(request, handler, context);\r\n        return odataUtils.invokeRequest(request, success, error, handler, httpClient, context);\r\n    } catch (err) {\r\n        // errors in success handler for sync requests are catched here and result in error handler calls. \r\n        // So here we fix this and throw that error further.\r\n        if (err.bIsSuccessHandlerError) {\r\n            throw err;\r\n        } else {\r\n            error(err);\r\n        }\r\n    }\r\n\r\n};\r\n\r\n/** Parses the csdl metadata to ODataJS metatdata format. This method can be used when the metadata is retrieved using something other than odatajs\r\n * @param {string} csdlMetadataDocument - A string that represents the entire csdl metadata.\r\n * @returns {Object} An object that has the representation of the metadata in odatajs format.\r\n */\r\nexports.parseMetadata = function (csdlMetadataDocument) {\r\n\r\n    return metadataParser(null, csdlMetadataDocument);\r\n};\r\n\r\n// Configure the batch handler to use the default handler for the batch parts.\r\nexports.batch.batchHandler.partHandler = exports.defaultHandler;\r\nexports.metadataHandler =  odataMetadata.metadataHandler;\r\nexports.jsonHandler =  odataJson.jsonHandler;\r\n}, \"batch\" : function(exports, module, require) {\r\n'use strict';\r\n\r\n/** @module odata/batch */\r\n\r\nvar utils    = require('./../utils.js');\r\nvar odataUtils    = require('./odatautils.js');\r\nvar odataHandler = require('./handler.js');\r\n\r\nvar extend = utils.extend;\r\nvar isArray = utils.isArray;\r\nvar trimString = utils.trimString;\r\n\r\nvar contentType = odataHandler.contentType;\r\nvar handler = odataHandler.handler;\r\nvar isBatch = odataUtils.isBatch;\r\nvar MAX_DATA_SERVICE_VERSION = odataHandler.MAX_DATA_SERVICE_VERSION;\r\nvar normalizeHeaders = odataUtils.normalizeHeaders;\r\n//TODO var payloadTypeOf = odata.payloadTypeOf;\r\nvar prepareRequest = odataUtils.prepareRequest;\r\n\r\n\r\n// Imports\r\n\r\n// CONTENT START\r\nvar batchMediaType = \"multipart/mixed\";\r\nvar responseStatusRegex = /^HTTP\\/1\\.\\d (\\d{3}) (.*)$/i;\r\nvar responseHeaderRegex = /^([^()<>@,;:\\\\\"\\/[\\]?={} \\t]+)\\s?:\\s?(.*)/;\r\n\r\n/** Calculates a random 16 bit number and returns it in hexadecimal format.\r\n * @returns {String} A 16-bit number in hex format.\r\n */\r\nfunction hex16() {\r\n\r\n    return Math.floor((1 + Math.random()) * 0x10000).toString(16).substr(1);\r\n}\r\n\r\n/** Creates a string that can be used as a multipart request boundary.\r\n * @param {String} [prefix] - \r\n * @returns {String} Boundary string of the format: <prefix><hex16>-<hex16>-<hex16>\r\n */\r\nfunction createBoundary(prefix) {\r\n\r\n    return prefix + hex16() + \"-\" + hex16() + \"-\" + hex16();\r\n}\r\n\r\n/** Gets the handler for data serialization of individual requests / responses in a batch.\r\n * @param context - Context used for data serialization.\r\n * @returns Handler object\r\n */\r\nfunction partHandler(context) {\r\n\r\n    return context.handler.partHandler;\r\n}\r\n\r\n/** Gets the current boundary used for parsing the body of a multipart response.\r\n * @param context - Context used for parsing a multipart response.\r\n * @returns {String} Boundary string.\r\n */\r\nfunction currentBoundary(context) {\r\n    var boundaries = context.boundaries;\r\n    return boundaries[boundaries.length - 1];\r\n}\r\n\r\n/** Parses a batch response.\r\n * @param handler - This handler.\r\n * @param {String} text - Batch text.\r\n * @param {Object} context - Object with parsing context.\r\n * @return An object representation of the batch.\r\n */\r\nfunction batchParser(handler, text, context) {\r\n\r\n    var boundary = context.contentType.properties[\"boundary\"];\r\n    return { __batchResponses: readBatch(text, { boundaries: [boundary], handlerContext: context }) };\r\n}\r\n\r\n/** Serializes a batch object representation into text.\r\n * @param handler - This handler.\r\n * @param {Object} data - Representation of a batch.\r\n * @param {Object} context - Object with parsing context.\r\n * @return An text representation of the batch object; undefined if not applicable.#\r\n */\r\nfunction batchSerializer(handler, data, context) {\r\n\r\n    var cType = context.contentType = context.contentType || contentType(batchMediaType);\r\n    if (cType.mediaType === batchMediaType) {\r\n        return writeBatch(data, context);\r\n    }\r\n}\r\n\r\n/** Parses a multipart/mixed response body from from the position defined by the context.\r\n * @param {String}  text - Body of the multipart/mixed response.\r\n * @param context - Context used for parsing.\r\n * @return Array of objects representing the individual responses.\r\n */\r\nfunction readBatch(text, context) {\r\n    var delimiter = \"--\" + currentBoundary(context);\r\n\r\n    // Move beyond the delimiter and read the complete batch\r\n    readTo(text, context, delimiter);\r\n\r\n    // Ignore the incoming line\r\n    readLine(text, context);\r\n\r\n    // Read the batch parts\r\n    var responses = [];\r\n    var partEnd = null;\r\n\r\n    while (partEnd !== \"--\" && context.position < text.length) {\r\n        var partHeaders = readHeaders(text, context);\r\n        var partContentType = contentType(partHeaders[\"Content-Type\"]);\r\n\r\n        var changeResponses;\r\n        if (partContentType && partContentType.mediaType === batchMediaType) {\r\n            context.boundaries.push(partContentType.properties.boundary);\r\n            try {\r\n                changeResponses = readBatch(text, context);\r\n            } catch (e) {\r\n                e.response = readResponse(text, context, delimiter);\r\n                changeResponses = [e];\r\n            }\r\n            responses.push({ __changeResponses: changeResponses });\r\n            context.boundaries.pop();\r\n            readTo(text, context, \"--\" + currentBoundary(context));\r\n        } else {\r\n            if (!partContentType || partContentType.mediaType !== \"application/http\") {\r\n                throw { message: \"invalid MIME part type \" };\r\n            }\r\n            // Skip empty line\r\n            readLine(text, context);\r\n            // Read the response\r\n            var response = readResponse(text, context, delimiter);\r\n            try {\r\n                if (response.statusCode >= 200 && response.statusCode <= 299) {\r\n                    partHandler(context.handlerContext).read(response, context.handlerContext);\r\n                } else {\r\n                    // Keep track of failed responses and continue processing the batch.\r\n                    response = { message: \"HTTP request failed\", response: response };\r\n                }\r\n            } catch (e) {\r\n                response = e;\r\n            }\r\n\r\n            responses.push(response);\r\n        }\r\n\r\n        partEnd = text.substr(context.position, 2);\r\n\r\n        // Ignore the incoming line.\r\n        readLine(text, context);\r\n    }\r\n    return responses;\r\n}\r\n\r\n/** Parses the http headers in the text from the position defined by the context.\r\n * @param {String} text - Text containing an http response's headers\r\n * @param context - Context used for parsing.\r\n * @returns Object containing the headers as key value pairs.\r\n * This function doesn't support split headers and it will stop reading when it hits two consecutive line breaks.\r\n*/\r\nfunction readHeaders(text, context) {\r\n    var headers = {};\r\n    var parts;\r\n    var line;\r\n    var pos;\r\n\r\n    do {\r\n        pos = context.position;\r\n        line = readLine(text, context);\r\n        parts = responseHeaderRegex.exec(line);\r\n        if (parts !== null) {\r\n            headers[parts[1]] = parts[2];\r\n        } else {\r\n            // Whatever was found is not a header, so reset the context position.\r\n            context.position = pos;\r\n        }\r\n    } while (line && parts);\r\n\r\n    normalizeHeaders(headers);\r\n\r\n    return headers;\r\n}\r\n\r\n/** Parses an HTTP response.\r\n * @param {String} text -Text representing the http response.\r\n * @param context optional - Context used for parsing.\r\n * @param {String} delimiter -String used as delimiter of the multipart response parts.\r\n * @return Object representing the http response.\r\n */\r\nfunction readResponse(text, context, delimiter) {\r\n    // Read the status line.\r\n    var pos = context.position;\r\n    var match = responseStatusRegex.exec(readLine(text, context));\r\n\r\n    var statusCode;\r\n    var statusText;\r\n    var headers;\r\n\r\n    if (match) {\r\n        statusCode = match[1];\r\n        statusText = match[2];\r\n        headers = readHeaders(text, context);\r\n        readLine(text, context);\r\n    } else {\r\n        context.position = pos;\r\n    }\r\n\r\n    return {\r\n        statusCode: statusCode,\r\n        statusText: statusText,\r\n        headers: headers,\r\n        body: readTo(text, context, \"\\r\\n\" + delimiter)\r\n    };\r\n}\r\n\r\n/** Returns a substring from the position defined by the context up to the next line break (CRLF).\r\n * @param {String} text - Input string.\r\n * @param context - Context used for reading the input string.\r\n * @returns {String} Substring to the first ocurrence of a line break or null if none can be found. \r\n */\r\nfunction readLine(text, context) {\r\n\r\n    return readTo(text, context, \"\\r\\n\");\r\n}\r\n\r\n/** Returns a substring from the position given by the context up to value defined by the str parameter and increments the position in the context.\r\n * @param {String} text - Input string.\r\n * @param context - Context used for reading the input string.\r\n * @param {String} [str] - Substring to read up to.\r\n * @returns {String} Substring to the first ocurrence of str or the end of the input string if str is not specified. Null if the marker is not found.\r\n */\r\nfunction readTo(text, context, str) {\r\n    var start = context.position || 0;\r\n    var end = text.length;\r\n    if (str) {\r\n        end = text.indexOf(str, start);\r\n        if (end === -1) {\r\n            return null;\r\n        }\r\n        context.position = end + str.length;\r\n    } else {\r\n        context.position = end;\r\n    }\r\n\r\n    return text.substring(start, end);\r\n}\r\n\r\n/** Serializes a batch request object to a string.\r\n * @param data - Batch request object in payload representation format\r\n * @param context - Context used for the serialization\r\n * @returns {String} String representing the batch request\r\n */\r\nfunction writeBatch(data, context) {\r\n    if (!isBatch(data)) {\r\n        throw { message: \"Data is not a batch object.\" };\r\n    }\r\n\r\n    var batchBoundary = createBoundary(\"batch_\");\r\n    var batchParts = data.__batchRequests;\r\n    var batch = \"\";\r\n    var i, len;\r\n    for (i = 0, len = batchParts.length; i < len; i++) {\r\n        batch += writeBatchPartDelimiter(batchBoundary, false) +\r\n                 writeBatchPart(batchParts[i], context);\r\n    }\r\n    batch += writeBatchPartDelimiter(batchBoundary, true);\r\n\r\n    // Register the boundary with the request content type.\r\n    var contentTypeProperties = context.contentType.properties;\r\n    contentTypeProperties.boundary = batchBoundary;\r\n\r\n    return batch;\r\n}\r\n\r\n/** Creates the delimiter that indicates that start or end of an individual request.\r\n * @param {String} boundary Boundary string used to indicate the start of the request\r\n * @param {Boolean} close - Flag indicating that a close delimiter string should be generated\r\n * @returns {String} Delimiter string\r\n */\r\nfunction writeBatchPartDelimiter(boundary, close) {\r\n    var result = \"\\r\\n--\" + boundary;\r\n    if (close) {\r\n        result += \"--\";\r\n    }\r\n\r\n    return result + \"\\r\\n\";\r\n}\r\n\r\n/** Serializes a part of a batch request to a string. A part can be either a GET request or\r\n * a change set grouping several CUD (create, update, delete) requests.\r\n * @param part - Request or change set object in payload representation format\r\n * @param context - Object containing context information used for the serialization\r\n * @param {boolean} [nested] - \r\n * @returns {String} String representing the serialized part\r\n * A change set is an array of request objects and they cannot be nested inside other change sets.\r\n */\r\nfunction writeBatchPart(part, context, nested) {\r\n    \r\n\r\n    var changeSet = part.__changeRequests;\r\n    var result;\r\n    if (isArray(changeSet)) {\r\n        if (nested) {\r\n            throw { message: \"Not Supported: change set nested in other change set\" };\r\n        }\r\n\r\n        var changeSetBoundary = createBoundary(\"changeset_\");\r\n        result = \"Content-Type: \" + batchMediaType + \"; boundary=\" + changeSetBoundary + \"\\r\\n\";\r\n        var i, len;\r\n        for (i = 0, len = changeSet.length; i < len; i++) {\r\n            result += writeBatchPartDelimiter(changeSetBoundary, false) +\r\n                 writeBatchPart(changeSet[i], context, true);\r\n        }\r\n\r\n        result += writeBatchPartDelimiter(changeSetBoundary, true);\r\n    } else {\r\n        result = \"Content-Type: application/http\\r\\nContent-Transfer-Encoding: binary\\r\\n\\r\\n\";\r\n        var partContext = extend({}, context);\r\n        partContext.handler = handler;\r\n        partContext.request = part;\r\n        partContext.contentType = null;\r\n\r\n        prepareRequest(part, partHandler(context), partContext);\r\n        result += writeRequest(part);\r\n    }\r\n\r\n    return result;\r\n}\r\n\r\n/** Serializes a request object to a string.\r\n * @param request - Request object to serialize\r\n * @returns {String} String representing the serialized request\r\n */\r\nfunction writeRequest(request) {\r\n    var result = (request.method ? request.method : \"GET\") + \" \" + request.requestUri + \" HTTP/1.1\\r\\n\";\r\n    for (var name in request.headers) {\r\n        if (request.headers[name]) {\r\n            result = result + name + \": \" + request.headers[name] + \"\\r\\n\";\r\n        }\r\n    }\r\n\r\n    result += \"\\r\\n\";\r\n\r\n    if (request.body) {\r\n        result += request.body;\r\n    }\r\n\r\n    return result;\r\n}\r\n\r\n\r\n\r\n/** batchHandler (see {@link module:odata/batch~batchParser}) */\r\nexports.batchHandler = handler(batchParser, batchSerializer, batchMediaType, MAX_DATA_SERVICE_VERSION);\r\n\r\n/** batchSerializer (see {@link module:odata/batch~batchSerializer}) */\r\nexports.batchSerializer = batchSerializer;\r\n\r\n/** writeRequest (see {@link module:odata/batch~writeRequest}) */\r\nexports.writeRequest = writeRequest;}, \"handler\" : function(exports, module, require) {\r\n'use strict';\r\n\r\n/** @module odata/handler */\r\n\r\n\r\nvar utils    = require('./../utils.js');\r\nvar oDataUtils    = require('./odatautils.js');\r\n\r\n// Imports.\r\nvar assigned = utils.assigned;\r\nvar extend = utils.extend;\r\nvar trimString = utils.trimString;\r\nvar maxVersion = oDataUtils.maxVersion;\r\nvar MAX_DATA_SERVICE_VERSION = \"4.0\";\r\n\r\n/** Parses a string into an object with media type and properties.\r\n * @param {String} str - String with media type to parse.\r\n * @return null if the string is empty; an object with 'mediaType' and a 'properties' dictionary otherwise.\r\n */\r\nfunction contentType(str) {\r\n\r\n    if (!str) {\r\n        return null;\r\n    }\r\n\r\n    var contentTypeParts = str.split(\";\");\r\n    var properties = {};\r\n\r\n    var i, len;\r\n    for (i = 1, len = contentTypeParts.length; i < len; i++) {\r\n        var contentTypeParams = contentTypeParts[i].split(\"=\");\r\n        properties[trimString(contentTypeParams[0])] = contentTypeParams[1];\r\n    }\r\n\r\n    return { mediaType: trimString(contentTypeParts[0]), properties: properties };\r\n}\r\n\r\n/** Serializes an object with media type and properties dictionary into a string.\r\n * @param contentType - Object with media type and properties dictionary to serialize.\r\n * @return String representation of the media type object; undefined if contentType is null or undefined.\r\n */\r\nfunction contentTypeToString(contentType) {\r\n    if (!contentType) {\r\n        return undefined;\r\n    }\r\n\r\n    var result = contentType.mediaType;\r\n    var property;\r\n    for (property in contentType.properties) {\r\n        result += \";\" + property + \"=\" + contentType.properties[property];\r\n    }\r\n    return result;\r\n}\r\n\r\n/** Creates an object that is going to be used as the context for the handler's parser and serializer.\r\n * @param contentType - Object with media type and properties dictionary.\r\n * @param {String} dataServiceVersion - String indicating the version of the protocol to use.\r\n * @param context - Operation context.\r\n * @param handler - Handler object that is processing a resquest or response.\r\n * @return Context object.\r\n */\r\nfunction createReadWriteContext(contentType, dataServiceVersion, context, handler) {\r\n\r\n    var rwContext = {};\r\n    extend(rwContext, context);\r\n    extend(rwContext, {\r\n        contentType: contentType,\r\n        dataServiceVersion: dataServiceVersion,\r\n        handler: handler\r\n    });\r\n\r\n    return rwContext;\r\n}\r\n\r\n/** Sets a request header's value. If the header has already a value other than undefined, null or empty string, then this method does nothing.\r\n * @param request - Request object on which the header will be set.\r\n * @param {String} name - Header name.\r\n * @param {String} value - Header value.\r\n */\r\nfunction fixRequestHeader(request, name, value) {\r\n    if (!request) {\r\n        return;\r\n    }\r\n\r\n    var headers = request.headers;\r\n    if (!headers[name]) {\r\n        headers[name] = value;\r\n    }\r\n}\r\n\r\n/** Sets the DataServiceVersion header of the request if its value is not yet defined or of a lower version.\r\n * @param request - Request object on which the header will be set.\r\n * @param {String} version - Version value.\r\n *  If the request has already a version value higher than the one supplied the this function does nothing.\r\n */\r\nfunction fixDataServiceVersionHeader(request, version) {   \r\n\r\n    if (request) {\r\n        var headers = request.headers;\r\n        var dsv = headers[\"OData-Version\"];\r\n        headers[\"OData-Version\"] = dsv ? maxVersion(dsv, version) : version;\r\n    }\r\n}\r\n\r\n/** Gets the value of a request or response header.\r\n * @param requestOrResponse - Object representing a request or a response.\r\n * @param {String} name - Name of the header to retrieve.\r\n * @returns {String} String value of the header; undefined if the header cannot be found.\r\n */\r\nfunction getRequestOrResponseHeader(requestOrResponse, name) {\r\n\r\n    var headers = requestOrResponse.headers;\r\n    return (headers && headers[name]) || undefined;\r\n}\r\n\r\n/** Gets the value of the Content-Type header from a request or response.\r\n * @param requestOrResponse - Object representing a request or a response.\r\n * @returns {Object} Object with 'mediaType' and a 'properties' dictionary; null in case that the header is not found or doesn't have a value.\r\n */\r\nfunction getContentType(requestOrResponse) {\r\n\r\n    return contentType(getRequestOrResponseHeader(requestOrResponse, \"Content-Type\"));\r\n}\r\n\r\nvar versionRE = /^\\s?(\\d+\\.\\d+);?.*$/;\r\n/** Gets the value of the DataServiceVersion header from a request or response.\r\n * @param requestOrResponse - Object representing a request or a response.\r\n * @returns {String} Data service version; undefined if the header cannot be found.\r\n */\r\nfunction getDataServiceVersion(requestOrResponse) {\r\n\r\n    var value = getRequestOrResponseHeader(requestOrResponse, \"OData-Version\");\r\n    if (value) {\r\n        var matches = versionRE.exec(value);\r\n        if (matches && matches.length) {\r\n            return matches[1];\r\n        }\r\n    }\r\n\r\n    // Fall through and return undefined.\r\n}\r\n\r\n/** Checks that a handler can process a particular mime type.\r\n * @param handler - Handler object that is processing a resquest or response.\r\n * @param cType - Object with 'mediaType' and a 'properties' dictionary.\r\n * @returns {Boolean} True if the handler can process the mime type; false otherwise.\r\n *\r\n * The following check isn't as strict because if cType.mediaType = application/; it will match an accept value of \"application/xml\";\r\n * however in practice we don't not expect to see such \"suffixed\" mimeTypes for the handlers.\r\n */\r\nfunction handlerAccepts(handler, cType) {\r\n    return handler.accept.indexOf(cType.mediaType) >= 0;\r\n}\r\n\r\n/** Invokes the parser associated with a handler for reading the payload of a HTTP response.\r\n * @param handler - Handler object that is processing the response.\r\n * @param {Function} parseCallback - Parser function that will process the response payload.\r\n * @param response - HTTP response whose payload is going to be processed.\r\n * @param context - Object used as the context for processing the response.\r\n * @returns {Boolean} True if the handler processed the response payload and the response.data property was set; false otherwise.\r\n */\r\nfunction handlerRead(handler, parseCallback, response, context) {\r\n\r\n    if (!response || !response.headers) {\r\n        return false;\r\n    }\r\n\r\n    var cType = getContentType(response);\r\n    var version = getDataServiceVersion(response) || \"\";\r\n    var body = response.body;\r\n\r\n    if (!assigned(body)) {\r\n        return false;\r\n    }\r\n\r\n    if (handlerAccepts(handler, cType)) {\r\n        var readContext = createReadWriteContext(cType, version, context, handler);\r\n        readContext.response = response;\r\n        response.data = parseCallback(handler, body, readContext);\r\n        return response.data !== undefined;\r\n    }\r\n\r\n    return false;\r\n}\r\n\r\n/** Invokes the serializer associated with a handler for generating the payload of a HTTP request.\r\n * @param handler - Handler object that is processing the request.\r\n * @param {Function} serializeCallback - Serializer function that will generate the request payload.\r\n * @param request - HTTP request whose payload is going to be generated.\r\n * @param context - Object used as the context for serializing the request.\r\n * @returns {Boolean} True if the handler serialized the request payload and the request.body property was set; false otherwise.\r\n */\r\nfunction handlerWrite(handler, serializeCallback, request, context) {\r\n    if (!request || !request.headers) {\r\n        return false;\r\n    }\r\n\r\n    var cType = getContentType(request);\r\n    var version = getDataServiceVersion(request);\r\n\r\n    if (!cType || handlerAccepts(handler, cType)) {\r\n        var writeContext = createReadWriteContext(cType, version, context, handler);\r\n        writeContext.request = request;\r\n\r\n        request.body = serializeCallback(handler, request.data, writeContext);\r\n\r\n        if (request.body !== undefined) {\r\n            fixDataServiceVersionHeader(request, writeContext.dataServiceVersion || \"4.0\");\r\n\r\n            fixRequestHeader(request, \"Content-Type\", contentTypeToString(writeContext.contentType));\r\n            fixRequestHeader(request, \"OData-MaxVersion\", handler.maxDataServiceVersion);\r\n            return true;\r\n        }\r\n    }\r\n\r\n    return false;\r\n}\r\n\r\n/** Creates a handler object for processing HTTP requests and responses.\r\n * @param {Function} parseCallback - Parser function that will process the response payload.\r\n * @param {Function} serializeCallback - Serializer function that will generate the request payload.\r\n * @param {String} accept - String containing a comma separated list of the mime types that this handler can work with.\r\n * @param {String} maxDataServiceVersion - String indicating the highest version of the protocol that this handler can work with.\r\n * @returns {Object} Handler object.\r\n */\r\nfunction handler(parseCallback, serializeCallback, accept, maxDataServiceVersion) {\r\n\r\n    return {\r\n        accept: accept,\r\n        maxDataServiceVersion: maxDataServiceVersion,\r\n\r\n        read: function (response, context) {\r\n            return handlerRead(this, parseCallback, response, context);\r\n        },\r\n\r\n        write: function (request, context) {\r\n            return handlerWrite(this, serializeCallback, request, context);\r\n        }\r\n    };\r\n}\r\n\r\nfunction textParse(handler, body /*, context */) {\r\n    return body;\r\n}\r\n\r\nfunction textSerialize(handler, data /*, context */) {\r\n    if (assigned(data)) {\r\n        return data.toString();\r\n    } else {\r\n        return undefined;\r\n    }\r\n}\r\n\r\n\r\n\r\n\r\nexports.textHandler = handler(textParse, textSerialize, \"text/plain\", MAX_DATA_SERVICE_VERSION);\r\nexports.contentType = contentType;\r\nexports.contentTypeToString = contentTypeToString;\r\nexports.handler = handler;\r\nexports.createReadWriteContext = createReadWriteContext;\r\nexports.fixRequestHeader = fixRequestHeader;\r\nexports.getRequestOrResponseHeader = getRequestOrResponseHeader;\r\nexports.getContentType = getContentType;\r\nexports.getDataServiceVersion = getDataServiceVersion;\r\nexports.MAX_DATA_SERVICE_VERSION = MAX_DATA_SERVICE_VERSION;}, \"json\" : function(exports, module, require) {\r\n\r\n/** @module odata/json */\r\n\r\n\r\n\r\nvar utils        = require('./../utils.js');\r\nvar oDataUtils   = require('./odatautils.js');\r\nvar oDataHandler = require('./handler.js');\r\n\r\nvar odataNs = \"odata\";\r\nvar odataAnnotationPrefix = odataNs + \".\";\r\nvar contextUrlAnnotation = \"@\" + odataAnnotationPrefix + \"context\";\r\n\r\nvar assigned = utils.assigned;\r\nvar defined = utils.defined;\r\nvar isArray = utils.isArray;\r\n//var isDate = utils.isDate;\r\nvar isObject = utils.isObject;\r\n//var normalizeURI = utils.normalizeURI;\r\nvar parseInt10 = utils.parseInt10;\r\nvar getFormatKind = utils.getFormatKind;\r\nvar convertByteArrayToHexString = utils.convertByteArrayToHexString;\r\n\r\n\r\nvar formatDateTimeOffset = oDataUtils.formatDateTimeOffset;\r\nvar formatDuration = oDataUtils.formatDuration;\r\nvar formatNumberWidth = oDataUtils.formatNumberWidth;\r\nvar getCanonicalTimezone = oDataUtils.getCanonicalTimezone;\r\nvar handler = oDataUtils.handler;\r\nvar isComplex = oDataUtils.isComplex;\r\nvar isPrimitive = oDataUtils.isPrimitive;\r\nvar isCollectionType = oDataUtils.isCollectionType;\r\nvar lookupComplexType = oDataUtils.lookupComplexType;\r\nvar lookupEntityType = oDataUtils.lookupEntityType;\r\nvar lookupSingleton = oDataUtils.lookupSingleton;\r\nvar lookupEntitySet = oDataUtils.lookupEntitySet;\r\nvar lookupDefaultEntityContainer = oDataUtils.lookupDefaultEntityContainer;\r\nvar lookupProperty = oDataUtils.lookupProperty;\r\nvar MAX_DATA_SERVICE_VERSION = oDataUtils.MAX_DATA_SERVICE_VERSION;\r\nvar maxVersion = oDataUtils.maxVersion;\r\n\r\nvar isPrimitiveEdmType = oDataUtils.isPrimitiveEdmType;\r\nvar isGeographyEdmType = oDataUtils.isGeographyEdmType;\r\nvar isGeometryEdmType = oDataUtils.isGeometryEdmType;\r\n\r\nvar PAYLOADTYPE_FEED = \"f\";\r\nvar PAYLOADTYPE_ENTRY = \"e\";\r\nvar PAYLOADTYPE_PROPERTY = \"p\";\r\nvar PAYLOADTYPE_COLLECTION = \"c\";\r\nvar PAYLOADTYPE_ENUMERATION_PROPERTY = \"enum\";\r\nvar PAYLOADTYPE_SVCDOC = \"s\";\r\nvar PAYLOADTYPE_ENTITY_REF_LINK = \"erl\";\r\nvar PAYLOADTYPE_ENTITY_REF_LINKS = \"erls\";\r\n\r\nvar PAYLOADTYPE_VALUE = \"v\";\r\n\r\nvar PAYLOADTYPE_DELTA = \"d\";\r\nvar DELTATYPE_FEED = \"f\";\r\nvar DELTATYPE_DELETED_ENTRY = \"de\";\r\nvar DELTATYPE_LINK = \"l\";\r\nvar DELTATYPE_DELETED_LINK = \"dl\";\r\n\r\nvar jsonMediaType = \"application/json\";\r\nvar jsonContentType = oDataHandler.contentType(jsonMediaType);\r\n\r\nvar jsonSerializableMetadata = [\"@odata.id\", \"@odata.type\"];\r\n\r\n\r\n\r\n\r\n\r\n/** Extend JSON OData payload with metadata\r\n * @param handler - This handler.\r\n * @param text - Payload text (this parser also handles pre-parsed objects).\r\n * @param {Object} context - Object with parsing context.\r\n * @return An object representation of the OData payload.\r\n */\r\nfunction jsonParser(handler, text, context) {\r\n    var recognizeDates = defined(context.recognizeDates, handler.recognizeDates);\r\n    var model = context.metadata;\r\n    var json = (typeof text === \"string\") ? JSON.parse(text) : text;\r\n    var metadataContentType;\r\n    if (assigned(context.contentType) && assigned(context.contentType.properties)) {\r\n        metadataContentType = context.contentType.properties[\"odata.metadata\"]; //TODO convert to lower before comparism\r\n    }\r\n\r\n    var payloadFormat = getFormatKind(metadataContentType, 1); // none: 0, minimal: 1, full: 2\r\n\r\n    // No errors should be throw out if we could not parse the json payload, instead we should just return the original json object.\r\n    if (payloadFormat === 0) {\r\n        return json;\r\n    }\r\n    else if (payloadFormat === 1) {\r\n        return addMinimalMetadataToJsonPayload(json, model, recognizeDates);\r\n    }\r\n    else if (payloadFormat === 2) {\r\n        // to do: using the EDM Model to get the type of each property instead of just guessing.\r\n        return addFullMetadataToJsonPayload(json, model, recognizeDates);\r\n    }\r\n    else {\r\n        return json;\r\n    }\r\n}\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n// The regular expression corresponds to something like this:\r\n// /Date(123+60)/\r\n//\r\n// This first number is date ticks, the + may be a - and is optional,\r\n// with the second number indicating a timezone offset in minutes.\r\n//\r\n// On the wire, the leading and trailing forward slashes are\r\n// escaped without being required to so the chance of collisions is reduced;\r\n// however, by the time we see the objects, the characters already\r\n// look like regular forward slashes.\r\nvar jsonDateRE = /^\\/Date\\((-?\\d+)(\\+|-)?(\\d+)?\\)\\/$/;\r\n\r\n\r\n// Some JSON implementations cannot produce the character sequence \\/\r\n// which is needed to format DateTime and DateTimeOffset into the\r\n// JSON string representation defined by the OData protocol.\r\n// See the history of this file for a candidate implementation of\r\n// a 'formatJsonDateString' function.\r\n\r\n\r\nvar jsonReplacer = function (_, value) {\r\n    /// <summary>JSON replacer function for converting a value to its JSON representation.</summary>\r\n    /// <param value type=\"Object\">Value to convert.</param>\r\n    /// <returns type=\"String\">JSON representation of the input value.</returns>\r\n    /// <remarks>\r\n    ///   This method is used during JSON serialization and invoked only by the JSON.stringify function.\r\n    ///   It should never be called directly.\r\n    /// </remarks>\r\n\r\n    if (value && value.__edmType === \"Edm.Time\") {\r\n        return formatDuration(value);\r\n    } else {\r\n        return value;\r\n    }\r\n};\r\n\r\n/** Serializes a ODataJs payload structure to the wire format which can be send to the server\r\n * @param handler - This handler.\r\n * @param data - Data to serialize.\r\n * @param {Object} context - Object with serialization context.\r\n * @returns {String} The string representation of data.\r\n */\r\nfunction jsonSerializer(handler, data, context) {\r\n\r\n    var dataServiceVersion = context.dataServiceVersion || \"4.0\";\r\n    var cType = context.contentType = context.contentType || jsonContentType;\r\n\r\n    if (cType && cType.mediaType === jsonContentType.mediaType) {\r\n        context.dataServiceVersion = maxVersion(dataServiceVersion, \"4.0\");\r\n        var newdata = formatJsonRequestPayload(data);\r\n        if (newdata) {\r\n            return JSON.stringify(newdata,jsonReplacer);\r\n        }\r\n    }\r\n    return undefined;\r\n}\r\n\r\n\r\n\r\n\r\n/** Convert OData objects for serialisation in to a new data structure\r\n * @param data - Data to serialize.\r\n * @returns {String} The string representation of data.\r\n */\r\nfunction formatJsonRequestPayload(data) {\r\n    if (!data) {\r\n        return data;\r\n    }\r\n\r\n    if (isPrimitive(data)) {\r\n        return data;\r\n    }\r\n\r\n    if (isArray(data)) {\r\n        var newArrayData = [];\r\n        var i, len;\r\n        for (i = 0, len = data.length; i < len; i++) {\r\n            newArrayData[i] = formatJsonRequestPayload(data[i]);\r\n        }\r\n\r\n        return newArrayData;\r\n    }\r\n\r\n    var newdata = {};\r\n    for (var property in data) {\r\n        if (isJsonSerializableProperty(property)) {\r\n            newdata[property] = formatJsonRequestPayload(data[property]);\r\n        }\r\n    }\r\n\r\n    return newdata;\r\n}\r\n\r\n/** Determine form the attribute name if the attribute is a serializable property\r\n * @param attribute\r\n * @returns {boolean}\r\n */\r\nfunction isJsonSerializableProperty(attribute) {\r\n    if (!attribute) {\r\n        return false;\r\n    }\r\n\r\n    if (attribute.indexOf(\"@odata.\") == -1) {\r\n        return true;\r\n    }\r\n\r\n    var i, len;\r\n    for (i = 0, len = jsonSerializableMetadata.length; i < len; i++) {\r\n        var name = jsonSerializableMetadata[i];\r\n        if (attribute.indexOf(name) != -1) {\r\n            return true;\r\n        }\r\n    }\r\n\r\n    return false;\r\n}\r\n\r\n/** Creates an object containing information for the json payload.\r\n * @param {String} kind - JSON payload kind\r\n * @param {String} type - Type name of the JSON payload.\r\n * @returns {Object} Object with kind and type fields.\r\n */\r\nfunction jsonMakePayloadInfo(kind, type) {\r\n    return { kind: kind, type: type || null };\r\n}\r\n\r\n\r\n\r\n/** Add metadata to an JSON payload complex object containing full metadata\r\n * @param {Object} data - Data structure to be extended\r\n * @param {Object} model - Metadata model\r\n * @param {Boolean} recognizeDates - Flag indicating whether datetime literal strings should be converted to JavaScript Date objects.\r\n */\r\nfunction addFullMetadataToJsonPayload(data, model, recognizeDates) {\r\n    var type;\r\n    if (utils.isObject(data)) {\r\n        for (var key in data) {\r\n            if (data.hasOwnProperty(key)) {\r\n                if (key.indexOf('@') === -1) {\r\n                    if (utils.isArray(data[key])) {\r\n                        for (var i = 0; i < data[key].length; ++i) {\r\n                            addFullMetadataToJsonPayload(data[key][i], model, recognizeDates);\r\n                        }\r\n                    } else if (utils.isObject(data[key])) {\r\n                        if (data[key] !== null) {\r\n                            //don't step into geo.. objects\r\n                            type = data[key+'@odata.type'];\r\n                            if (!type) {\r\n                                //type unknown\r\n                                addFullMetadataToJsonPayload(data[key], model, recognizeDates);\r\n                            } else {\r\n                                type = type.substring(1);\r\n                                if  (isGeographyEdmType(type) || isGeometryEdmType(type)) {\r\n                                    // don't add type info for geo* types\r\n                                } else {\r\n                                    addFullMetadataToJsonPayload(data[key], model, recognizeDates);\r\n                                }\r\n                            }\r\n                        }\r\n                    } else {\r\n                        type = data[key + '@odata.type'];\r\n\r\n                        // On .Net OData library, some basic EDM type is omitted, e.g. Edm.String, Edm.Int, and etc.\r\n                        // For the full metadata payload, we need to full fill the @data.type for each property if it is missing.\r\n                        // We do this is to help the OlingoJS consumers to easily get the type of each property.\r\n                        if (!assigned(type)) {\r\n                            // Guessing the \"type\" from the type of the value is not the right way here.\r\n                            // To do: we need to get the type from metadata instead of guessing.\r\n                            var typeFromObject = typeof data[key];\r\n                            if (typeFromObject === 'string') {\r\n                                addType(data, key, 'String');\r\n                            } else if (typeFromObject === 'boolean') {\r\n                                addType(data, key, 'Boolean');\r\n                            } else if (typeFromObject === 'number') {\r\n                                if (data[key] % 1 === 0) { // has fraction\r\n                                    addType(data, key, 'Int32'); // the biggst integer\r\n                                } else {\r\n                                    addType(data, key, 'Decimal'); // the biggst float single,doulbe,decimal\r\n                                }\r\n                            }\r\n                        }\r\n                        else {\r\n                            if (recognizeDates) {\r\n                                convertDatesNoEdm(data, key, type.substring(1));\r\n                            }\r\n                        }\r\n                    }\r\n                }\r\n            }\r\n        }\r\n    }\r\n\r\n    return data;\r\n}\r\n\r\n/** Loop through the properties of an JSON payload object, look up the type info of the property and call\r\n * the appropriate add*MetadataToJsonPayloadObject function\r\n * @param {Object} data - Data structure to be extended\r\n * @param {String} objectInfoType - Information about the data (name,type,typename,...)\r\n * @param {String} baseURI - Base Url\r\n * @param {Object} model - Metadata model\r\n * @param {Boolean} recognizeDates - Flag indicating whether datetime literal strings should be converted to JavaScript Date objects.\r\n */\r\nfunction checkProperties(data, objectInfoType, baseURI, model, recognizeDates) {\r\n    for (var name in data) {\r\n        if (name.indexOf(\"@\") === -1) {\r\n            var curType = objectInfoType;\r\n            var propertyValue = data[name];\r\n            var property = lookupProperty(curType.property,name); //TODO SK add check for parent type\r\n\r\n            while (( property === null) && (curType.baseType !== undefined)) {\r\n                curType = lookupEntityType(curType.baseType, model);\r\n                property = lookupProperty(curType.property,name);\r\n            }\r\n\r\n            if ( isArray(propertyValue)) {\r\n                //data[name+'@odata.type'] = '#' + property.type;\r\n                if (isCollectionType(property.type)) {\r\n                    addTypeColNoEdm(data,name,property.type.substring(11,property.type.length-1));\r\n                } else {\r\n                    addTypeNoEdm(data,name,property.type);\r\n                }\r\n\r\n\r\n                for ( var i = 0; i < propertyValue.length; i++) {\r\n                    addMetadataToJsonMinimalPayloadComplex(propertyValue[i], property, baseURI, model, recognizeDates);\r\n                }\r\n            } else if (isObject(propertyValue) && (propertyValue !== null)) {\r\n                addMetadataToJsonMinimalPayloadComplex(propertyValue, property, baseURI, model, recognizeDates);\r\n            } else {\r\n                //data[name+'@odata.type'] = '#' + property.type;\r\n                addTypeNoEdm(data,name,property.type);\r\n                if (recognizeDates) {\r\n                    convertDates(data, name, property.type);\r\n                }\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n\r\n\r\n/** Add metadata to an JSON payload object containing minimal metadata\r\n * @param {Object} data - Json response payload object\r\n * @param {Object} model - Object describing an OData conceptual schema\r\n * @param {Boolean} recognizeDates - Flag indicating whether datetime literal strings should be converted to JavaScript Date objects.\r\n * @returns {Object} Object in the library's representation.\r\n */\r\nfunction addMinimalMetadataToJsonPayload(data, model, recognizeDates) {\r\n\r\n    if (!assigned(model) || isArray(model)) {\r\n        return data;\r\n    }\r\n\r\n    var baseURI = data[contextUrlAnnotation];\r\n    var payloadInfo = createPayloadInfo(data, model);\r\n\r\n    switch (payloadInfo.detectedPayloadKind) {\r\n\r\n        case PAYLOADTYPE_VALUE:\r\n            if (payloadInfo.type !== null) {\r\n                return addMetadataToJsonMinimalPayloadEntity(data, payloadInfo, baseURI, model, recognizeDates);\r\n            } else {\r\n                return addTypeNoEdm(data,'value', payloadInfo.typeName);\r\n            }\r\n\r\n        case PAYLOADTYPE_FEED:\r\n            return addMetadataToJsonMinimalPayloadFeed(data, model, payloadInfo, baseURI, recognizeDates);\r\n\r\n        case PAYLOADTYPE_ENTRY:\r\n            return addMetadataToJsonMinimalPayloadEntity(data, payloadInfo, baseURI, model, recognizeDates);\r\n\r\n        case PAYLOADTYPE_COLLECTION:\r\n            return addMetadataToJsonMinimalPayloadCollection(data, model, payloadInfo, baseURI, recognizeDates);\r\n\r\n        case PAYLOADTYPE_PROPERTY:\r\n            if (payloadInfo.type !== null) {\r\n                return addMetadataToJsonMinimalPayloadEntity(data, payloadInfo, baseURI, model, recognizeDates);\r\n            } else {\r\n                return addTypeNoEdm(data,'value', payloadInfo.typeName);\r\n            }\r\n\r\n        case PAYLOADTYPE_SVCDOC:\r\n            return data;\r\n\r\n        case PAYLOADTYPE_LINKS:\r\n            return data;\r\n    }\r\n\r\n    return data;\r\n}\r\n\r\n/** Add metadata to an JSON payload feed object containing minimal metadata\r\n * @param {Object} data - Data structure to be extended\r\n * @param {Object} model - Metadata model\r\n * @param {String} feedInfo - Information about the data (name,type,typename,...)\r\n * @param {String} baseURI - Base Url\r\n * @param {Boolean} recognizeDates - Flag indicating whether datetime literal strings should be converted to JavaScript Date objects.\r\n */\r\nfunction addMetadataToJsonMinimalPayloadFeed(data, model, feedInfo, baseURI, recognizeDates) {\r\n    var entries = [];\r\n    var items = data.value;\r\n    var i,len;\r\n    var entry;\r\n    for (i = 0, len = items.length; i < len; i++) {\r\n        var item = items[i];\r\n        if ( defined(item['@odata.type'])) { // in case of mixed feeds\r\n            var typeName = item['@odata.type'].substring(1);\r\n            var type = lookupEntityType( typeName, model);\r\n            var entryInfo = {\r\n                contentTypeOdata : feedInfo.contentTypeOdata,\r\n                detectedPayloadKind : feedInfo.detectedPayloadKind,\r\n                name : feedInfo.name,\r\n                type : type,\r\n                typeName : typeName\r\n            };\r\n\r\n            entry = addMetadataToJsonMinimalPayloadEntity(item, entryInfo, baseURI, model, recognizeDates);\r\n        } else {\r\n            entry = addMetadataToJsonMinimalPayloadEntity(item, feedInfo, baseURI, model, recognizeDates);\r\n        }\r\n\r\n        entries.push(entry);\r\n    }\r\n    data.value = entries;\r\n    return data;\r\n}\r\n\r\n\r\n/** Add metadata to an JSON payload entity object containing minimal metadata\r\n * @param {Object} data - Data structure to be extended\r\n * @param {String} objectInfo - Information about the data (name,type,typename,...)\r\n * @param {String} baseURI - Base Url\r\n * @param {Object} model - Metadata model\r\n * @param {Boolean} recognizeDates - Flag indicating whether datetime literal strings should be converted to JavaScript Date objects.\r\n */\r\nfunction addMetadataToJsonMinimalPayloadEntity(data, objectInfo, baseURI, model, recognizeDates) {\r\n    addType(data,'',objectInfo.typeName);\r\n\r\n    var keyType = objectInfo.type;\r\n    while ((defined(keyType)) && ( keyType.key === undefined) && (keyType.baseType !== undefined)) {\r\n        keyType = lookupEntityType(keyType.baseType, model);\r\n    }\r\n\r\n    if (keyType.key !== undefined) {\r\n        var lastIdSegment = objectInfo.name + jsonGetEntryKey(data, keyType);\r\n        data['@odata.id'] = baseURI.substring(0, baseURI.lastIndexOf(\"$metadata\")) + lastIdSegment;\r\n        data['@odata.editLink'] = lastIdSegment;\r\n    }\r\n\r\n    //var serviceURI = baseURI.substring(0, baseURI.lastIndexOf(\"$metadata\"));\r\n\r\n    checkProperties(data, objectInfo.type, baseURI, model, recognizeDates);\r\n\r\n    return data;\r\n}\r\n\r\n/** Add metadata to an JSON payload complex object containing minimal metadata\r\n * @param {Object} data - Data structure to be extended\r\n * @param {String} property - Information about the data (name,type,typename,...)\r\n * @param {String} baseURI - Base Url\r\n * @param {Object} model - Metadata model\r\n * @param {Boolean} recognizeDates - Flag indicating whether datetime literal strings should be converted to JavaScript Date objects.\r\n */\r\nfunction addMetadataToJsonMinimalPayloadComplex(data, property, baseURI, model, recognizeDates) {\r\n    var type = property.type;\r\n    if (isCollectionType(property.type)) {\r\n        type =property.type.substring(11,property.type.length-1);\r\n    }\r\n\r\n    addType(data,'',property.type);\r\n\r\n    var propertyType = lookupComplexType(type, model);\r\n    if (propertyType === null)  {\r\n        return; //TODO check what to do if the type is not known e.g. type #GeometryCollection\r\n    }\r\n\r\n    checkProperties(data, propertyType, baseURI, model, recognizeDates);\r\n}\r\n\r\n/** Add metadata to an JSON payload collection object containing minimal metadata\r\n * @param {Object} data - Data structure to be extended\r\n * @param {Object} model - Metadata model\r\n * @param {String} collectionInfo - Information about the data (name,type,typename,...)\r\n * @param {String} baseURI - Base Url\r\n * @param {Boolean} recognizeDates - Flag indicating whether datetime literal strings should be converted to JavaScript Date objects.\r\n */\r\nfunction addMetadataToJsonMinimalPayloadCollection(data, model, collectionInfo, baseURI, recognizeDates) {\r\n\r\n    addTypeColNoEdm(data,'', collectionInfo.typeName);\r\n\r\n    if (collectionInfo.type !== null) {\r\n        var entries = [];\r\n\r\n        var items = data.value;\r\n        var i,len;\r\n        var entry;\r\n        for (i = 0, len = items.length; i < len; i++) {\r\n            var item = items[i];\r\n            if ( defined(item['@odata.type'])) { // in case of mixed collections\r\n                var typeName = item['@odata.type'].substring(1);\r\n                var type = lookupEntityType( typeName, model);\r\n                var entryInfo = {\r\n                    contentTypeOdata : collectionInfo.contentTypeOdata,\r\n                    detectedPayloadKind : collectionInfo.detectedPayloadKind,\r\n                    name : collectionInfo.name,\r\n                    type : type,\r\n                    typeName : typeName\r\n                };\r\n\r\n                entry = addMetadataToJsonMinimalPayloadEntity(item, entryInfo, baseURI, model, recognizeDates);\r\n            } else {\r\n                entry = addMetadataToJsonMinimalPayloadEntity(item, collectionInfo, baseURI, model, recognizeDates);\r\n            }\r\n\r\n            entries.push(entry);\r\n        }\r\n        data.value = entries;\r\n    }\r\n    return data;\r\n}\r\n\r\n/** Add an OData type tag to an JSON payload object\r\n * @param {Object} data - Data structure to be extended\r\n * @param {String} name - Name of the property whose type is set\r\n * @param {String} value - Type name\r\n */\r\nfunction addType(data, name, value ) {\r\n    var fullName = name + '@odata.type';\r\n\r\n    if ( data[fullName] === undefined) {\r\n        data[fullName] = '#' + value;\r\n    }\r\n}\r\n\r\n/** Add an OData type tag to an JSON payload object collection (without \"Edm.\" namespace)\r\n * @param {Object} data - Data structure to be extended\r\n * @param {String} name - Name of the property whose type is set\r\n * @param {String} typeName - Type name\r\n */\r\nfunction addTypeColNoEdm(data, name, typeName ) {\r\n    var fullName = name + '@odata.type';\r\n\r\n    if ( data[fullName] === undefined) {\r\n        if ( typeName.substring(0,4)==='Edm.') {\r\n            data[fullName] = '#Collection('+typeName.substring(4)+ ')';\r\n        } else {\r\n            data[fullName] = '#Collection('+typeName+ ')';\r\n        }\r\n    }\r\n}\r\n\r\n\r\n/** Add an OData type tag to an JSON payload object (without \"Edm.\" namespace)\r\n * @param {Object} data - Data structure to be extended\r\n * @param {String} name - Name of the property whose type is set\r\n * @param {String} value - Type name\r\n */\r\nfunction addTypeNoEdm(data, name, value ) {\r\n    var fullName = name + '@odata.type';\r\n\r\n    if ( data[fullName] === undefined) {\r\n        if ( value.substring(0,4)==='Edm.') {\r\n            data[fullName] = '#' + value.substring(4);\r\n        } else {\r\n            data[fullName] = '#' + value;\r\n        }\r\n    }\r\n    return data;\r\n}\r\n/** Convert the date/time format of an property from the JSON payload object (without \"Edm.\" namespace)\r\n * @param {Object} data - Data structure to be extended\r\n * @param propertyName - Name of the property to be changed\r\n * @param type - Type\r\n */\r\nfunction convertDates(data, propertyName,type) {\r\n    if (type === 'Edm.Date') {\r\n        data[propertyName] = oDataUtils.parseDate(data[propertyName], true);\r\n    } else if (type === 'Edm.DateTimeOffset') {\r\n        data[propertyName] = oDataUtils.parseDateTimeOffset(data[propertyName], true);\r\n    } else if (type === 'Edm.Duration') {\r\n        data[propertyName] = oDataUtils.parseDuration(data[propertyName], true);\r\n    } else if (type === 'Edm.Time') {\r\n        data[propertyName] = oDataUtils.parseTime(data[propertyName], true);\r\n    }\r\n}\r\n\r\n/** Convert the date/time format of an property from the JSON payload object\r\n * @param {Object} data - Data structure to be extended\r\n * @param propertyName - Name of the property to be changed\r\n * @param type - Type\r\n */\r\nfunction convertDatesNoEdm(data, propertyName,type) {\r\n    if (type === 'Date') {\r\n        data[propertyName] = oDataUtils.parseDate(data[propertyName], true);\r\n    } else if (type === 'DateTimeOffset') {\r\n        data[propertyName] = oDataUtils.parseDateTimeOffset(data[propertyName], true);\r\n    } else if (type === 'Duration') {\r\n        data[propertyName] = oDataUtils.parseDuration(data[propertyName], true);\r\n    } else if (type === 'Time') {\r\n        data[propertyName] = oDataUtils.parseTime(data[propertyName], true);\r\n    }\r\n}\r\n\r\n/** Formats a value according to Uri literal format\r\n * @param value - Value to be formatted.\r\n * @param type - Edm type of the value\r\n * @returns {string} Value after formatting\r\n */\r\nfunction formatLiteral(value, type) {\r\n\r\n    value = \"\" + formatRawLiteral(value, type);\r\n    value = encodeURIComponent(value.replace(\"'\", \"''\"));\r\n    switch ((type)) {\r\n        case \"Edm.Binary\":\r\n            return \"X'\" + value + \"'\";\r\n        case \"Edm.DateTime\":\r\n            return \"datetime\" + \"'\" + value + \"'\";\r\n        case \"Edm.DateTimeOffset\":\r\n            return \"datetimeoffset\" + \"'\" + value + \"'\";\r\n        case \"Edm.Decimal\":\r\n            return value + \"M\";\r\n        case \"Edm.Guid\":\r\n            return \"guid\" + \"'\" + value + \"'\";\r\n        case \"Edm.Int64\":\r\n            return value + \"L\";\r\n        case \"Edm.Float\":\r\n            return value + \"f\";\r\n        case \"Edm.Double\":\r\n            return value + \"D\";\r\n        case \"Edm.Geography\":\r\n            return \"geography\" + \"'\" + value + \"'\";\r\n        case \"Edm.Geometry\":\r\n            return \"geometry\" + \"'\" + value + \"'\";\r\n        case \"Edm.Time\":\r\n            return \"time\" + \"'\" + value + \"'\";\r\n        case \"Edm.String\":\r\n            return \"'\" + value + \"'\";\r\n        default:\r\n            return value;\r\n    }\r\n}\r\n\r\n/** convert raw byteArray to hexString if the property is an binary property\r\n * @param value - Value to be formatted.\r\n * @param type - Edm type of the value\r\n * @returns {string} Value after formatting\r\n */\r\nfunction formatRawLiteral(value, type) {\r\n    switch (type) {\r\n        case \"Edm.Binary\":\r\n            return convertByteArrayToHexString(value);\r\n        default:\r\n            return value;\r\n    }\r\n}\r\n\r\n/** Formats the given minutes into (+/-)hh:mm format.\r\n * @param {Number} minutes - Number of minutes to format.\r\n * @returns {String} The minutes in (+/-)hh:mm format.\r\n */\r\nfunction minutesToOffset(minutes) {\r\n\r\n    var sign;\r\n    if (minutes < 0) {\r\n        sign = \"-\";\r\n        minutes = -minutes;\r\n    } else {\r\n        sign = \"+\";\r\n    }\r\n\r\n    var hours = Math.floor(minutes / 60);\r\n    minutes = minutes - (60 * hours);\r\n\r\n    return sign + formatNumberWidth(hours, 2) + \":\" + formatNumberWidth(minutes, 2);\r\n}\r\n\r\n/** Parses the JSON Date representation into a Date object.\r\n * @param {String} value - String value.\r\n * @returns {Date} A Date object if the value matches one; falsy otherwise.\r\n */\r\nfunction parseJsonDateString(value) {\r\n\r\n    var arr = value && jsonDateRE.exec(value);\r\n    if (arr) {\r\n        // 0 - complete results; 1 - ticks; 2 - sign; 3 - minutes\r\n        var result = new Date(parseInt10(arr[1]));\r\n        if (arr[2]) {\r\n            var mins = parseInt10(arr[3]);\r\n            if (arr[2] === \"-\") {\r\n                mins = -mins;\r\n            }\r\n\r\n            // The offset is reversed to get back the UTC date, which is\r\n            // what the API will eventually have.\r\n            var current = result.getUTCMinutes();\r\n            result.setUTCMinutes(current - mins);\r\n            result.__edmType = \"Edm.DateTimeOffset\";\r\n            result.__offset = minutesToOffset(mins);\r\n        }\r\n        if (!isNaN(result.valueOf())) {\r\n            return result;\r\n        }\r\n    }\r\n\r\n    // Allow undefined to be returned.\r\n}\r\n\r\n/** Creates an object containing information for the context\r\n * @param {String} fragments - Uri fragment\r\n * @param {Object} model - Object describing an OData conceptual schema\r\n * @returns {Object} type(optional)  object containing type information for entity- and complex-types ( null if a typeName is a primitive)\r\n */\r\nfunction parseContextUriFragment( fragments, model ) {\r\n    var ret = {};\r\n\r\n    if (fragments.indexOf('/') === -1 ) {\r\n        if (fragments.length === 0) {\r\n            // Capter 10.1\r\n            ret.detectedPayloadKind = PAYLOADTYPE_SVCDOC;\r\n            return ret;\r\n        } else if (fragments === 'Edm.Null') {\r\n            // Capter 10.15\r\n            ret.detectedPayloadKind = PAYLOADTYPE_VALUE;\r\n            ret.isNullProperty = true;\r\n            return ret;\r\n        } else if (fragments === 'Collection($ref)') {\r\n            // Capter 10.11\r\n            ret.detectedPayloadKind = PAYLOADTYPE_ENTITY_REF_LINKS;\r\n            return ret;\r\n        } else if (fragments === '$ref') {\r\n            // Capter 10.12\r\n            ret.detectedPayloadKind = PAYLOADTYPE_ENTITY_REF_LINK;\r\n            return ret;\r\n        } else {\r\n            //TODO check for navigation resource\r\n        }\r\n    }\r\n\r\n    ret.type = undefined;\r\n    ret.typeName = undefined;\r\n\r\n    var fragmentParts = fragments.split(\"/\");\r\n    var type;\r\n\r\n    for(var i = 0; i < fragmentParts.length; ++i) {\r\n        var fragment = fragmentParts[i];\r\n        if (ret.typeName === undefined) {\r\n            //preparation\r\n            if ( fragment.indexOf('(') !== -1 ) {\r\n                //remove the query function, cut fragment to matching '('\r\n                var index = fragment.length - 2 ;\r\n                for ( var rCount = 1; rCount > 0 && index > 0; --index) {\r\n                    if ( fragment.charAt(index)=='(') {\r\n                        rCount --;\r\n                    } else if ( fragment.charAt(index)==')') {\r\n                        rCount ++;\r\n                    }\r\n                }\r\n\r\n                if (index === 0) {\r\n                    //TODO throw error\r\n                }\r\n\r\n                //remove the projected entity from the fragment; TODO decide if we want to store the projected entity\r\n                var inPharenthesis = fragment.substring(index+2,fragment.length - 1);\r\n                fragment = fragment.substring(0,index+1);\r\n\r\n                if (utils.startsWith(fragment, 'Collection')) {\r\n                    ret.detectedPayloadKind = PAYLOADTYPE_COLLECTION;\r\n                    // Capter 10.14\r\n                    ret.typeName = inPharenthesis;\r\n\r\n                    type = lookupEntityType(ret.typeName, model);\r\n                    if ( type !== null) {\r\n                        ret.type = type;\r\n                        continue;\r\n                    }\r\n                    type = lookupComplexType(ret.typeName, model);\r\n                    if ( type !== null) {\r\n                        ret.type = type;\r\n                        continue;\r\n                    }\r\n\r\n                    ret.type = null;//in case of #Collection(Edm.String) only lastTypeName is filled\r\n                    continue;\r\n                } else {\r\n                    // projection: Capter 10.7, 10.8 and 10.9\r\n                    ret.projection = inPharenthesis;\r\n                }\r\n            }\r\n\r\n\r\n            if (jsonIsPrimitiveType(fragment)) {\r\n                ret.typeName = fragment;\r\n                ret.type = null;\r\n                ret.detectedPayloadKind = PAYLOADTYPE_VALUE;\r\n                continue;\r\n            }\r\n\r\n            var container = lookupDefaultEntityContainer(model);\r\n\r\n            //check for entity\r\n            var entitySet = lookupEntitySet(container.entitySet, fragment);\r\n            if ( entitySet !== null) {\r\n                ret.typeName = entitySet.entityType;\r\n                ret.type = lookupEntityType( ret.typeName, model);\r\n                ret.name = fragment;\r\n                ret.detectedPayloadKind = PAYLOADTYPE_FEED;\r\n                // Capter 10.2\r\n                continue;\r\n            }\r\n\r\n            //check for singleton\r\n            var singleton = lookupSingleton(container.singleton, fragment);\r\n            if ( singleton !== null) {\r\n                ret.typeName = singleton.entityType;\r\n                ret.type = lookupEntityType( ret.typeName, model);\r\n                ret.name = fragment;\r\n                ret.detectedPayloadKind =  PAYLOADTYPE_ENTRY;\r\n                // Capter 10.4\r\n                continue;\r\n            }\r\n\r\n\r\n\r\n            //TODO throw ERROR\r\n        } else {\r\n            //check for $entity\r\n            if (utils.endsWith(fragment, '$entity') && (ret.detectedPayloadKind === PAYLOADTYPE_FEED)) {\r\n                //TODO ret.name = fragment;\r\n                ret.detectedPayloadKind = PAYLOADTYPE_ENTRY;\r\n                // Capter 10.3 and 10.6\r\n                continue;\r\n            }\r\n\r\n            //check for derived types\r\n            if (fragment.indexOf('.') !== -1) {\r\n                // Capter 10.6\r\n                ret.typeName = fragment;\r\n                type = lookupEntityType(ret.typeName, model);\r\n                if ( type !== null) {\r\n                    ret.type = type;\r\n                    continue;\r\n                }\r\n                type = lookupComplexType(ret.typeName, model);\r\n                if ( type !== null) {\r\n                    ret.type = type;\r\n                    continue;\r\n                }\r\n\r\n                //TODO throw ERROR invalid type\r\n            }\r\n\r\n            //check for property value\r\n            if ( ret.detectedPayloadKind === PAYLOADTYPE_FEED || ret.detectedPayloadKind === PAYLOADTYPE_ENTRY) {\r\n                var property = lookupProperty(ret.type.property, fragment);\r\n                if (property !== null) {\r\n                    //PAYLOADTYPE_COLLECTION\r\n                    ret.typeName = property.type;\r\n\r\n\r\n                    if (utils.startsWith(property.type, 'Collection')) {\r\n                        ret.detectedPayloadKind = PAYLOADTYPE_COLLECTION;\r\n                        var tmp12 =  property.type.substring(10+1,property.type.length - 1);\r\n                        ret.typeName = tmp12;\r\n                        ret.type = lookupComplexType(tmp12, model);\r\n                        ret.detectedPayloadKind = PAYLOADTYPE_COLLECTION;\r\n                    } else {\r\n                        ret.type = lookupComplexType(property.type, model);\r\n                        ret.detectedPayloadKind = PAYLOADTYPE_PROPERTY;\r\n                    }\r\n\r\n                    ret.name = fragment;\r\n                    // Capter 10.15\r\n                }\r\n                continue;\r\n            }\r\n\r\n            if (fragment === '$delta') {\r\n                ret.deltaKind = DELTATYPE_FEED;\r\n                continue;\r\n            } else if (utils.endsWith(fragment, '/$deletedEntity')) {\r\n                ret.deltaKind = DELTATYPE_DELETED_ENTRY;\r\n                continue;\r\n            } else if (utils.endsWith(fragment, '/$link')) {\r\n                ret.deltaKind = DELTATYPE_LINK;\r\n                continue;\r\n            } else if (utils.endsWith(fragment, '/$deletedLink')) {\r\n                ret.deltaKind = DELTATYPE_DELETED_LINK;\r\n                continue;\r\n            }\r\n            //TODO throw ERROr\r\n        }\r\n    }\r\n\r\n    return ret;\r\n}\r\n\r\n\r\n/** Infers the information describing the JSON payload from its metadata annotation, structure, and data model.\r\n * @param {Object} data - Json response payload object.\r\n * @param {Object} model - Object describing an OData conceptual schema.\r\n * If the arguments passed to the function don't convey enough information about the payload to determine without doubt that the payload is a feed then it\r\n * will try to use the payload object structure instead.  If the payload looks like a feed (has value property that is an array or non-primitive values) then\r\n * the function will report its kind as PAYLOADTYPE_FEED unless the inferFeedAsComplexType flag is set to true. This flag comes from the user request\r\n * and allows the user to control how the library behaves with an ambigous JSON payload.\r\n * @return Object with kind and type fields. Null if there is no metadata annotation or the payload info cannot be obtained..\r\n */\r\nfunction createPayloadInfo(data, model) {\r\n    var metadataUri = data[contextUrlAnnotation];\r\n    if (!metadataUri || typeof metadataUri !== \"string\") {\r\n        return null;\r\n    }\r\n\r\n    var fragmentStart = metadataUri.lastIndexOf(\"#\");\r\n    if (fragmentStart === -1) {\r\n        return jsonMakePayloadInfo(PAYLOADTYPE_SVCDOC);\r\n    }\r\n\r\n    var fragment = metadataUri.substring(fragmentStart + 1);\r\n    return parseContextUriFragment(fragment,model);\r\n}\r\n/** Gets the key of an entry.\r\n * @param {Object} data - JSON entry.\r\n * @param {Object} data - EDM entity model for key loockup.\r\n * @returns {string} Entry instance key.\r\n */\r\nfunction jsonGetEntryKey(data, entityModel) {\r\n\r\n    var entityInstanceKey;\r\n    var entityKeys = entityModel.key[0].propertyRef;\r\n    var type;\r\n    entityInstanceKey = \"(\";\r\n    if (entityKeys.length == 1) {\r\n        type = lookupProperty(entityModel.property, entityKeys[0].name).type;\r\n        entityInstanceKey += formatLiteral(data[entityKeys[0].name], type);\r\n    } else {\r\n        var first = true;\r\n        for (var i = 0; i < entityKeys.length; i++) {\r\n            if (!first) {\r\n                entityInstanceKey += \",\";\r\n            } else {\r\n                first = false;\r\n            }\r\n            type = lookupProperty(entityModel.property, entityKeys[i].name).type;\r\n            entityInstanceKey += entityKeys[i].name + \"=\" + formatLiteral(data[entityKeys[i].name], type);\r\n        }\r\n    }\r\n    entityInstanceKey += \")\";\r\n    return entityInstanceKey;\r\n}\r\n/** Determines whether a type name is a primitive type in a JSON payload.\r\n * @param {String} typeName - Type name to test.\r\n * @returns {Boolean} True if the type name an EDM primitive type or an OData spatial type; false otherwise.\r\n */\r\nfunction jsonIsPrimitiveType(typeName) {\r\n    return isPrimitiveEdmType(typeName) || isGeographyEdmType(typeName) || isGeometryEdmType(typeName);\r\n}\r\n\r\n\r\nvar jsonHandler = oDataHandler.handler(jsonParser, jsonSerializer, jsonMediaType, MAX_DATA_SERVICE_VERSION);\r\njsonHandler.recognizeDates = false;\r\n\r\nexports.createPayloadInfo = createPayloadInfo;\r\nexports.jsonHandler = jsonHandler;\r\nexports.jsonParser = jsonParser;\r\nexports.jsonSerializer = jsonSerializer;\r\nexports.parseJsonDateString = parseJsonDateString;}, \"metadata\" : function(exports, module, require) {\r\n'use strict';\r\n\r\n/** @module odata/metadata */\r\n\r\nvar utils    = require('./../utils.js');\r\nvar oDSxml    = require('./../xml.js');\r\nvar odataHandler    = require('./handler.js');\r\n\r\n\r\n\r\n// imports \r\nvar contains = utils.contains;\r\nvar normalizeURI = utils.normalizeURI;\r\nvar xmlAttributes = oDSxml.xmlAttributes;\r\nvar xmlChildElements = oDSxml.xmlChildElements;\r\nvar xmlFirstChildElement = oDSxml.xmlFirstChildElement;\r\nvar xmlInnerText = oDSxml.xmlInnerText;\r\nvar xmlLocalName = oDSxml.xmlLocalName;\r\nvar xmlNamespaceURI = oDSxml.xmlNamespaceURI;\r\nvar xmlNS = oDSxml.xmlNS;\r\nvar xmlnsNS = oDSxml.xmlnsNS;\r\nvar xmlParse = oDSxml.xmlParse;\r\n\r\nvar ado = oDSxml.http + \"docs.oasis-open.org/odata/\";      // http://docs.oasis-open.org/odata/\r\nvar adoDs = ado + \"ns\";                             // http://docs.oasis-open.org/odata/ns\r\nvar edmxNs = adoDs + \"/edmx\";                       // http://docs.oasis-open.org/odata/ns/edmx\r\nvar edmNs1 = adoDs + \"/edm\";                        // http://docs.oasis-open.org/odata/ns/edm\r\nvar odataMetaXmlNs = adoDs + \"/metadata\";           // http://docs.oasis-open.org/odata/ns/metadata\r\nvar MAX_DATA_SERVICE_VERSION = odataHandler.MAX_DATA_SERVICE_VERSION;\r\n\r\nvar xmlMediaType = \"application/xml\";\r\n\r\n/** Creates an object that describes an element in an schema.\r\n * @param {Array} attributes - List containing the names of the attributes allowed for this element.\r\n * @param {Array} elements - List containing the names of the child elements allowed for this element.\r\n * @param {Boolean} text - Flag indicating if the element's text value is of interest or not.\r\n * @param {String} ns - Namespace to which the element belongs to.\r\n * If a child element name ends with * then it is understood by the schema that that child element can appear 0 or more times.\r\n * @returns {Object} Object with attributes, elements, text, and ns fields.\r\n */\r\nfunction schemaElement(attributes, elements, text, ns) {\r\n\r\n    return {\r\n        attributes: attributes,\r\n        elements: elements,\r\n        text: text || false,\r\n        ns: ns\r\n    };\r\n}\r\n\r\n// It's assumed that all elements may have Documentation children and Annotation elements.\r\n// See http://docs.oasis-open.org/odata/odata/v4.0/cs01/part3-csdl/odata-v4.0-cs01-part3-csdl.html for a CSDL reference.\r\nvar schema = {\r\n    elements: {\r\n        Action: schemaElement(\r\n        /*attributes*/[\"Name\", \"IsBound\", \"EntitySetPath\"],\r\n        /*elements*/[\"ReturnType\", \"Parameter*\", \"Annotation*\"]\r\n        ),\r\n        ActionImport: schemaElement(\r\n        /*attributes*/[\"Name\", \"Action\", \"EntitySet\", \"Annotation*\"]\r\n        ),\r\n        Annotation: schemaElement(\r\n        /*attributes*/[\"Term\", \"Qualifier\", \"Binary\", \"Bool\", \"Date\", \"DateTimeOffset\", \"Decimal\", \"Duration\", \"EnumMember\", \"Float\", \"Guid\", \"Int\", \"String\", \"TimeOfDay\", \"AnnotationPath\", \"NavigationPropertyPath\", \"Path\", \"PropertyPath\", \"UrlRef\"],\r\n        /*elements*/[\"Binary*\", \"Bool*\", \"Date*\", \"DateTimeOffset*\", \"Decimal*\", \"Duration*\", \"EnumMember*\", \"Float*\", \"Guid*\", \"Int*\", \"String*\", \"TimeOfDay*\", \"And*\", \"Or*\", \"Not*\", \"Eq*\", \"Ne*\", \"Gt*\", \"Ge*\", \"Lt*\", \"Le*\", \"AnnotationPath*\", \"Apply*\", \"Cast*\", \"Collection*\", \"If*\", \"IsOf*\", \"LabeledElement*\", \"LabeledElementReference*\", \"Null*\", \"NavigationPropertyPath*\", \"Path*\", \"PropertyPath*\", \"Record*\", \"UrlRef*\", \"Annotation*\"]\r\n        ),\r\n        AnnotationPath: schemaElement(\r\n        /*attributes*/null,\r\n        /*elements*/null,\r\n        /*text*/true\r\n        ),\r\n        Annotations: schemaElement(\r\n        /*attributes*/[\"Target\", \"Qualifier\"],\r\n        /*elements*/[\"Annotation*\"]\r\n        ),\r\n        Apply: schemaElement(\r\n        /*attributes*/[\"Function\"],\r\n        /*elements*/[\"String*\", \"Path*\", \"LabeledElement*\", \"Annotation*\"]\r\n        ),\r\n        And: schemaElement(\r\n        /*attributes*/null,\r\n        /*elements*/null,\r\n        /*text*/true\r\n        ),\r\n        Or: schemaElement(\r\n        /*attributes*/null,\r\n        /*elements*/null,\r\n        /*text*/true\r\n        ),\r\n        Not: schemaElement(\r\n        /*attributes*/null,\r\n        /*elements*/null,\r\n        /*text*/true\r\n        ),\r\n        Eq: schemaElement(\r\n        /*attributes*/null,\r\n        /*elements*/null,\r\n        /*text*/true\r\n        ),\r\n        Ne: schemaElement(\r\n        /*attributes*/null,\r\n        /*elements*/null,\r\n        /*text*/true\r\n        ),\r\n        Gt: schemaElement(\r\n        /*attributes*/null,\r\n        /*elements*/null,\r\n        /*text*/true\r\n        ),\r\n        Ge: schemaElement(\r\n        /*attributes*/null,\r\n        /*elements*/null,\r\n        /*text*/true\r\n        ),\r\n        Lt: schemaElement(\r\n        /*attributes*/null,\r\n        /*elements*/null,\r\n        /*text*/true\r\n        ),\r\n        Le: schemaElement(\r\n        /*attributes*/null,\r\n        /*elements*/null,\r\n        /*text*/true\r\n        ),\r\n        Binary: schemaElement(\r\n        /*attributes*/null,\r\n        /*elements*/null,\r\n        /*text*/true\r\n        ),\r\n        Bool: schemaElement(\r\n        /*attributes*/null,\r\n        /*elements*/null,\r\n        /*text*/true\r\n        ),\r\n        Cast: schemaElement(\r\n        /*attributes*/[\"Type\"],\r\n        /*elements*/[\"Path*\", \"Annotation*\"]\r\n        ),\r\n        Collection: schemaElement(\r\n        /*attributes*/null,\r\n        /*elements*/[\"Binary*\", \"Bool*\", \"Date*\", \"DateTimeOffset*\", \"Decimal*\", \"Duration*\", \"EnumMember*\", \"Float*\", \"Guid*\", \"Int*\", \"String*\", \"TimeOfDay*\", \"And*\", \"Or*\", \"Not*\", \"Eq*\", \"Ne*\", \"Gt*\", \"Ge*\", \"Lt*\", \"Le*\", \"AnnotationPath*\", \"Apply*\", \"Cast*\", \"Collection*\", \"If*\", \"IsOf*\", \"LabeledElement*\", \"LabeledElementReference*\", \"Null*\", \"NavigationPropertyPath*\", \"Path*\", \"PropertyPath*\", \"Record*\", \"UrlRef*\"]\r\n        ),\r\n        ComplexType: schemaElement(\r\n        /*attributes*/[\"Name\", \"BaseType\", \"Abstract\", \"OpenType\"],\r\n        /*elements*/[\"Property*\", \"NavigationProperty*\", \"Annotation*\"]\r\n        ),\r\n        Date: schemaElement(\r\n        /*attributes*/null,\r\n        /*elements*/null,\r\n        /*text*/true\r\n        ),\r\n        DateTimeOffset: schemaElement(\r\n        /*attributes*/null,\r\n        /*elements*/null,\r\n        /*text*/true\r\n        ),\r\n        Decimal: schemaElement(\r\n        /*attributes*/null,\r\n        /*elements*/null,\r\n        /*text*/true\r\n        ),\r\n        Duration: schemaElement(\r\n        /*attributes*/null,\r\n        /*elements*/null,\r\n        /*text*/true\r\n        ),\r\n        EntityContainer: schemaElement(\r\n        /*attributes*/[\"Name\", \"Extends\"],\r\n        /*elements*/[\"EntitySet*\", \"Singleton*\", \"ActionImport*\", \"FunctionImport*\", \"Annotation*\"]\r\n        ),\r\n        EntitySet: schemaElement(\r\n        /*attributes*/[\"Name\", \"EntityType\", \"IncludeInServiceDocument\"],\r\n        /*elements*/[\"NavigationPropertyBinding*\", \"Annotation*\"]\r\n        ),\r\n        EntityType: schemaElement(\r\n        /*attributes*/[\"Name\", \"BaseType\", \"Abstract\", \"OpenType\", \"HasStream\"],\r\n        /*elements*/[\"Key*\", \"Property*\", \"NavigationProperty*\", \"Annotation*\"]\r\n        ),\r\n        EnumMember: schemaElement(\r\n        /*attributes*/null,\r\n        /*elements*/null,\r\n        /*text*/true\r\n        ),\r\n        EnumType: schemaElement(\r\n        /*attributes*/[\"Name\", \"UnderlyingType\", \"IsFlags\"],\r\n        /*elements*/[\"Member*\"]\r\n        ),\r\n        Float: schemaElement(\r\n        /*attributes*/null,\r\n        /*elements*/null,\r\n        /*text*/true\r\n        ),\r\n        Function: schemaElement(\r\n        /*attributes*/[\"Name\", \"IsBound\", \"IsComposable\", \"EntitySetPath\"],\r\n        /*elements*/[\"ReturnType\", \"Parameter*\", \"Annotation*\"]\r\n        ),\r\n        FunctionImport: schemaElement(\r\n        /*attributes*/[\"Name\", \"Function\", \"EntitySet\", \"IncludeInServiceDocument\", \"Annotation*\"]\r\n        ),\r\n        Guid: schemaElement(\r\n        /*attributes*/null,\r\n        /*elements*/null,\r\n        /*text*/true\r\n        ),\r\n        If: schemaElement(\r\n        /*attributes*/null,\r\n        /*elements*/[\"Path*\", \"String*\", \"Annotation*\"]\r\n        ),\r\n        Int: schemaElement(\r\n        /*attributes*/null,\r\n        /*elements*/null,\r\n        /*text*/true\r\n        ),\r\n        IsOf: schemaElement(\r\n        /*attributes*/[\"Type\", \"MaxLength\", \"Precision\", \"Scale\", \"Unicode\", \"SRID\", \"DefaultValue\", \"Annotation*\"],\r\n        /*elements*/[\"Path*\"]\r\n        ),\r\n        Key: schemaElement(\r\n        /*attributes*/null,\r\n        /*elements*/[\"PropertyRef*\"]\r\n        ),\r\n        LabeledElement: schemaElement(\r\n        /*attributes*/[\"Name\"],\r\n        /*elements*/[\"Binary*\", \"Bool*\", \"Date*\", \"DateTimeOffset*\", \"Decimal*\", \"Duration*\", \"EnumMember*\", \"Float*\", \"Guid*\", \"Int*\", \"String*\", \"TimeOfDay*\", \"And*\", \"Or*\", \"Not*\", \"Eq*\", \"Ne*\", \"Gt*\", \"Ge*\", \"Lt*\", \"Le*\", \"AnnotationPath*\", \"Apply*\", \"Cast*\", \"Collection*\", \"If*\", \"IsOf*\", \"LabeledElement*\", \"LabeledElementReference*\", \"Null*\", \"NavigationPropertyPath*\", \"Path*\", \"PropertyPath*\", \"Record*\", \"UrlRef*\", \"Annotation*\"]\r\n        ),\r\n        LabeledElementReference: schemaElement(\r\n        /*attributes*/[\"Term\"],\r\n        /*elements*/[\"Binary*\", \"Bool*\", \"Date*\", \"DateTimeOffset*\", \"Decimal*\", \"Duration*\", \"EnumMember*\", \"Float*\", \"Guid*\", \"Int*\", \"String*\", \"TimeOfDay*\", \"And*\", \"Or*\", \"Not*\", \"Eq*\", \"Ne*\", \"Gt*\", \"Ge*\", \"Lt*\", \"Le*\", \"AnnotationPath*\", \"Apply*\", \"Cast*\", \"Collection*\", \"If*\", \"IsOf*\", \"LabeledElement*\", \"LabeledElementReference*\", \"Null*\", \"NavigationPropertyPath*\", \"Path*\", \"PropertyPath*\", \"Record*\", \"UrlRef*\"]\r\n        ),\r\n        Member: schemaElement(\r\n        /*attributes*/[\"Name\", \"Value\"],\r\n        /*element*/[\"Annotation*\"]\r\n        ),\r\n        NavigationProperty: schemaElement(\r\n        /*attributes*/[\"Name\", \"Type\", \"Nullable\", \"Partner\", \"ContainsTarget\"],\r\n        /*elements*/[\"ReferentialConstraint*\", \"OnDelete*\", \"Annotation*\"]\r\n        ),\r\n        NavigationPropertyBinding: schemaElement(\r\n        /*attributes*/[\"Path\", \"Target\"]\r\n        ),\r\n        NavigationPropertyPath: schemaElement(\r\n        /*attributes*/null,\r\n        /*elements*/null,\r\n        /*text*/true\r\n        ),\r\n        Null: schemaElement(\r\n        /*attributes*/null,\r\n        /*elements*/[\"Annotation*\"]\r\n        ),\r\n        OnDelete: schemaElement(\r\n        /*attributes*/[\"Action\"],\r\n        /*elements*/[\"Annotation*\"]\r\n        ),\r\n        Path: schemaElement(\r\n        /*attributes*/null,\r\n        /*elements*/null,\r\n        /*text*/true\r\n        ),\r\n        Parameter: schemaElement(\r\n        /*attributes*/[\"Name\", \"Type\", \"Nullable\", \"MaxLength\", \"Precision\", \"Scale\", \"SRID\"],\r\n        /*elements*/[\"Annotation*\"]\r\n        ),\r\n        Property: schemaElement(\r\n        /*attributes*/[\"Name\", \"Type\", \"Nullable\", \"MaxLength\", \"Precision\", \"Scale\", \"Unicode\", \"SRID\", \"DefaultValue\"],\r\n        /*elements*/[\"Annotation*\"]\r\n        ),\r\n        PropertyPath: schemaElement(\r\n        /*attributes*/null,\r\n        /*elements*/null,\r\n        /*text*/true\r\n        ),\r\n        PropertyRef: schemaElement(\r\n        /*attributes*/[\"Name\", \"Alias\"]\r\n        ),\r\n        PropertyValue: schemaElement(\r\n        /*attributes*/[\"Property\", \"Path\"],\r\n        /*elements*/[\"Binary*\", \"Bool*\", \"Date*\", \"DateTimeOffset*\", \"Decimal*\", \"Duration*\", \"EnumMember*\", \"Float*\", \"Guid*\", \"Int*\", \"String*\", \"TimeOfDay*\", \"And*\", \"Or*\", \"Not*\", \"Eq*\", \"Ne*\", \"Gt*\", \"Ge*\", \"Lt*\", \"Le*\", \"AnnotationPath*\", \"Apply*\", \"Cast*\", \"Collection*\", \"If*\", \"IsOf*\", \"LabeledElement*\", \"LabeledElementReference*\", \"Null*\", \"NavigationPropertyPath*\", \"Path*\", \"PropertyPath*\", \"Record*\", \"UrlRef*\", \"Annotation*\"]\r\n        ),\r\n        Record: schemaElement(\r\n        /*attributes*/null,\r\n        /*Elements*/[\"PropertyValue*\", \"Property*\", \"Annotation*\"]\r\n        ),\r\n        ReferentialConstraint: schemaElement(\r\n        /*attributes*/[\"Property\", \"ReferencedProperty\", \"Annotation*\"]\r\n        ),\r\n        ReturnType: schemaElement(\r\n        /*attributes*/[\"Type\", \"Nullable\", \"MaxLength\", \"Precision\", \"Scale\", \"SRID\"]\r\n        ),\r\n        String: schemaElement(\r\n        /*attributes*/null,\r\n        /*elements*/null,\r\n        /*text*/true\r\n        ),\r\n        Schema: schemaElement(\r\n        /*attributes*/[\"Namespace\", \"Alias\"],\r\n        /*elements*/[\"Action*\", \"Annotations*\", \"Annotation*\", \"ComplexType*\", \"EntityContainer\", \"EntityType*\", \"EnumType*\", \"Function*\", \"Term*\", \"TypeDefinition*\", \"Annotation*\"]\r\n        ),\r\n        Singleton: schemaElement(\r\n        /*attributes*/[\"Name\", \"Type\"],\r\n        /*elements*/[\"NavigationPropertyBinding*\", \"Annotation*\"]\r\n        ),\r\n        Term: schemaElement(\r\n        /*attributes*/[\"Name\", \"Type\", \"BaseTerm\", \"DefaultValue \", \"AppliesTo\", \"Nullable\", \"MaxLength\", \"Precision\", \"Scale\", \"SRID\"],\r\n        /*elements*/[\"Annotation*\"]\r\n        ),\r\n        TimeOfDay: schemaElement(\r\n        /*attributes*/null,\r\n        /*elements*/null,\r\n        /*text*/true\r\n        ),\r\n        TypeDefinition: schemaElement(\r\n        /*attributes*/[\"Name\", \"UnderlyingType\", \"MaxLength\", \"Unicode\", \"Precision\", \"Scale\", \"SRID\"],\r\n        /*elements*/[\"Annotation*\"]\r\n        ),\r\n        UrlRef: schemaElement(\r\n        /*attributes*/null,\r\n        /*elements*/[\"Binary*\", \"Bool*\", \"Date*\", \"DateTimeOffset*\", \"Decimal*\", \"Duration*\", \"EnumMember*\", \"Float*\", \"Guid*\", \"Int*\", \"String*\", \"TimeOfDay*\", \"And*\", \"Or*\", \"Not*\", \"Eq*\", \"Ne*\", \"Gt*\", \"Ge*\", \"Lt*\", \"Le*\", \"AnnotationPath*\", \"Apply*\", \"Cast*\", \"Collection*\", \"If*\", \"IsOf*\", \"LabeledElement*\", \"LabeledElementReference*\", \"Null*\", \"NavigationPropertyPath*\", \"Path*\", \"PropertyPath*\", \"Record*\", \"UrlRef*\", \"Annotation*\"]\r\n        ),\r\n\r\n        // See http://msdn.microsoft.com/en-us/library/dd541238(v=prot.10) for an EDMX reference.\r\n        Edmx: schemaElement(\r\n        /*attributes*/[\"Version\"],\r\n        /*elements*/[\"DataServices\", \"Reference*\"],\r\n        /*text*/false,\r\n        /*ns*/edmxNs\r\n        ),\r\n        DataServices: schemaElement(\r\n        /*attributes*/[\"m:MaxDataServiceVersion\", \"m:DataServiceVersion\"],\r\n        /*elements*/[\"Schema*\"],\r\n        /*text*/false,\r\n        /*ns*/edmxNs\r\n        ),\r\n        Reference: schemaElement(\r\n        /*attributes*/[\"Uri\"],\r\n        /*elements*/[\"Include*\", \"IncludeAnnotations*\", \"Annotation*\"]\r\n        ),\r\n        Include: schemaElement(\r\n        /*attributes*/[\"Namespace\", \"Alias\"]\r\n        ),\r\n        IncludeAnnotations: schemaElement(\r\n        /*attributes*/[\"TermNamespace\", \"Qualifier\", \"TargetNamespace\"]\r\n        )\r\n    }\r\n};\r\n\r\n\r\n/** Converts a Pascal-case identifier into a camel-case identifier.\r\n * @param {String} text - Text to convert.\r\n * @returns {String} Converted text.\r\n * If the text starts with multiple uppercase characters, it is left as-is.\r\n */\r\nfunction scriptCase(text) {\r\n\r\n    if (!text) {\r\n        return text;\r\n    }\r\n\r\n    if (text.length > 1) {\r\n        var firstTwo = text.substr(0, 2);\r\n        if (firstTwo === firstTwo.toUpperCase()) {\r\n            return text;\r\n        }\r\n\r\n        return text.charAt(0).toLowerCase() + text.substr(1);\r\n    }\r\n\r\n    return text.charAt(0).toLowerCase();\r\n}\r\n\r\n/** Gets the schema node for the specified element.\r\n * @param {Object} parentSchema - Schema of the parent XML node of 'element'.\r\n * @param candidateName - XML element name to consider.\r\n * @returns {Object} The schema that describes the specified element; null if not found.\r\n */\r\nfunction getChildSchema(parentSchema, candidateName) {\r\n\r\n    var elements = parentSchema.elements;\r\n    if (!elements) {\r\n        return null;\r\n    }\r\n\r\n    var i, len;\r\n    for (i = 0, len = elements.length; i < len; i++) {\r\n        var elementName = elements[i];\r\n        var multipleElements = false;\r\n        if (elementName.charAt(elementName.length - 1) === \"*\") {\r\n            multipleElements = true;\r\n            elementName = elementName.substr(0, elementName.length - 1);\r\n        }\r\n\r\n        if (candidateName === elementName) {\r\n            var propertyName = scriptCase(elementName);\r\n            return { isArray: multipleElements, propertyName: propertyName };\r\n        }\r\n    }\r\n\r\n    return null;\r\n}\r\n\r\n/** Checks whether the specifies namespace URI is one of the known CSDL namespace URIs.\r\n * @param {String} nsURI - Namespace URI to check.\r\n * @returns {Boolean} true if nsURI is a known CSDL namespace; false otherwise.\r\n */\r\nfunction isEdmNamespace(nsURI) {\r\n\r\n    return nsURI === edmNs1;\r\n}\r\n\r\n/** Parses a CSDL document.\r\n * @param element - DOM element to parse.\r\n * @returns {Object} An object describing the parsed element.\r\n */\r\nfunction parseConceptualModelElement(element) {\r\n\r\n    var localName = xmlLocalName(element);\r\n    var nsURI = xmlNamespaceURI(element);\r\n    var elementSchema = schema.elements[localName];\r\n    if (!elementSchema) {\r\n        return null;\r\n    }\r\n\r\n    if (elementSchema.ns) {\r\n        if (nsURI !== elementSchema.ns) {\r\n            return null;\r\n        }\r\n    } else if (!isEdmNamespace(nsURI)) {\r\n        return null;\r\n    }\r\n\r\n    var item = {};\r\n    var attributes = elementSchema.attributes || [];\r\n    xmlAttributes(element, function (attribute) {\r\n\r\n        var localName = xmlLocalName(attribute);\r\n        var nsURI = xmlNamespaceURI(attribute);\r\n        var value = attribute.value;\r\n\r\n        // Don't do anything with xmlns attributes.\r\n        if (nsURI === xmlnsNS) {\r\n            return;\r\n        }\r\n\r\n        // Currently, only m: for metadata is supported as a prefix in the internal schema table,\r\n        // un-prefixed element names imply one a CSDL element.\r\n        var schemaName = null;\r\n        if (isEdmNamespace(nsURI) || nsURI === null) {\r\n            schemaName = \"\";\r\n        } else if (nsURI === odataMetaXmlNs) {\r\n            schemaName = \"m:\";\r\n        }\r\n\r\n        if (schemaName !== null) {\r\n            schemaName += localName;\r\n\r\n            if (contains(attributes, schemaName)) {\r\n                item[scriptCase(localName)] = value;\r\n            }\r\n        }\r\n\r\n    });\r\n\r\n    xmlChildElements(element, function (child) {\r\n        var localName = xmlLocalName(child);\r\n        var childSchema = getChildSchema(elementSchema, localName);\r\n        if (childSchema) {\r\n            if (childSchema.isArray) {\r\n                var arr = item[childSchema.propertyName];\r\n                if (!arr) {\r\n                    arr = [];\r\n                    item[childSchema.propertyName] = arr;\r\n                }\r\n                arr.push(parseConceptualModelElement(child));\r\n            } else {\r\n                item[childSchema.propertyName] = parseConceptualModelElement(child);\r\n            }\r\n        } \r\n    });\r\n\r\n    if (elementSchema.text) {\r\n        item.text = xmlInnerText(element);\r\n    }\r\n\r\n    return item;\r\n}\r\n\r\n/** Parses a metadata document.\r\n * @param handler - This handler.\r\n * @param {String} text - Metadata text.\r\n * @returns An object representation of the conceptual model.\r\n */\r\nfunction metadataParser(handler, text) {\r\n\r\n    var doc = xmlParse(text);\r\n    var root = xmlFirstChildElement(doc);\r\n    return parseConceptualModelElement(root) || undefined;\r\n}\r\n\r\n\r\n\r\nexports.metadataHandler = odataHandler.handler(metadataParser, null, xmlMediaType, MAX_DATA_SERVICE_VERSION);\r\n\r\nexports.schema = schema;\r\nexports.scriptCase = scriptCase;\r\nexports.getChildSchema = getChildSchema;\r\nexports.parseConceptualModelElement = parseConceptualModelElement;\r\nexports.metadataParser = metadataParser;}, \"net\" : function(exports, module, require) {\r\n\r\n/** @module odata/net */\r\n/*for browser*/\r\n\r\n\r\nvar utils    = require('./../utils.js');\r\n// Imports.\r\n\r\nvar defined = utils.defined;\r\nvar delay = utils.delay;\r\n\r\nvar ticks = 0;\r\n\r\n/* Checks whether the specified request can be satisfied with a JSONP request.\r\n * @param request - Request object to check.\r\n * @returns {Boolean} true if the request can be satisfied; false otherwise.\r\n\r\n * Requests that 'degrade' without changing their meaning by going through JSONP\r\n * are considered usable.\r\n *\r\n * We allow data to come in a different format, as the servers SHOULD honor the Accept\r\n * request but may in practice return content with a different MIME type.\r\n */\r\nfunction canUseJSONP(request) {\r\n    \r\n    return !(request.method && request.method !== \"GET\");\r\n\r\n\r\n}\r\n\r\n/** Creates an IFRAME tag for loading the JSONP script\r\n * @param {String} url - The source URL of the script\r\n * @returns {HTMLElement} The IFRAME tag\r\n */\r\nfunction createIFrame(url) {\r\n    var iframe = window.document.createElement(\"IFRAME\");\r\n    iframe.style.display = \"none\";\r\n\r\n    var attributeEncodedUrl = url.replace(/&/g, \"&amp;\").replace(/\"/g, \"&quot;\").replace(/</g, \"&lt;\");\r\n    var html = \"<html><head><script type=\\\"text/javascript\\\" src=\\\"\" + attributeEncodedUrl + \"\\\"><\\/script><\\/head><body><\\/body><\\/html>\";\r\n\r\n    var body = window.document.getElementsByTagName(\"BODY\")[0];\r\n    body.appendChild(iframe);\r\n\r\n    writeHtmlToIFrame(iframe, html);\r\n    return iframe;\r\n}\r\n\r\n/** Creates a XmlHttpRequest object.\r\n * @returns {XmlHttpRequest} XmlHttpRequest object.\r\n */\r\nfunction createXmlHttpRequest() {\r\n    if (window.XMLHttpRequest) {\r\n        return new window.XMLHttpRequest();\r\n    }\r\n    var exception;\r\n    if (window.ActiveXObject) {\r\n        try {\r\n            return new window.ActiveXObject(\"Msxml2.XMLHTTP.6.0\");\r\n        } catch (_) {\r\n            try {\r\n                return new window.ActiveXObject(\"Msxml2.XMLHTTP.3.0\");\r\n            } catch (e) {\r\n                exception = e;\r\n            }\r\n        }\r\n    } else {\r\n        exception = { message: \"XMLHttpRequest not supported\" };\r\n    }\r\n    throw exception;\r\n}\r\n\r\n/** Checks whether the specified URL is an absolute URL.\r\n * @param {String} url - URL to check.\r\n * @returns {Boolean} true if the url is an absolute URL; false otherwise.\r\n*/\r\nfunction isAbsoluteUrl(url) {\r\n    return url.indexOf(\"http://\") === 0 ||\r\n        url.indexOf(\"https://\") === 0 ||\r\n        url.indexOf(\"file://\") === 0;\r\n}\r\n\r\n/** Checks whether the specified URL is local to the current context.\r\n * @param {String} url - URL to check.\r\n * @returns {Boolean} true if the url is a local URL; false otherwise.\r\n */\r\nfunction isLocalUrl(url) {\r\n\r\n    if (!isAbsoluteUrl(url)) {\r\n        return true;\r\n    }\r\n\r\n    // URL-embedded username and password will not be recognized as same-origin URLs.\r\n    var location = window.location;\r\n    var locationDomain = location.protocol + \"//\" + location.host + \"/\";\r\n    return (url.indexOf(locationDomain) === 0);\r\n}\r\n\r\n/** Removes a callback used for a JSONP request.\r\n * @param {String} name - Function name to remove.\r\n * @param {Number} tick - Tick count used on the callback.\r\n */\r\nfunction removeCallback(name, tick) {\r\n    try {\r\n        delete window[name];\r\n    } catch (err) {\r\n        window[name] = undefined;\r\n        if (tick === ticks - 1) {\r\n            ticks -= 1;\r\n        }\r\n    }\r\n}\r\n\r\n/** Removes an iframe.\r\n * @param {Object} iframe - The iframe to remove.\r\n * @returns {Object} Null value to be assigned to iframe reference.\r\n */\r\nfunction removeIFrame(iframe) {\r\n    if (iframe) {\r\n        writeHtmlToIFrame(iframe, \"\");\r\n        iframe.parentNode.removeChild(iframe);\r\n    }\r\n\r\n    return null;\r\n}\r\n\r\n/** Reads response headers into array.\r\n * @param {XMLHttpRequest} xhr - HTTP request with response available.\r\n * @param {Array} headers - Target array to fill with name/value pairs.\r\n */\r\nfunction readResponseHeaders(xhr, headers) {\r\n\r\n    var responseHeaders = xhr.getAllResponseHeaders().split(/\\r?\\n/);\r\n    var i, len;\r\n    for (i = 0, len = responseHeaders.length; i < len; i++) {\r\n        if (responseHeaders[i]) {\r\n            var header = responseHeaders[i].split(\": \");\r\n            headers[header[0]] = header[1];\r\n        }\r\n    }\r\n}\r\n\r\n/** Writes HTML to an IFRAME document.\r\n * @param {HTMLElement} iframe - The IFRAME element to write to.\r\n * @param {String} html - The HTML to write.\r\n */\r\nfunction writeHtmlToIFrame(iframe, html) {\r\n    var frameDocument = (iframe.contentWindow) ? iframe.contentWindow.document : iframe.contentDocument.document;\r\n    frameDocument.open();\r\n    frameDocument.write(html);\r\n    frameDocument.close();\r\n}\r\n\r\nexports.defaultHttpClient = {\r\n    callbackParameterName: \"$callback\",\r\n\r\n    formatQueryString: \"$format=json\",\r\n\r\n    enableJsonpCallback: false,\r\n\r\n    /** Performs a network request.\r\n     * @param {Object} request - Request description\r\n     * @param {Function} success - Success callback with the response object.\r\n     * @param {Function} error - Error callback with an error object.\r\n     * @returns {Object} Object with an 'abort' method for the operation.\r\n     */\r\n    request: function createRequest() {\r\n\r\n        var that = this;\r\n\r\n\r\n        return function(request, success, error) {\r\n\r\n        var result = {};\r\n        var xhr = null;\r\n        var done = false;\r\n        var iframe;\r\n\r\n        result.abort = function () {\r\n            iframe = removeIFrame(iframe);\r\n            if (done) {\r\n                return;\r\n            }\r\n\r\n            done = true;\r\n            if (xhr) {\r\n                xhr.abort();\r\n                xhr = null;\r\n            }\r\n\r\n            error({ message: \"Request aborted\" });\r\n        };\r\n\r\n        var handleTimeout = function () {\r\n            iframe = removeIFrame(iframe);\r\n            if (!done) {\r\n                done = true;\r\n                xhr = null;\r\n                error({ message: \"Request timed out\" });\r\n            }\r\n        };\r\n\r\n        var name;\r\n        var url = request.requestUri;\r\n        var enableJsonpCallback = defined(request.enableJsonpCallback , that.enableJsonpCallback);\r\n        var callbackParameterName = defined(request.callbackParameterName, that.callbackParameterName);\r\n        var formatQueryString = defined(request.formatQueryString, that.formatQueryString);\r\n        if (!enableJsonpCallback || isLocalUrl(url)) {\r\n\r\n            xhr = createXmlHttpRequest();\r\n            xhr.onreadystatechange = function () {\r\n                if (done || xhr === null || xhr.readyState !== 4) {\r\n                    return;\r\n                }\r\n\r\n                // Workaround for XHR behavior on IE.\r\n                var statusText = xhr.statusText;\r\n                var statusCode = xhr.status;\r\n                if (statusCode === 1223) {\r\n                    statusCode = 204;\r\n                    statusText = \"No Content\";\r\n                }\r\n\r\n                var headers = [];\r\n                readResponseHeaders(xhr, headers);\r\n\r\n                var response = { requestUri: url, statusCode: statusCode, statusText: statusText, headers: headers, body: xhr.responseText };\r\n\r\n                done = true;\r\n                xhr = null;\r\n                if (statusCode >= 200 && statusCode <= 299) {\r\n                    success(response);\r\n                } else {\r\n                    error({ message: \"HTTP request failed\", request: request, response: response });\r\n                }\r\n            };\r\n\r\n            xhr.open(request.method || \"GET\", url, true, request.user, request.password);\r\n\r\n            // Set the name/value pairs.\r\n            if (request.headers) {\r\n                for (name in request.headers) {\r\n                    xhr.setRequestHeader(name, request.headers[name]);\r\n                }\r\n            }\r\n\r\n            // Set the timeout if available.\r\n            if (request.timeoutMS) {\r\n                xhr.timeout = request.timeoutMS;\r\n                xhr.ontimeout = handleTimeout;\r\n            }\r\n\r\n            xhr.send(request.body);\r\n        } else {\r\n            if (!canUseJSONP(request)) {\r\n                throw { message: \"Request is not local and cannot be done through JSONP.\" };\r\n            }\r\n\r\n            var tick = ticks;\r\n            ticks += 1;\r\n            var tickText = tick.toString();\r\n            var succeeded = false;\r\n            var timeoutId;\r\n            name = \"handleJSONP_\" + tickText;\r\n            window[name] = function (data) {\r\n                iframe = removeIFrame(iframe);\r\n                if (!done) {\r\n                    succeeded = true;\r\n                    window.clearTimeout(timeoutId);\r\n                    removeCallback(name, tick);\r\n\r\n                    // Workaround for IE8 and IE10 below where trying to access data.constructor after the IFRAME has been removed\r\n                    // throws an \"unknown exception\"\r\n                    if (window.ActiveXObject) {\r\n                        data = window.JSON.parse(window.JSON.stringify(data));\r\n                    }\r\n\r\n\r\n                    var headers;\r\n                    if (!formatQueryString || formatQueryString == \"$format=json\") {\r\n                        headers = { \"Content-Type\": \"application/json;odata.metadata=minimal\", \"OData-Version\": \"4.0\" };\r\n                    } else {\r\n                        // the formatQueryString should be in the format of \"$format=xxx\", xxx should be one of the application/json;odata.metadata=minimal(none or full)\r\n                        // set the content-type with the string xxx which stars from index 8.\r\n                        headers = { \"Content-Type\": formatQueryString.substring(8), \"OData-Version\": \"4.0\" };\r\n                    }\r\n\r\n                    // Call the success callback in the context of the parent window, instead of the IFRAME\r\n                    delay(function () {\r\n                        removeIFrame(iframe);\r\n                        success({ body: data, statusCode: 200, headers: headers });\r\n                    });\r\n                }\r\n            };\r\n\r\n            // Default to two minutes before timing out, 1000 ms * 60 * 2 = 120000.\r\n            var timeoutMS = (request.timeoutMS) ? request.timeoutMS : 120000;\r\n            timeoutId = window.setTimeout(handleTimeout, timeoutMS);\r\n\r\n            var queryStringParams = callbackParameterName + \"=parent.\" + name;\r\n            if (formatQueryString) {\r\n                queryStringParams += \"&\" + formatQueryString;\r\n            }\r\n\r\n            var qIndex = url.indexOf(\"?\");\r\n            if (qIndex === -1) {\r\n                url = url + \"?\" + queryStringParams;\r\n            } else if (qIndex === url.length - 1) {\r\n                url = url + queryStringParams;\r\n            } else {\r\n                url = url + \"&\" + queryStringParams;\r\n            }\r\n\r\n            iframe = createIFrame(url);\r\n        }\r\n\r\n        return result;\r\n    }\r\n    }()\r\n};\r\n\r\n\r\n\r\nexports.canUseJSONP = canUseJSONP;\r\nexports.isAbsoluteUrl = isAbsoluteUrl;\r\nexports.isLocalUrl = isLocalUrl;}, \"odatautils\" : function(exports, module, require) {\r\n'use strict';\r\n /** @module odata/utils */\r\n\r\nvar utils    = require('./../utils.js');\r\n\r\n// Imports\r\nvar assigned = utils.assigned;\r\nvar contains = utils.contains;\r\nvar find = utils.find;\r\nvar isArray = utils.isArray;\r\nvar isDate = utils.isDate;\r\nvar isObject = utils.isObject;\r\nvar parseInt10 = utils.parseInt10;\r\n\r\n\r\n/** Gets the type name of a data item value that belongs to a feed, an entry, a complex type property, or a collection property\r\n * @param {string} value - Value of the data item from which the type name is going to be retrieved.\r\n * @param {object} [metadata] - Object containing metadata about the data tiem.\r\n * @returns {string} Data item type name; null if the type name cannot be found within the value or the metadata\r\n * This function will first try to get the type name from the data item's value itself if it is an object with a __metadata property; otherwise\r\n * it will try to recover it from the metadata.  If both attempts fail, it will return null.\r\n */\r\nvar dataItemTypeName = function (value, metadata) {\r\n    var valueTypeName = ((value && value.__metadata) || {}).type;\r\n    return valueTypeName || (metadata ? metadata.type : null);\r\n};\r\n\r\nvar EDM = \"Edm.\";\r\nvar EDM_BOOLEAN = EDM + \"Boolean\";\r\nvar EDM_BYTE = EDM + \"Byte\";\r\nvar EDM_SBYTE = EDM + \"SByte\";\r\nvar EDM_INT16 = EDM + \"Int16\";\r\nvar EDM_INT32 = EDM + \"Int32\";\r\nvar EDM_INT64 = EDM + \"Int64\";\r\nvar EDM_SINGLE = EDM + \"Single\";\r\nvar EDM_DOUBLE = EDM + \"Double\";\r\nvar EDM_DECIMAL = EDM + \"Decimal\";\r\nvar EDM_STRING = EDM + \"String\";\r\n\r\nvar EDM_BINARY = EDM + \"Binary\";\r\nvar EDM_DATE = EDM + \"Date\";\r\nvar EDM_DATETIMEOFFSET = EDM + \"DateTimeOffset\";\r\nvar EDM_DURATION = EDM + \"Duration\";\r\nvar EDM_GUID = EDM + \"Guid\";\r\nvar EDM_TIMEOFDAY = EDM + \"Time\";\r\n\r\nvar GEOGRAPHY = \"Geography\";\r\nvar EDM_GEOGRAPHY = EDM + GEOGRAPHY;\r\nvar EDM_GEOGRAPHY_POINT = EDM_GEOGRAPHY + \"Point\";\r\nvar EDM_GEOGRAPHY_LINESTRING = EDM_GEOGRAPHY + \"LineString\";\r\nvar EDM_GEOGRAPHY_POLYGON = EDM_GEOGRAPHY + \"Polygon\";\r\nvar EDM_GEOGRAPHY_COLLECTION = EDM_GEOGRAPHY + \"Collection\";\r\nvar EDM_GEOGRAPHY_MULTIPOLYGON = EDM_GEOGRAPHY + \"MultiPolygon\";\r\nvar EDM_GEOGRAPHY_MULTILINESTRING = EDM_GEOGRAPHY + \"MultiLineString\";\r\nvar EDM_GEOGRAPHY_MULTIPOINT = EDM_GEOGRAPHY + \"MultiPoint\";\r\n\r\nvar GEOGRAPHY_POINT = GEOGRAPHY + \"Point\";\r\nvar GEOGRAPHY_LINESTRING = GEOGRAPHY + \"LineString\";\r\nvar GEOGRAPHY_POLYGON = GEOGRAPHY + \"Polygon\";\r\nvar GEOGRAPHY_COLLECTION = GEOGRAPHY + \"Collection\";\r\nvar GEOGRAPHY_MULTIPOLYGON = GEOGRAPHY + \"MultiPolygon\";\r\nvar GEOGRAPHY_MULTILINESTRING = GEOGRAPHY + \"MultiLineString\";\r\nvar GEOGRAPHY_MULTIPOINT = GEOGRAPHY + \"MultiPoint\";\r\n\r\nvar GEOMETRY = \"Geometry\";\r\nvar EDM_GEOMETRY = EDM + GEOMETRY;\r\nvar EDM_GEOMETRY_POINT = EDM_GEOMETRY + \"Point\";\r\nvar EDM_GEOMETRY_LINESTRING = EDM_GEOMETRY + \"LineString\";\r\nvar EDM_GEOMETRY_POLYGON = EDM_GEOMETRY + \"Polygon\";\r\nvar EDM_GEOMETRY_COLLECTION = EDM_GEOMETRY + \"Collection\";\r\nvar EDM_GEOMETRY_MULTIPOLYGON = EDM_GEOMETRY + \"MultiPolygon\";\r\nvar EDM_GEOMETRY_MULTILINESTRING = EDM_GEOMETRY + \"MultiLineString\";\r\nvar EDM_GEOMETRY_MULTIPOINT = EDM_GEOMETRY + \"MultiPoint\";\r\n\r\nvar GEOMETRY_POINT = GEOMETRY + \"Point\";\r\nvar GEOMETRY_LINESTRING = GEOMETRY + \"LineString\";\r\nvar GEOMETRY_POLYGON = GEOMETRY + \"Polygon\";\r\nvar GEOMETRY_COLLECTION = GEOMETRY + \"Collection\";\r\nvar GEOMETRY_MULTIPOLYGON = GEOMETRY + \"MultiPolygon\";\r\nvar GEOMETRY_MULTILINESTRING = GEOMETRY + \"MultiLineString\";\r\nvar GEOMETRY_MULTIPOINT = GEOMETRY + \"MultiPoint\";\r\n\r\nvar GEOJSON_POINT = \"Point\";\r\nvar GEOJSON_LINESTRING = \"LineString\";\r\nvar GEOJSON_POLYGON = \"Polygon\";\r\nvar GEOJSON_MULTIPOINT = \"MultiPoint\";\r\nvar GEOJSON_MULTILINESTRING = \"MultiLineString\";\r\nvar GEOJSON_MULTIPOLYGON = \"MultiPolygon\";\r\nvar GEOJSON_GEOMETRYCOLLECTION = \"GeometryCollection\";\r\n\r\nvar primitiveEdmTypes = [\r\n    EDM_STRING,\r\n    EDM_INT32,\r\n    EDM_INT64,\r\n    EDM_BOOLEAN,\r\n    EDM_DOUBLE,\r\n    EDM_SINGLE,\r\n    EDM_DATE,\r\n    EDM_DATETIMEOFFSET,\r\n    EDM_DURATION,\r\n    EDM_TIMEOFDAY,\r\n    EDM_DECIMAL,\r\n    EDM_GUID,\r\n    EDM_BYTE,\r\n    EDM_INT16,\r\n    EDM_SBYTE,\r\n    EDM_BINARY\r\n];\r\n\r\nvar geometryEdmTypes = [\r\n    EDM_GEOMETRY,\r\n    EDM_GEOMETRY_POINT,\r\n    EDM_GEOMETRY_LINESTRING,\r\n    EDM_GEOMETRY_POLYGON,\r\n    EDM_GEOMETRY_COLLECTION,\r\n    EDM_GEOMETRY_MULTIPOLYGON,\r\n    EDM_GEOMETRY_MULTILINESTRING,\r\n    EDM_GEOMETRY_MULTIPOINT\r\n];\r\n\r\nvar geometryTypes = [\r\n    GEOMETRY,\r\n    GEOMETRY_POINT,\r\n    GEOMETRY_LINESTRING,\r\n    GEOMETRY_POLYGON,\r\n    GEOMETRY_COLLECTION,\r\n    GEOMETRY_MULTIPOLYGON,\r\n    GEOMETRY_MULTILINESTRING,\r\n    GEOMETRY_MULTIPOINT\r\n];\r\n\r\nvar geographyEdmTypes = [\r\n    EDM_GEOGRAPHY,\r\n    EDM_GEOGRAPHY_POINT,\r\n    EDM_GEOGRAPHY_LINESTRING,\r\n    EDM_GEOGRAPHY_POLYGON,\r\n    EDM_GEOGRAPHY_COLLECTION,\r\n    EDM_GEOGRAPHY_MULTIPOLYGON,\r\n    EDM_GEOGRAPHY_MULTILINESTRING,\r\n    EDM_GEOGRAPHY_MULTIPOINT\r\n];\r\n\r\nvar geographyTypes = [\r\n    GEOGRAPHY,\r\n    GEOGRAPHY_POINT,\r\n    GEOGRAPHY_LINESTRING,\r\n    GEOGRAPHY_POLYGON,\r\n    GEOGRAPHY_COLLECTION,\r\n    GEOGRAPHY_MULTIPOLYGON,\r\n    GEOGRAPHY_MULTILINESTRING,\r\n    GEOGRAPHY_MULTIPOINT\r\n];\r\n\r\n/** Invokes a function once per schema in metadata.\r\n * @param metadata - Metadata store; one of edmx, schema, or an array of any of them.\r\n * @param {Function} callback - Callback function to invoke once per schema.\r\n * @returns The first truthy value to be returned from the callback; null or the last falsy value otherwise.\r\n */\r\nfunction forEachSchema(metadata, callback) {\r\n    \r\n\r\n    if (!metadata) {\r\n        return null;\r\n    }\r\n\r\n    if (isArray(metadata)) {\r\n        var i, len, result;\r\n        for (i = 0, len = metadata.length; i < len; i++) {\r\n            result = forEachSchema(metadata[i], callback);\r\n            if (result) {\r\n                return result;\r\n            }\r\n        }\r\n\r\n        return null;\r\n    } else {\r\n        if (metadata.dataServices) {\r\n            return forEachSchema(metadata.dataServices.schema, callback);\r\n        }\r\n\r\n        return callback(metadata);\r\n    }\r\n}\r\n\r\n/** Formats a millisecond and a nanosecond value into a single string.\r\n * @param {Number} ms - Number of milliseconds to format.\r\n * @param {Number} ns - Number of nanoseconds to format.\r\n * @returns {String} Formatted text.\r\n * If the value is already as string it's returned as-is.\r\n */\r\nfunction formatMilliseconds(ms, ns) {\r\n\r\n    // Avoid generating milliseconds if not necessary.\r\n    if (ms === 0) {\r\n        ms = \"\";\r\n    } else {\r\n        ms = \".\" + formatNumberWidth(ms.toString(), 3);\r\n    }\r\n    if (ns > 0) {\r\n        if (ms === \"\") {\r\n            ms = \".000\";\r\n        }\r\n        ms += formatNumberWidth(ns.toString(), 4);\r\n    }\r\n    return ms;\r\n}\r\n\r\nfunction formatDateTimeOffsetJSON(value) {\r\n    return \"\\/Date(\" + value.getTime() + \")\\/\";\r\n}\r\n\r\n/** Formats a DateTime or DateTimeOffset value a string.\r\n * @param {Date} value - Value to format\r\n * @returns {String} Formatted text.\r\n * If the value is already as string it's returned as-is\r\n´*/\r\nfunction formatDateTimeOffset(value) {\r\n\r\n    if (typeof value === \"string\") {\r\n        return value;\r\n    }\r\n\r\n    var hasOffset = isDateTimeOffset(value);\r\n    var offset = getCanonicalTimezone(value.__offset);\r\n    if (hasOffset && offset !== \"Z\") {\r\n        // We're about to change the value, so make a copy.\r\n        value = new Date(value.valueOf());\r\n\r\n        var timezone = parseTimezone(offset);\r\n        var hours = value.getUTCHours() + (timezone.d * timezone.h);\r\n        var minutes = value.getUTCMinutes() + (timezone.d * timezone.m);\r\n\r\n        value.setUTCHours(hours, minutes);\r\n    } else if (!hasOffset) {\r\n        // Don't suffix a 'Z' for Edm.DateTime values.\r\n        offset = \"\";\r\n    }\r\n\r\n    var year = value.getUTCFullYear();\r\n    var month = value.getUTCMonth() + 1;\r\n    var sign = \"\";\r\n    if (year <= 0) {\r\n        year = -(year - 1);\r\n        sign = \"-\";\r\n    }\r\n\r\n    var ms = formatMilliseconds(value.getUTCMilliseconds(), value.__ns);\r\n\r\n    return sign +\r\n        formatNumberWidth(year, 4) + \"-\" +\r\n        formatNumberWidth(month, 2) + \"-\" +\r\n        formatNumberWidth(value.getUTCDate(), 2) + \"T\" +\r\n        formatNumberWidth(value.getUTCHours(), 2) + \":\" +\r\n        formatNumberWidth(value.getUTCMinutes(), 2) + \":\" +\r\n        formatNumberWidth(value.getUTCSeconds(), 2) +\r\n        ms + offset;\r\n}\r\n\r\n/** Converts a duration to a string in xsd:duration format.\r\n * @param {Object} value - Object with ms and __edmType properties.\r\n * @returns {String} String representation of the time object in xsd:duration format.\r\n */\r\nfunction formatDuration(value) {\r\n\r\n    var ms = value.ms;\r\n\r\n    var sign = \"\";\r\n    if (ms < 0) {\r\n        sign = \"-\";\r\n        ms = -ms;\r\n    }\r\n\r\n    var days = Math.floor(ms / 86400000);\r\n    ms -= 86400000 * days;\r\n    var hours = Math.floor(ms / 3600000);\r\n    ms -= 3600000 * hours;\r\n    var minutes = Math.floor(ms / 60000);\r\n    ms -= 60000 * minutes;\r\n    var seconds = Math.floor(ms / 1000);\r\n    ms -= seconds * 1000;\r\n\r\n    return sign + \"P\" +\r\n           formatNumberWidth(days, 2) + \"DT\" +\r\n           formatNumberWidth(hours, 2) + \"H\" +\r\n           formatNumberWidth(minutes, 2) + \"M\" +\r\n           formatNumberWidth(seconds, 2) +\r\n           formatMilliseconds(ms, value.ns) + \"S\";\r\n}\r\n\r\n/** Formats the specified value to the given width.\r\n * @param {Number} value - Number to format (non-negative).\r\n * @param {Number} width - Minimum width for number.\r\n * @param {Boolean} append - Flag indicating if the value is padded at the beginning (false) or at the end (true).\r\n * @returns {String} Text representation.\r\n */\r\nfunction formatNumberWidth(value, width, append) {\r\n    var result = value.toString(10);\r\n    while (result.length < width) {\r\n        if (append) {\r\n            result += \"0\";\r\n        } else {\r\n            result = \"0\" + result;\r\n        }\r\n    }\r\n\r\n    return result;\r\n}\r\n\r\n/** Gets the canonical timezone representation.\r\n * @param {String} timezone - Timezone representation.\r\n * @returns {String} An 'Z' string if the timezone is absent or 0; the timezone otherwise.\r\n */\r\nfunction getCanonicalTimezone(timezone) {\r\n\r\n    return (!timezone || timezone === \"Z\" || timezone === \"+00:00\" || timezone === \"-00:00\") ? \"Z\" : timezone;\r\n}\r\n\r\n/** Gets the type of a collection type name.\r\n * @param {String} typeName - Type name of the collection.\r\n * @returns {String} Type of the collection; null if the type name is not a collection type.\r\n */\r\nfunction getCollectionType(typeName) {\r\n\r\n    if (typeof typeName === \"string\") {\r\n        var end = typeName.indexOf(\")\", 10);\r\n        if (typeName.indexOf(\"Collection(\") === 0 && end > 0) {\r\n            return typeName.substring(11, end);\r\n        }\r\n    }\r\n    return null;\r\n}\r\n\r\n/** Sends a request containing OData payload to a server.\r\n* @param request - Object that represents the request to be sent..\r\n* @param success - Callback for a successful read operation.\r\n* @param error - Callback for handling errors.\r\n* @param handler - Handler for data serialization.\r\n* @param httpClient - HTTP client layer.\r\n* @param context - Context used for processing the request\r\n*/\r\nfunction invokeRequest(request, success, error, handler, httpClient, context) {\r\n\r\n    return httpClient.request(request, function (response) {\r\n        try {\r\n            if (response.headers) {\r\n                normalizeHeaders(response.headers);\r\n            }\r\n\r\n            if (response.data === undefined && response.statusCode !== 204) {\r\n                handler.read(response, context);\r\n            }\r\n        } catch (err) {\r\n            if (err.request === undefined) {\r\n                err.request = request;\r\n            }\r\n            if (err.response === undefined) {\r\n                err.response = response;\r\n            }\r\n            error(err);\r\n            return;\r\n        }\r\n        // errors in success handler for sync requests result in error handler calls. So here we fix this. \r\n        try {\r\n            success(response.data, response);\r\n        } catch (err) {\r\n            err.bIsSuccessHandlerError = true;\r\n            throw err;\r\n        }\r\n    }, error);\r\n}\r\n\r\n/** Tests whether a value is a batch object in the library's internal representation.\r\n * @param value - Value to test.\r\n * @returns {Boolean} True is the value is a batch object; false otherwise.\r\n */\r\nfunction isBatch(value) {\r\n\r\n    return isComplex(value) && isArray(value.__batchRequests);\r\n}\r\n\r\n// Regular expression used for testing and parsing for a collection type.\r\nvar collectionTypeRE = /Collection\\((.*)\\)/;\r\n\r\n/** Tests whether a value is a collection value in the library's internal representation.\r\n * @param value - Value to test.\r\n * @param {String} typeName - Type name of the value. This is used to disambiguate from a collection property value.\r\n * @returns {Boolean} True is the value is a feed value; false otherwise.\r\n */\r\nfunction isCollection(value, typeName) {\r\n\r\n    var colData = value && value.results || value;\r\n    return !!colData &&\r\n        (isCollectionType(typeName)) ||\r\n        (!typeName && isArray(colData) && !isComplex(colData[0]));\r\n}\r\n\r\n/** Checks whether the specified type name is a collection type.\r\n * @param {String} typeName - Name of type to check.\r\n * @returns {Boolean} True if the type is the name of a collection type; false otherwise.\r\n */\r\nfunction isCollectionType(typeName) {\r\n    return collectionTypeRE.test(typeName);\r\n}\r\n\r\n/** Tests whether a value is a complex type value in the library's internal representation.\r\n * @param value - Value to test.\r\n * @returns {Boolean} True is the value is a complex type value; false otherwise.\r\n */\r\nfunction isComplex(value) {\r\n\r\n    return !!value &&\r\n        isObject(value) &&\r\n        !isArray(value) &&\r\n        !isDate(value);\r\n}\r\n\r\n/** Checks whether a Date object is DateTimeOffset value\r\n * @param {Date} value - Value to check\r\n * @returns {Boolean} true if the value is a DateTimeOffset, false otherwise.\r\n */\r\nfunction isDateTimeOffset(value) {\r\n    return (value.__edmType === \"Edm.DateTimeOffset\" || (!value.__edmType && value.__offset));\r\n}\r\n\r\n/** Tests whether a value is a deferred navigation property in the library's internal representation.\r\n * @param value - Value to test.\r\n * @returns {Boolean} True is the value is a deferred navigation property; false otherwise.\r\n */\r\nfunction isDeferred(value) {\r\n\r\n    if (!value && !isComplex(value)) {\r\n        return false;\r\n    }\r\n    var metadata = value.__metadata || {};\r\n    var deferred = value.__deferred || {};\r\n    return !metadata.type && !!deferred.uri;\r\n}\r\n\r\n/** Tests whether a value is an entry object in the library's internal representation.\r\n * @param value - Value to test.\r\n * @returns {Boolean} True is the value is an entry object; false otherwise.\r\n */\r\nfunction isEntry(value) {\r\n\r\n    return isComplex(value) && value.__metadata && \"uri\" in value.__metadata;\r\n}\r\n\r\n/** Tests whether a value is a feed value in the library's internal representation.\r\n * @param value - Value to test.\r\n * @param {String} typeName - Type name of the value. This is used to disambiguate from a collection property value.\r\n * @returns {Boolean} True is the value is a feed value; false otherwise.\r\n */\r\nfunction isFeed(value, typeName) {\r\n\r\n    var feedData = value && value.results || value;\r\n    return isArray(feedData) && (\r\n        (!isCollectionType(typeName)) &&\r\n        (isComplex(feedData[0]))\r\n    );\r\n}\r\n\r\n/** Checks whether the specified type name is a geography EDM type.\r\n * @param {String} typeName - Name of type to check.\r\n * @returns {Boolean} True if the type is a geography EDM type; false otherwise.\r\n */\r\nfunction isGeographyEdmType(typeName) {\r\n    //check with edm\r\n    return contains(geographyEdmTypes, typeName) ||\r\n        (typeName.indexOf('.') === -1 && contains(geographyTypes, typeName));\r\n        \r\n}\r\n\r\n/** Checks whether the specified type name is a geometry EDM type.\r\n * @param {String} typeName - Name of type to check.\r\n * @returns {Boolean} True if the type is a geometry EDM type; false otherwise.\r\n */\r\nfunction isGeometryEdmType(typeName) {\r\n    return contains(geometryEdmTypes, typeName) ||\r\n        (typeName.indexOf('.') === -1 && contains(geometryTypes, typeName));\r\n}\r\n\r\n\r\n\r\n/** Tests whether a value is a named stream value in the library's internal representation.\r\n * @param value - Value to test.\r\n * @returns {Boolean} True is the value is a named stream; false otherwise.\r\n */\r\nfunction isNamedStream(value) {\r\n\r\n    if (!value && !isComplex(value)) {\r\n        return false;\r\n    }\r\n    var metadata = value.__metadata;\r\n    var mediaResource = value.__mediaresource;\r\n    return !metadata && !!mediaResource && !!mediaResource.media_src;\r\n}\r\n\r\n/** Tests whether a value is a primitive type value in the library's internal representation.\r\n * @param value - Value to test.\r\n * @returns {Boolean} True is the value is a primitive type value.\r\n * Date objects are considered primitive types by the library.\r\n */\r\nfunction isPrimitive(value) {\r\n\r\n    return isDate(value) ||\r\n        typeof value === \"string\" ||\r\n        typeof value === \"number\" ||\r\n        typeof value === \"boolean\";\r\n}\r\n\r\n/** Checks whether the specified type name is a primitive EDM type.\r\n * @param {String} typeName - Name of type to check.\r\n * @returns {Boolean} True if the type is a primitive EDM type; false otherwise.\r\n */\r\nfunction isPrimitiveEdmType(typeName) {\r\n\r\n    return contains(primitiveEdmTypes, typeName);\r\n}\r\n\r\n/** Gets the kind of a navigation property value.\r\n * @param value - Value of the navigation property.\r\n * @param {Object} [propertyModel] - Object that describes the navigation property in an OData conceptual schema.\r\n * @returns {String} String value describing the kind of the navigation property; null if the kind cannot be determined.\r\n */\r\nfunction navigationPropertyKind(value, propertyModel) {\r\n\r\n    if (isDeferred(value)) {\r\n        return \"deferred\";\r\n    }\r\n    if (isEntry(value)) {\r\n        return \"entry\";\r\n    }\r\n    if (isFeed(value)) {\r\n        return \"feed\";\r\n    }\r\n    if (propertyModel && propertyModel.relationship) {\r\n        if (value === null || value === undefined || !isFeed(value)) {\r\n            return \"entry\";\r\n        }\r\n        return \"feed\";\r\n    }\r\n    return null;\r\n}\r\n\r\n/** Looks up a property by name.\r\n * @param {Array} properties - Array of property objects as per EDM metadata (may be null)\r\n * @param {String} name - Name to look for.\r\n * @returns {Object} The property object; null if not found.\r\n */\r\nfunction lookupProperty(properties, name) {\r\n\r\n    return find(properties, function (property) {\r\n        return property.name === name;\r\n    });\r\n}\r\n\r\n/** Looks up a type object by name.\r\n * @param {String} name - Name, possibly null or empty.\r\n * @param metadata - Metadata store; one of edmx, schema, or an array of any of them.\r\n * @param {String} kind - Kind of object to look for as per EDM metadata.\r\n * @returns An type description if the name is found; null otherwise\r\n */\r\nfunction lookupInMetadata(name, metadata, kind) {\r\n\r\n    return (name) ? forEachSchema(metadata, function (schema) {\r\n        return lookupInSchema(name, schema, kind);\r\n    }) : null;\r\n}\r\n\r\n/** Looks up a entity set by name.\r\n * @param {Array} entitySets - Array of entity set objects as per EDM metadata( may be null)\r\n * @param {String} name - Name to look for.\r\n * @returns {Object} The entity set object; null if not found.\r\n */\r\nfunction lookupEntitySet(entitySets, name) {\r\n\r\n    return find(entitySets, function (entitySet) {\r\n        return entitySet.name === name;\r\n    });\r\n}\r\n\r\n/** Looks up a entity set by name.\r\n * @param {Array} singletons - Array of entity set objects as per EDM metadata (may be null)\r\n * @param {String} name - Name to look for.\r\n * @returns {Object} The entity set object; null if not found.\r\n */\r\nfunction lookupSingleton(singletons, name) {\r\n\r\n    return find(singletons, function (singleton) {\r\n        return singleton.name === name;\r\n    });\r\n}\r\n\r\n/** Looks up a complex type object by name.\r\n * @param {String} name - Name, possibly null or empty.\r\n * @param metadata - Metadata store; one of edmx, schema, or an array of any of them.\r\n * @returns A complex type description if the name is found; null otherwise.\r\n */\r\nfunction lookupComplexType(name, metadata) {\r\n\r\n    return lookupInMetadata(name, metadata, \"complexType\");\r\n}\r\n\r\n/** Looks up an entity type object by name.\r\n * @param {String} name - Name, possibly null or empty.\r\n * @param metadata - Metadata store; one of edmx, schema, or an array of any of them.\r\n * @returns An entity type description if the name is found; null otherwise.\r\n */\r\nfunction lookupEntityType(name, metadata) {\r\n\r\n    return lookupInMetadata(name, metadata, \"entityType\");\r\n}\r\n\r\n\r\n/** Looks up an\r\n * @param metadata - Metadata store; one of edmx, schema, or an array of any of them.\r\n * @returns An entity container description if the name is found; null otherwise.\r\n */\r\nfunction lookupDefaultEntityContainer(metadata) {\r\n\r\n    return forEachSchema(metadata, function (schema) {\r\n        if (isObject(schema.entityContainer)) { \r\n            return schema.entityContainer;\r\n        }\r\n    });\r\n}\r\n\r\n/** Looks up an entity container object by name.\r\n * @param {String} name - Name, possibly null or empty.\r\n * @param metadata - Metadata store; one of edmx, schema, or an array of any of them.\r\n * @returns An entity container description if the name is found; null otherwise.\r\n */\r\nfunction lookupEntityContainer(name, metadata) {\r\n\r\n    return lookupInMetadata(name, metadata, \"entityContainer\");\r\n}\r\n\r\n/** Looks up a function import by name.\r\n * @param {Array} functionImports - Array of function import objects as per EDM metadata (May be null)\r\n * @param {String} name - Name to look for.\r\n * @returns {Object} The entity set object; null if not found.\r\n */\r\nfunction lookupFunctionImport(functionImports, name) {\r\n    return find(functionImports, function (functionImport) {\r\n        return functionImport.name === name;\r\n    });\r\n}\r\n\r\n/** Looks up the target entity type for a navigation property.\r\n * @param {Object} navigationProperty - \r\n * @param {Object} metadata - \r\n * @returns {String} The entity type name for the specified property, null if not found.\r\n */\r\nfunction lookupNavigationPropertyType(navigationProperty, metadata) {\r\n\r\n    var result = null;\r\n    if (navigationProperty) {\r\n        var rel = navigationProperty.relationship;\r\n        var association = forEachSchema(metadata, function (schema) {\r\n            // The name should be the namespace qualified name in 'ns'.'type' format.\r\n            var nameOnly = removeNamespace(schema.namespace, rel);\r\n            var associations = schema.association;\r\n            if (nameOnly && associations) {\r\n                var i, len;\r\n                for (i = 0, len = associations.length; i < len; i++) {\r\n                    if (associations[i].name === nameOnly) {\r\n                        return associations[i];\r\n                    }\r\n                }\r\n            }\r\n            return null;\r\n        });\r\n\r\n        if (association) {\r\n            var end = association.end[0];\r\n            if (end.role !== navigationProperty.toRole) {\r\n                end = association.end[1];\r\n                // For metadata to be valid, end.role === navigationProperty.toRole now.\r\n            }\r\n            result = end.type;\r\n        }\r\n    }\r\n    return result;\r\n}\r\n\r\n/** Looks up the target entityset name for a navigation property.\r\n * @param {Object} navigationProperty - \r\n * @param {Object} sourceEntitySetName -\r\n * @param {Object} metadata -\r\n * metadata\r\n * @returns {String} The entityset name for the specified property, null if not found.\r\n */\r\nfunction lookupNavigationPropertyEntitySet(navigationProperty, sourceEntitySetName, metadata) {\r\n\r\n    if (navigationProperty) {\r\n        var rel = navigationProperty.relationship;\r\n        var associationSet = forEachSchema(metadata, function (schema) {\r\n            var containers = schema.entityContainer;\r\n            for (var i = 0; i < containers.length; i++) {\r\n                var associationSets = containers[i].associationSet;\r\n                if (associationSets) {\r\n                    for (var j = 0; j < associationSets.length; j++) {\r\n                        if (associationSets[j].association == rel) {\r\n                            return associationSets[j];\r\n                        }\r\n                    }\r\n                }\r\n            }\r\n            return null;\r\n        });\r\n        if (associationSet && associationSet.end[0] && associationSet.end[1]) {\r\n            return (associationSet.end[0].entitySet == sourceEntitySetName) ? associationSet.end[1].entitySet : associationSet.end[0].entitySet;\r\n        }\r\n    }\r\n    return null;\r\n}\r\n\r\n/** Gets the entitySet info, container name and functionImports for an entitySet\r\n * @param {Object} entitySetName -\r\n * @param {Object} metadata - \r\n * @returns {Object} The info about the entitySet.\r\n */\r\nfunction getEntitySetInfo(entitySetName, metadata) {\r\n\r\n    var info = forEachSchema(metadata, function (schema) {\r\n        var container = schema.entityContainer;\r\n        var entitySets = container.entitySet;\r\n        if (entitySets) {\r\n            for (var j = 0; j < entitySets.length; j++) {\r\n                if (entitySets[j].name == entitySetName) {\r\n                    return { entitySet: entitySets[j], containerName: container.name, functionImport: container.functionImport };\r\n                }\r\n            }\r\n        }\r\n        return null;\r\n    });\r\n\r\n    return info;\r\n}\r\n\r\n/** Given an expected namespace prefix, removes it from a full name.\r\n * @param {String} ns - Expected namespace.\r\n * @param {String} fullName - Full name in 'ns'.'name' form.\r\n * @returns {String} The local name, null if it isn't found in the expected namespace.\r\n */\r\nfunction removeNamespace(ns, fullName) {\r\n\r\n    if (fullName.indexOf(ns) === 0 && fullName.charAt(ns.length) === \".\") {\r\n        return fullName.substr(ns.length + 1);\r\n    }\r\n\r\n    return null;\r\n}\r\n\r\n/** Looks up a schema object by name.\r\n * @param {String} name - Name (assigned).\r\n * @param schema - Schema object as per EDM metadata.\r\n * @param {String} kind - Kind of object to look for as per EDM metadata.\r\n * @returns An entity type description if the name is found; null otherwise.\r\n */\r\nfunction lookupInSchema(name, schema, kind) {\r\n\r\n    if (name && schema) {\r\n        // The name should be the namespace qualified name in 'ns'.'type' format.\r\n        var nameOnly = removeNamespace(schema.namespace, name);\r\n        if (nameOnly) {\r\n            return find(schema[kind], function (item) {\r\n                return item.name === nameOnly;\r\n            });\r\n        }\r\n    }\r\n    return null;\r\n}\r\n\r\n/** Compares to version strings and returns the higher one.\r\n * @param {String} left - Version string in the form \"major.minor.rev\"\r\n * @param {String} right - Version string in the form \"major.minor.rev\"\r\n * @returns {String} The higher version string.\r\n */\r\nfunction maxVersion(left, right) {\r\n\r\n    if (left === right) {\r\n        return left;\r\n    }\r\n\r\n    var leftParts = left.split(\".\");\r\n    var rightParts = right.split(\".\");\r\n\r\n    var len = (leftParts.length >= rightParts.length) ?\r\n        leftParts.length :\r\n        rightParts.length;\r\n\r\n    for (var i = 0; i < len; i++) {\r\n        var leftVersion = leftParts[i] && parseInt10(leftParts[i]);\r\n        var rightVersion = rightParts[i] && parseInt10(rightParts[i]);\r\n        if (leftVersion > rightVersion) {\r\n            return left;\r\n        }\r\n        if (leftVersion < rightVersion) {\r\n            return right;\r\n        }\r\n    }\r\n}\r\n\r\nvar normalHeaders = {\r\n    // Headers shared by request and response\r\n    \"content-type\": \"Content-Type\",\r\n    \"content-encoding\": \"Content-Encoding\",\r\n    \"content-length\": \"Content-Length\",\r\n    \"odata-version\": \"OData-Version\",\r\n    \r\n    // Headers used by request\r\n    \"accept\": \"Accept\",\r\n    \"accept-charset\": \"Accept-Charset\",\r\n    \"if-match\": \"If-Match\",\r\n    \"if-none-match\": \"If-None-Match\",\r\n    \"odata-isolation\": \"OData-Isolation\",\r\n    \"odata-maxversion\": \"OData-MaxVersion\",\r\n    \"prefer\": \"Prefer\",\r\n    \"content-id\": \"Content-ID\",\r\n    \"content-transfer-encoding\": \"Content-Transfer-Encoding\",\r\n    \r\n    // Headers used by response\r\n    \"etag\": \"ETag\",\r\n    \"location\": \"Location\",\r\n    \"odata-entityid\": \"OData-EntityId\",\r\n    \"preference-applied\": \"Preference-Applied\",\r\n    \"retry-after\": \"Retry-After\"\r\n};\r\n\r\n/** Normalizes headers so they can be found with consistent casing.\r\n * @param {Object} headers - Dictionary of name/value pairs.\r\n */\r\nfunction normalizeHeaders(headers) {\r\n\r\n    for (var name in headers) {\r\n        var lowerName = name.toLowerCase();\r\n        var normalName = normalHeaders[lowerName];\r\n        if (normalName && name !== normalName) {\r\n            var val = headers[name];\r\n            delete headers[name];\r\n            headers[normalName] = val;\r\n        }\r\n    }\r\n}\r\n\r\n/** Parses a string into a boolean value.\r\n * @param propertyValue - Value to parse.\r\n * @returns {Boolean} true if the property value is 'true'; false otherwise.\r\n */\r\nfunction parseBool(propertyValue) {\r\n\r\n    if (typeof propertyValue === \"boolean\") {\r\n        return propertyValue;\r\n    }\r\n\r\n    return typeof propertyValue === \"string\" && propertyValue.toLowerCase() === \"true\";\r\n}\r\n\r\n\r\n// The captured indices for this expression are:\r\n// 0     - complete input\r\n// 1,2,3 - year with optional minus sign, month, day\r\n// 4,5,6 - hours, minutes, seconds\r\n// 7     - optional milliseconds\r\n// 8     - everything else (presumably offset information)\r\nvar parseDateTimeRE = /^(-?\\d{4,})-(\\d{2})-(\\d{2})T(\\d{2}):(\\d{2})(?::(\\d{2}))?(?:\\.(\\d+))?(.*)$/;\r\n\r\n/** Parses a string into a DateTime value.\r\n * @param {String} value - Value to parse.\r\n * @param {Boolean} withOffset - Whether offset is expected.\r\n * @param {Boolean} nullOnError - return null instead of throwing an exception\r\n * @returns {Date} The parsed value.\r\n */\r\nfunction parseDateTimeMaybeOffset(value, withOffset, nullOnError) {\r\n\r\n    // We cannot parse this in cases of failure to match or if offset information is specified.\r\n    var parts = parseDateTimeRE.exec(value);\r\n    var offset = (parts) ? getCanonicalTimezone(parts[8]) : null;\r\n\r\n    if (!parts || (!withOffset && offset !== \"Z\")) {\r\n        if (nullOnError) {\r\n            return null;\r\n        }\r\n        throw { message: \"Invalid date/time value\" };\r\n    }\r\n\r\n    // Pre-parse years, account for year '0' being invalid in dateTime.\r\n    var year = parseInt10(parts[1]);\r\n    if (year <= 0) {\r\n        year++;\r\n    }\r\n\r\n    // Pre-parse optional milliseconds, fill in default. Fail if value is too precise.\r\n    var ms = parts[7];\r\n    var ns = 0;\r\n    if (!ms) {\r\n        ms = 0;\r\n    } else {\r\n        if (ms.length > 7) {\r\n            if (nullOnError) {\r\n                return null;\r\n            }\r\n            throw { message: \"Cannot parse date/time value to given precision.\" };\r\n        }\r\n\r\n        ns = formatNumberWidth(ms.substring(3), 4, true);\r\n        ms = formatNumberWidth(ms.substring(0, 3), 3, true);\r\n\r\n        ms = parseInt10(ms);\r\n        ns = parseInt10(ns);\r\n    }\r\n\r\n    // Pre-parse other time components and offset them if necessary.\r\n    var hours = parseInt10(parts[4]);\r\n    var minutes = parseInt10(parts[5]);\r\n    var seconds = parseInt10(parts[6]) || 0;\r\n    if (offset !== \"Z\") {\r\n        // The offset is reversed to get back the UTC date, which is\r\n        // what the API will eventually have.\r\n        var timezone = parseTimezone(offset);\r\n        var direction = -(timezone.d);\r\n        hours += timezone.h * direction;\r\n        minutes += timezone.m * direction;\r\n    }\r\n\r\n    // Set the date and time separately with setFullYear, so years 0-99 aren't biased like in Date.UTC.\r\n    var result = new Date();\r\n    result.setUTCFullYear(\r\n        year,                       // Year.\r\n        parseInt10(parts[2]) - 1,   // Month (zero-based for Date.UTC and setFullYear).\r\n        parseInt10(parts[3])        // Date.\r\n        );\r\n    result.setUTCHours(hours, minutes, seconds, ms);\r\n\r\n    if (isNaN(result.valueOf())) {\r\n        if (nullOnError) {\r\n            return null;\r\n        }\r\n        throw { message: \"Invalid date/time value\" };\r\n    }\r\n\r\n    if (withOffset) {\r\n        result.__edmType = \"Edm.DateTimeOffset\";\r\n        result.__offset = offset;\r\n    }\r\n\r\n    if (ns) {\r\n        result.__ns = ns;\r\n    }\r\n\r\n    return result;\r\n}\r\n\r\n/** Parses a string into a Date object.\r\n * @param {String} propertyValue - Value to parse.\r\n * @param {Boolean} nullOnError - return null instead of throwing an exception\r\n * @returns {Date} The parsed with year, month, day set, time values are set to 0\r\n */\r\nfunction parseDate(propertyValue, nullOnError) {\r\n    var parts = propertyValue.split('-');\r\n\r\n    if (parts.length != 3 && nullOnError) {\r\n        return null;\r\n    }\r\n    return new Date(\r\n        parseInt10(parts[0]),       // Year.\r\n        parseInt10(parts[1]) - 1,   // Month (zero-based for Date.UTC and setFullYear).\r\n        parseInt10(parts[2],\r\n        0,0,0,0)        // Date.\r\n        );\r\n\r\n}\r\n\r\nvar parseTimeOfDayRE = /^(\\d+):(\\d+)(:(\\d+)(.(\\d+))?)?$/;\r\n\r\n/**Parses a time into a Date object.\r\n * @param propertyValue\r\n * @param {Boolean} nullOnError - return null instead of throwing an exception\r\n * @returns {{h: Number, m: Number, s: Number, ms: Number}}\r\n */\r\nfunction parseTimeOfDay(propertyValue, nullOnError) {\r\n    var parts = parseTimeOfDayRE.exec(propertyValue);\r\n\r\n\r\n    return {\r\n        'h' :parseInt10(parts[1]),\r\n        'm' :parseInt10(parts[2]),\r\n        's' :parseInt10(parts[4]),\r\n        'ms' :parseInt10(parts[6])\r\n     };\r\n}\r\n\r\n/** Parses a string into a DateTimeOffset value.\r\n * @param {String} propertyValue - Value to parse.\r\n * @param {Boolean} nullOnError - return null instead of throwing an exception\r\n * @returns {Date} The parsed value.\r\n * The resulting object is annotated with an __edmType property and\r\n * an __offset property reflecting the original intended offset of\r\n * the value. The time is adjusted for UTC time, as the current\r\n * timezone-aware Date APIs will only work with the local timezone.\r\n */\r\nfunction parseDateTimeOffset(propertyValue, nullOnError) {\r\n    \r\n\r\n    return parseDateTimeMaybeOffset(propertyValue, true, nullOnError);\r\n}\r\n\r\n// The captured indices for this expression are:\r\n// 0       - complete input\r\n// 1       - direction\r\n// 2,3,4   - years, months, days\r\n// 5,6,7,8 - hours, minutes, seconds, miliseconds\r\n\r\nvar parseTimeRE = /^([+-])?P(?:(\\d+)Y)?(?:(\\d+)M)?(?:(\\d+)D)?(?:T(?:(\\d+)H)?(?:(\\d+)M)?(?:(\\d+)(?:\\.(\\d+))?S)?)?/;\r\n\r\nfunction isEdmDurationValue(value) {\r\n    parseTimeRE.test(value);\r\n}\r\n\r\n/** Parses a string in xsd:duration format.\r\n * @param {String} duration - Duration value.\r\n\r\n * This method will throw an exception if the input string has a year or a month component.\r\n\r\n * @returns {Object} Object representing the time\r\n */\r\nfunction parseDuration(duration) {\r\n\r\n    var parts = parseTimeRE.exec(duration);\r\n\r\n    if (parts === null) {\r\n        throw { message: \"Invalid duration value.\" };\r\n    }\r\n\r\n    var years = parts[2] || \"0\";\r\n    var months = parts[3] || \"0\";\r\n    var days = parseInt10(parts[4] || 0);\r\n    var hours = parseInt10(parts[5] || 0);\r\n    var minutes = parseInt10(parts[6] || 0);\r\n    var seconds = parseFloat(parts[7] || 0);\r\n\r\n    if (years !== \"0\" || months !== \"0\") {\r\n        throw { message: \"Unsupported duration value.\" };\r\n    }\r\n\r\n    var ms = parts[8];\r\n    var ns = 0;\r\n    if (!ms) {\r\n        ms = 0;\r\n    } else {\r\n        if (ms.length > 7) {\r\n            throw { message: \"Cannot parse duration value to given precision.\" };\r\n        }\r\n\r\n        ns = formatNumberWidth(ms.substring(3), 4, true);\r\n        ms = formatNumberWidth(ms.substring(0, 3), 3, true);\r\n\r\n        ms = parseInt10(ms);\r\n        ns = parseInt10(ns);\r\n    }\r\n\r\n    ms += seconds * 1000 + minutes * 60000 + hours * 3600000 + days * 86400000;\r\n\r\n    if (parts[1] === \"-\") {\r\n        ms = -ms;\r\n    }\r\n\r\n    var result = { ms: ms, __edmType: \"Edm.Time\" };\r\n\r\n    if (ns) {\r\n        result.ns = ns;\r\n    }\r\n    return result;\r\n}\r\n\r\n/** Parses a timezone description in (+|-)nn:nn format.\r\n * @param {String} timezone - Timezone offset.\r\n * @returns {Object} An object with a (d)irection property of 1 for + and -1 for -, offset (h)ours and offset (m)inutes.\r\n */\r\nfunction parseTimezone(timezone) {\r\n\r\n    var direction = timezone.substring(0, 1);\r\n    direction = (direction === \"+\") ? 1 : -1;\r\n\r\n    var offsetHours = parseInt10(timezone.substring(1));\r\n    var offsetMinutes = parseInt10(timezone.substring(timezone.indexOf(\":\") + 1));\r\n    return { d: direction, h: offsetHours, m: offsetMinutes };\r\n}\r\n\r\n/** Prepares a request object so that it can be sent through the network.\r\n* @param request - Object that represents the request to be sent.\r\n* @param handler - Handler for data serialization\r\n* @param context - Context used for preparing the request\r\n*/\r\nfunction prepareRequest(request, handler, context) {\r\n\r\n    // Default to GET if no method has been specified.\r\n    if (!request.method) {\r\n        request.method = \"GET\";\r\n    }\r\n\r\n    if (!request.headers) {\r\n        request.headers = {};\r\n    } else {\r\n        normalizeHeaders(request.headers);\r\n    }\r\n\r\n    if (request.headers.Accept === undefined) {\r\n        request.headers.Accept = handler.accept;\r\n    }\r\n\r\n    if (assigned(request.data) && request.body === undefined) {\r\n        handler.write(request, context);\r\n    }\r\n\r\n    if (!assigned(request.headers[\"OData-MaxVersion\"])) {\r\n        request.headers[\"OData-MaxVersion\"] = handler.maxDataServiceVersion || \"4.0\";\r\n    }\r\n\r\n    if (request.async === undefined) {\r\n        request.async = true;\r\n    }\r\n\r\n}\r\n\r\n/** Traverses a tree of objects invoking callback for every value.\r\n * @param {Object} item - Object or array to traverse.\r\n * @param {Object} owner - Pass through each callback\r\n * @param {Function} callback - Callback function with key and value, similar to JSON.parse reviver.\r\n * @returns {Object} The object with traversed properties.\r\n Unlike the JSON reviver, this won't delete null members.\r\n*/\r\nfunction traverseInternal(item, owner, callback) {\r\n\r\n    if (item && typeof item === \"object\") {\r\n        for (var name in item) {\r\n            var value = item[name];\r\n            var result = traverseInternal(value, name, callback);\r\n            result = callback(name, result, owner);\r\n            if (result !== value) {\r\n                if (value === undefined) {\r\n                    delete item[name];\r\n                } else {\r\n                    item[name] = result;\r\n                }\r\n            }\r\n        }\r\n    }\r\n\r\n    return item;\r\n}\r\n\r\n/** Traverses a tree of objects invoking callback for every value.\r\n * @param {Object} item - Object or array to traverse.\r\n * @param {Function} callback - Callback function with key and value, similar to JSON.parse reviver.\r\n * @returns {Object} The traversed object.\r\n * Unlike the JSON reviver, this won't delete null members.\r\n*/\r\nfunction traverse(item, callback) {\r\n\r\n    return callback(\"\", traverseInternal(item, \"\", callback));\r\n}\r\n\r\nexports.dataItemTypeName = dataItemTypeName;\r\nexports.EDM_BINARY = EDM_BINARY;\r\nexports.EDM_BOOLEAN = EDM_BOOLEAN;\r\nexports.EDM_BYTE = EDM_BYTE;\r\nexports.EDM_DATE = EDM_DATE;\r\nexports.EDM_DATETIMEOFFSET = EDM_DATETIMEOFFSET;\r\nexports.EDM_DURATION = EDM_DURATION;\r\nexports.EDM_DECIMAL = EDM_DECIMAL;\r\nexports.EDM_DOUBLE = EDM_DOUBLE;\r\nexports.EDM_GEOGRAPHY = EDM_GEOGRAPHY;\r\nexports.EDM_GEOGRAPHY_POINT = EDM_GEOGRAPHY_POINT;\r\nexports.EDM_GEOGRAPHY_LINESTRING = EDM_GEOGRAPHY_LINESTRING;\r\nexports.EDM_GEOGRAPHY_POLYGON = EDM_GEOGRAPHY_POLYGON;\r\nexports.EDM_GEOGRAPHY_COLLECTION = EDM_GEOGRAPHY_COLLECTION;\r\nexports.EDM_GEOGRAPHY_MULTIPOLYGON = EDM_GEOGRAPHY_MULTIPOLYGON;\r\nexports.EDM_GEOGRAPHY_MULTILINESTRING = EDM_GEOGRAPHY_MULTILINESTRING;\r\nexports.EDM_GEOGRAPHY_MULTIPOINT = EDM_GEOGRAPHY_MULTIPOINT;\r\nexports.EDM_GEOMETRY = EDM_GEOMETRY;\r\nexports.EDM_GEOMETRY_POINT = EDM_GEOMETRY_POINT;\r\nexports.EDM_GEOMETRY_LINESTRING = EDM_GEOMETRY_LINESTRING;\r\nexports.EDM_GEOMETRY_POLYGON = EDM_GEOMETRY_POLYGON;\r\nexports.EDM_GEOMETRY_COLLECTION = EDM_GEOMETRY_COLLECTION;\r\nexports.EDM_GEOMETRY_MULTIPOLYGON = EDM_GEOMETRY_MULTIPOLYGON;\r\nexports.EDM_GEOMETRY_MULTILINESTRING = EDM_GEOMETRY_MULTILINESTRING;\r\nexports.EDM_GEOMETRY_MULTIPOINT = EDM_GEOMETRY_MULTIPOINT;\r\nexports.EDM_GUID = EDM_GUID;\r\nexports.EDM_INT16 = EDM_INT16;\r\nexports.EDM_INT32 = EDM_INT32;\r\nexports.EDM_INT64 = EDM_INT64;\r\nexports.EDM_SBYTE = EDM_SBYTE;\r\nexports.EDM_SINGLE = EDM_SINGLE;\r\nexports.EDM_STRING = EDM_STRING;\r\nexports.EDM_TIMEOFDAY = EDM_TIMEOFDAY;\r\nexports.GEOJSON_POINT = GEOJSON_POINT;\r\nexports.GEOJSON_LINESTRING = GEOJSON_LINESTRING;\r\nexports.GEOJSON_POLYGON = GEOJSON_POLYGON;\r\nexports.GEOJSON_MULTIPOINT = GEOJSON_MULTIPOINT;\r\nexports.GEOJSON_MULTILINESTRING = GEOJSON_MULTILINESTRING;\r\nexports.GEOJSON_MULTIPOLYGON = GEOJSON_MULTIPOLYGON;\r\nexports.GEOJSON_GEOMETRYCOLLECTION = GEOJSON_GEOMETRYCOLLECTION;\r\nexports.forEachSchema = forEachSchema;\r\nexports.formatDateTimeOffset = formatDateTimeOffset;\r\nexports.formatDateTimeOffsetJSON = formatDateTimeOffsetJSON;\r\nexports.formatDuration = formatDuration;\r\nexports.formatNumberWidth = formatNumberWidth;\r\nexports.getCanonicalTimezone = getCanonicalTimezone;\r\nexports.getCollectionType = getCollectionType;\r\nexports.invokeRequest = invokeRequest;\r\nexports.isBatch = isBatch;\r\nexports.isCollection = isCollection;\r\nexports.isCollectionType = isCollectionType;\r\nexports.isComplex = isComplex;\r\nexports.isDateTimeOffset = isDateTimeOffset;\r\nexports.isDeferred = isDeferred;\r\nexports.isEntry = isEntry;\r\nexports.isFeed = isFeed;\r\nexports.isGeographyEdmType = isGeographyEdmType;\r\nexports.isGeometryEdmType = isGeometryEdmType;\r\nexports.isNamedStream = isNamedStream;\r\nexports.isPrimitive = isPrimitive;\r\nexports.isPrimitiveEdmType = isPrimitiveEdmType;\r\nexports.lookupComplexType = lookupComplexType;\r\nexports.lookupDefaultEntityContainer = lookupDefaultEntityContainer;\r\nexports.lookupEntityContainer = lookupEntityContainer;\r\nexports.lookupEntitySet = lookupEntitySet;\r\nexports.lookupSingleton = lookupSingleton;\r\nexports.lookupEntityType = lookupEntityType;\r\nexports.lookupFunctionImport = lookupFunctionImport;\r\nexports.lookupNavigationPropertyType = lookupNavigationPropertyType;\r\nexports.lookupNavigationPropertyEntitySet = lookupNavigationPropertyEntitySet;\r\nexports.lookupInSchema = lookupInSchema;\r\nexports.lookupProperty = lookupProperty;\r\nexports.lookupInMetadata = lookupInMetadata;\r\nexports.getEntitySetInfo = getEntitySetInfo;\r\nexports.maxVersion = maxVersion;\r\nexports.navigationPropertyKind = navigationPropertyKind;\r\nexports.normalizeHeaders = normalizeHeaders;\r\nexports.parseBool = parseBool;\r\n\r\n\r\nexports.parseDate = parseDate;\r\nexports.parseDateTimeOffset = parseDateTimeOffset;\r\nexports.parseDuration = parseDuration;\r\nexports.parseTimeOfDay = parseTimeOfDay;\r\n\r\nexports.parseInt10 = parseInt10;\r\nexports.prepareRequest = prepareRequest;\r\nexports.removeNamespace = removeNamespace;\r\nexports.traverse = traverse;\r\n\r\n\r\n}, \"store\" : function(exports, module, require) {\r\n//'use strict';\r\n\r\n /** @module store */\r\n\r\n\r\n\r\n\r\n\r\nexports.defaultStoreMechanism = \"best\";\r\n\r\n/** Creates a new store object.\r\n * @param {String} name - Store name.\r\n * @param {String} [mechanism] - \r\n * @returns {Object} Store object.\r\n*/\r\nexports.createStore = function (name, mechanism) {\r\n\r\n\r\n    if (!mechanism) {\r\n        mechanism = exports.defaultStoreMechanism;\r\n    }\r\n\r\n    if (mechanism === \"best\") {\r\n        mechanism = (DomStore.isSupported()) ? \"dom\" : \"memory\";\r\n    }\r\n\r\n    var factory = mechanisms[mechanism];\r\n    if (factory) {\r\n        return factory.create(name);\r\n    }\r\n\r\n    throw { message: \"Failed to create store\", name: name, mechanism: mechanism };\r\n};\r\n\r\nexports.DomStore       = DomStore       = require('./store/dom.js');\r\nexports.IndexedDBStore = IndexedDBStore = require('./store/indexeddb.js');\r\nexports.MemoryStore    = MemoryStore    = require('./store/memory.js');\r\n\r\nvar mechanisms = {\r\n    indexeddb: IndexedDBStore,\r\n    dom: DomStore,\r\n    memory: MemoryStore\r\n};\r\n\r\nexports.mechanisms = mechanisms;\r\n\r\n\r\n\r\n\r\n}, \"dom\" : function(exports, module, require) {\r\n'use strict';\r\n\r\n/** @module store/dom */\r\n\r\n\r\n\r\nvar utils = require('./../utils.js');\r\n\r\n// Imports.\r\nvar throwErrorCallback = utils.throwErrorCallback;\r\nvar delay = utils.delay;\r\n\r\nvar localStorage = null;\r\n\r\n/** This method is used to override the Date.toJSON method and is called only by\r\n * JSON.stringify.  It should never be called directly.\r\n * @summary Converts a Date object into an object representation friendly to JSON serialization.\r\n * @returns {Object} Object that represents the Date.\r\n */\r\nfunction domStoreDateToJSON() {\r\n    var newValue = { v: this.valueOf(), t: \"[object Date]\" };\r\n    // Date objects might have extra properties on them so we save them.\r\n    for (var name in this) {\r\n        newValue[name] = this[name];\r\n    }\r\n    return newValue;\r\n}\r\n\r\n/** This method is used during JSON parsing and invoked only by the reviver function.\r\n * It should never be called directly.\r\n * @summary JSON reviver function for converting an object representing a Date in a JSON stream to a Date object\r\n * @param value _\r\n * @param value - Object to convert.\r\n * @returns {Date} Date object.\r\n */\r\nfunction domStoreJSONToDate(_, value) {\r\n    if (value && value.t === \"[object Date]\") {\r\n        var newValue = new Date(value.v);\r\n        for (var name in value) {\r\n            if (name !== \"t\" && name !== \"v\") {\r\n                newValue[name] = value[name];\r\n            }\r\n        }\r\n        value = newValue;\r\n    }\r\n    return value;\r\n}\r\n\r\n/** Qualifies the key with the name of the store.\r\n * @param {Object} store - Store object whose name will be used for qualifying the key.\r\n * @param {String} key - Key string.\r\n * @returns {String} Fully qualified key string.\r\n */\r\nfunction qualifyDomStoreKey(store, key) {\r\n    return store.name + \"#!#\" + key;\r\n}\r\n\r\n/** Gets the key part of a fully qualified key string.\r\n * @param {Object} store - Store object whose name will be used for qualifying the key.\r\n * @param {String} key - Fully qualified key string.\r\n * @returns {String} Key part string\r\n */\r\nfunction unqualifyDomStoreKey(store, key) {\r\n    return key.replace(store.name + \"#!#\", \"\");\r\n}\r\n\r\n/** Constructor for store objects that use DOM storage as the underlying mechanism.\r\n * @class DomStore\r\n * @constructor\r\n * @param {String} name - Store name.\r\n */\r\nfunction DomStore(name) {\r\n    this.name = name;\r\n}\r\n\r\n/** Creates a store object that uses DOM Storage as its underlying mechanism.\r\n * @method module:store/dom~DomStore.create\r\n * @param {String} name - Store name.\r\n * @returns {Object} Store object.\r\n */\r\nDomStore.create = function (name) {\r\n\r\n    if (DomStore.isSupported()) {\r\n        localStorage = localStorage || window.localStorage;\r\n        return new DomStore(name);\r\n    }\r\n\r\n    throw { message: \"Web Storage not supported by the browser\" };\r\n};\r\n\r\n/** Checks whether the underlying mechanism for this kind of store objects is supported by the browser.\r\n * @method DomStore.isSupported\r\n * @returns {Boolean} - True if the mechanism is supported by the browser; otherwise false.\r\n*/\r\nDomStore.isSupported = function () {\r\n    return !!window.localStorage;\r\n};\r\n\r\n/** Adds a new value identified by a key to the store.\r\n * @method module:store/dom~DomStore#add\r\n * @param {String} key - Key string.\r\n * @param value - Value that is going to be added to the store.\r\n * @param {Function} success - Callback for a successful add operation.\r\n * @param {Function} [error] - Callback for handling errors. If not specified then store.defaultError is invoked.\r\n * This method errors out if the store already contains the specified key.\r\n */\r\nDomStore.prototype.add = function (key, value, success, error) {\r\n    error = error || this.defaultError;\r\n    var store = this;\r\n    this.contains(key, function (contained) {\r\n        if (!contained) {\r\n            store.addOrUpdate(key, value, success, error);\r\n        } else {\r\n            delay(error, { message: \"key already exists\", key: key });\r\n        }\r\n    }, error);\r\n};\r\n\r\n/** This method will overwrite the key's current value if it already exists in the store; otherwise it simply adds the new key and value.\r\n * @summary Adds or updates a value identified by a key to the store.\r\n * @method module:store/dom~DomStore#addOrUpdate\r\n * @param {String} key - Key string.\r\n * @param value - Value that is going to be added or updated to the store.\r\n * @param {Function} success - Callback for a successful add or update operation.\r\n * @param {Function} [error] - Callback for handling errors. If not specified then store.defaultError is invoked.\r\n */\r\nDomStore.prototype.addOrUpdate = function (key, value, success, error) {\r\n    error = error || this.defaultError;\r\n\r\n    if (key instanceof Array) {\r\n        error({ message: \"Array of keys not supported\" });\r\n    } else {\r\n        var fullKey = qualifyDomStoreKey(this, key);\r\n        var oldDateToJSON = Date.prototype.toJSON;\r\n        try {\r\n            var storedValue = value;\r\n            if (storedValue !== undefined) {\r\n                // Dehydrate using json\r\n                Date.prototype.toJSON = domStoreDateToJSON;\r\n                storedValue = window.JSON.stringify(value);\r\n            }\r\n            // Save the json string.\r\n            localStorage.setItem(fullKey, storedValue);\r\n            delay(success, key, value);\r\n        }\r\n        catch (e) {\r\n            if (e.code === 22 || e.number === 0x8007000E) {\r\n                delay(error, { name: \"QUOTA_EXCEEDED_ERR\", error: e });\r\n            } else {\r\n                delay(error, e);\r\n            }\r\n        }\r\n        finally {\r\n            Date.prototype.toJSON = oldDateToJSON;\r\n        }\r\n    }\r\n};\r\n\r\n/** In case of an error, this method will not restore any keys that might have been deleted at that point.\r\n * @summary Removes all the data associated with this store object.\r\n * @method module:store/dom~DomStore#clear\r\n * @param {Function} success - Callback for a successful clear operation.\r\n * @param {Function} [error] - Callback for handling errors. If not specified then store.defaultError is invoked.\r\n */\r\nDomStore.prototype.clear = function (success, error) {\r\n\r\n    error = error || this.defaultError;\r\n    try {\r\n        var i = 0, len = localStorage.length;\r\n        while (len > 0 && i < len) {\r\n            var fullKey = localStorage.key(i);\r\n            var key = unqualifyDomStoreKey(this, fullKey);\r\n            if (fullKey !== key) {\r\n                localStorage.removeItem(fullKey);\r\n                len = localStorage.length;\r\n            } else {\r\n                i++;\r\n            }\r\n        }\r\n        delay(success);\r\n    }\r\n    catch (e) {\r\n        delay(error, e);\r\n    }\r\n};\r\n\r\n/** This function does nothing in DomStore as it does not have a connection model\r\n * @method module:store/dom~DomStore#close\r\n */\r\nDomStore.prototype.close = function () {\r\n};\r\n\r\n/** Checks whether a key exists in the store.\r\n * @method module:store/dom~DomStore#contains\r\n * @param {String} key - Key string.\r\n * @param {Function} success - Callback indicating whether the store contains the key or not.\r\n * @param {Function} [error] - Callback for handling errors. If not specified then store.defaultError is invoked.\r\n*/\r\nDomStore.prototype.contains = function (key, success, error) {\r\n    error = error || this.defaultError;\r\n    try {\r\n        var fullKey = qualifyDomStoreKey(this, key);\r\n        var value = localStorage.getItem(fullKey);\r\n        delay(success, value !== null);\r\n    } catch (e) {\r\n        delay(error, e);\r\n    }\r\n};\r\n\r\nDomStore.prototype.defaultError = throwErrorCallback;\r\n\r\n/** Gets all the keys that exist in the store.\r\n * @method module:store/dom~DomStore#getAllKeys\r\n * @param {Function} success - Callback for a successful get operation.\r\n * @param {Function} [error] - Callback for handling errors. If not specified then store.defaultError is invoked.\r\n */\r\nDomStore.prototype.getAllKeys = function (success, error) {\r\n\r\n    error = error || this.defaultError;\r\n\r\n    var results = [];\r\n    var i, len;\r\n\r\n    try {\r\n        for (i = 0, len = localStorage.length; i < len; i++) {\r\n            var fullKey = localStorage.key(i);\r\n            var key = unqualifyDomStoreKey(this, fullKey);\r\n            if (fullKey !== key) {\r\n                results.push(key);\r\n            }\r\n        }\r\n        delay(success, results);\r\n    }\r\n    catch (e) {\r\n        delay(error, e);\r\n    }\r\n};\r\n\r\n/** Identifies the underlying mechanism used by the store.*/\r\nDomStore.prototype.mechanism = \"dom\";\r\n\r\n/** Reads the value associated to a key in the store.\r\n * @method module:store/dom~DomStore#read\r\n * @param {String} key - Key string.\r\n * @param {Function} success - Callback for a successful reads operation.\r\n * @param {Function} [error] - Callback for handling errors. If not specified then store.defaultError is invoked.\r\n */\r\nDomStore.prototype.read = function (key, success, error) {\r\n\r\n    error = error || this.defaultError;\r\n\r\n    if (key instanceof Array) {\r\n        error({ message: \"Array of keys not supported\" });\r\n    } else {\r\n        try {\r\n            var fullKey = qualifyDomStoreKey(this, key);\r\n            var value = localStorage.getItem(fullKey);\r\n            if (value !== null && value !== \"undefined\") {\r\n                // Hydrate using json\r\n                value = window.JSON.parse(value, domStoreJSONToDate);\r\n            }\r\n            else {\r\n                value = undefined;\r\n            }\r\n            delay(success, key, value);\r\n        } catch (e) {\r\n            delay(error, e);\r\n        }\r\n    }\r\n};\r\n\r\n/** Removes a key and its value from the store.\r\n * @method module:store/dom~DomStore#remove\r\n * @param {String} key - Key string.\r\n * @param {Function} success - Callback for a successful remove operation.\r\n * @param {Function} [error] - Callback for handling errors. If not specified then store.defaultError is invoked.\r\n */\r\nDomStore.prototype.remove = function (key, success, error) {\r\n    error = error || this.defaultError;\r\n\r\n    if (key instanceof Array) {\r\n        error({ message: \"Batches not supported\" });\r\n    } else {\r\n        try {\r\n            var fullKey = qualifyDomStoreKey(this, key);\r\n            localStorage.removeItem(fullKey);\r\n            delay(success);\r\n        } catch (e) {\r\n            delay(error, e);\r\n        }\r\n    }\r\n};\r\n\r\n/** Updates the value associated to a key in the store.\r\n * @method module:store/dom~DomStore#update\r\n * @param {String} key - Key string.\r\n * @param value - New value.\r\n * @param {Function} success - Callback for a successful update operation.\r\n * @param {Function} [error] - Callback for handling errors. If not specified then store.defaultError is invoked\r\n * This method errors out if the specified key is not found in the store.\r\n */\r\nDomStore.prototype.update = function (key, value, success, error) {\r\n    error = error || this.defaultError;\r\n    var store = this;\r\n    this.contains(key, function (contained) {\r\n        if (contained) {\r\n            store.addOrUpdate(key, value, success, error);\r\n        } else {\r\n            delay(error, { message: \"key not found\", key: key });\r\n        }\r\n    }, error);\r\n};\r\n\r\nmodule.exports = DomStore;}, \"indexeddb\" : function(exports, module, require) {\r\n'use strict';\r\n\r\n/** @module store/indexeddb */\r\nvar utils = require('./../utils.js');\r\n\r\n// Imports.\r\nvar throwErrorCallback = utils.throwErrorCallback;\r\nvar delay = utils.delay;\r\n\r\n\r\nvar indexedDB = utils.inBrowser() ? window.mozIndexedDB || window.webkitIndexedDB || window.msIndexedDB || window.indexedDB : undefined;\r\nvar IDBKeyRange = utils.inBrowser() ? window.IDBKeyRange || window.webkitIDBKeyRange : undefined;\r\nvar IDBTransaction = utils.inBrowser() ? window.IDBTransaction || window.webkitIDBTransaction || {} : {} ;\r\n\r\nvar IDBT_READ_ONLY = IDBTransaction.READ_ONLY || \"readonly\";\r\nvar IDBT_READ_WRITE = IDBTransaction.READ_WRITE || \"readwrite\";\r\n\r\n/** Returns either a specific error handler or the default error handler\r\n * @param {Function} error - The specific error handler\r\n * @param {Function} defaultError - The default error handler\r\n * @returns {Function} The error callback\r\n */\r\nfunction getError(error, defaultError) {\r\n\r\n    return function (e) {\r\n        var errorFunc = error || defaultError;\r\n        if (!errorFunc) {\r\n            return;\r\n        }\r\n\r\n        // Old api quota exceeded error support.\r\n        if (Object.prototype.toString.call(e) === \"[object IDBDatabaseException]\") {\r\n            if (e.code === 11 /* IndexedDb disk quota exceeded */) {\r\n                errorFunc({ name: \"QuotaExceededError\", error: e });\r\n                return;\r\n            }\r\n            errorFunc(e);\r\n            return;\r\n        }\r\n\r\n        var errName;\r\n        try {\r\n            var errObj = e.target.error || e;\r\n            errName = errObj.name;\r\n        } catch (ex) {\r\n            errName = (e.type === \"blocked\") ? \"IndexedDBBlocked\" : \"UnknownError\";\r\n        }\r\n        errorFunc({ name: errName, error: e });\r\n    };\r\n}\r\n\r\n/** Opens the store object's indexed db database.\r\n * @param {IndexedDBStore} store - The store object\r\n * @param {Function} success - The success callback\r\n * @param {Function} error - The error callback\r\n */\r\nfunction openStoreDb(store, success, error) {\r\n\r\n    var storeName = store.name;\r\n    var dbName = \"_odatajs_\" + storeName;\r\n\r\n    var request = indexedDB.open(dbName);\r\n    request.onblocked = error;\r\n    request.onerror = error;\r\n\r\n    request.onupgradeneeded = function () {\r\n        var db = request.result;\r\n        if (!db.objectStoreNames.contains(storeName)) {\r\n            db.createObjectStore(storeName);\r\n        }\r\n    };\r\n\r\n    request.onsuccess = function (event) {\r\n        var db = request.result;\r\n        if (!db.objectStoreNames.contains(storeName)) {\r\n            // Should we use the old style api to define the database schema?\r\n            if (\"setVersion\" in db) {\r\n                var versionRequest = db.setVersion(\"1.0\");\r\n                versionRequest.onsuccess = function () {\r\n                    var transaction = versionRequest.transaction;\r\n                    transaction.oncomplete = function () {\r\n                        success(db);\r\n                    };\r\n                    db.createObjectStore(storeName, null, false);\r\n                };\r\n                versionRequest.onerror = error;\r\n                versionRequest.onblocked = error;\r\n                return;\r\n            }\r\n\r\n            // The database doesn't have the expected store.\r\n            // Fabricate an error object for the event for the schema mismatch\r\n            // and error out.\r\n            event.target.error = { name: \"DBSchemaMismatch\" };\r\n            error(event);\r\n            return;\r\n        }\r\n\r\n        db.onversionchange = function(event) {\r\n            event.target.close();\r\n        };\r\n        success(db);\r\n    };\r\n}\r\n\r\n/** Opens a new transaction to the store\r\n * @param {IndexedDBStore} store - The store object\r\n * @param {Integer} mode - The read/write mode of the transaction (constants from IDBTransaction)\r\n * @param {Function} success - The success callback\r\n * @param {Function} error - The error callback\r\n */\r\nfunction openTransaction(store, mode, success, error) {\r\n\r\n    var storeName = store.name;\r\n    var storeDb = store.db;\r\n    var errorCallback = getError(error, store.defaultError);\r\n\r\n    if (storeDb) {\r\n        success(storeDb.transaction(storeName, mode));\r\n        return;\r\n    }\r\n\r\n    openStoreDb(store, function (db) {\r\n        store.db = db;\r\n        success(db.transaction(storeName, mode));\r\n    }, errorCallback);\r\n}\r\n\r\n/** Creates a new IndexedDBStore.\r\n * @class IndexedDBStore\r\n * @constructor\r\n * @param {String} name - The name of the store.\r\n * @returns {Object} The new IndexedDBStore.\r\n */\r\nfunction IndexedDBStore(name) {\r\n    this.name = name;\r\n}\r\n\r\n/** Creates a new IndexedDBStore.\r\n * @method module:store/indexeddb~IndexedDBStore.create\r\n * @param {String} name - The name of the store.\r\n * @returns {Object} The new IndexedDBStore.\r\n */\r\nIndexedDBStore.create = function (name) {\r\n    if (IndexedDBStore.isSupported()) {\r\n        return new IndexedDBStore(name);\r\n    }\r\n\r\n    throw { message: \"IndexedDB is not supported on this browser\" };\r\n};\r\n\r\n/** Returns whether IndexedDB is supported.\r\n * @method module:store/indexeddb~IndexedDBStore.isSupported\r\n * @returns {Boolean} True if IndexedDB is supported, false otherwise.\r\n */\r\nIndexedDBStore.isSupported = function () {\r\n    return !!indexedDB;\r\n};\r\n\r\n/** Adds a key/value pair to the store\r\n * @method module:store/indexeddb~IndexedDBStore#add\r\n * @param {String} key - The key\r\n * @param {Object} value - The value\r\n * @param {Function} success - The success callback\r\n * @param {Function} error - The error callback\r\n*/\r\nIndexedDBStore.prototype.add = function (key, value, success, error) {\r\n    var name = this.name;\r\n    var defaultError = this.defaultError;\r\n    var keys = [];\r\n    var values = [];\r\n\r\n    if (key instanceof Array) {\r\n        keys = key;\r\n        values = value;\r\n    } else {\r\n        keys = [key];\r\n        values = [value];\r\n    }\r\n\r\n    openTransaction(this, IDBT_READ_WRITE, function (transaction) {\r\n        transaction.onabort = getError(error, defaultError, key, \"add\");\r\n        transaction.oncomplete = function () {\r\n            if (key instanceof Array) {\r\n                success(keys, values);\r\n            } else {\r\n                success(key, value);\r\n            }\r\n        };\r\n\r\n        for (var i = 0; i < keys.length && i < values.length; i++) {\r\n            transaction.objectStore(name).add({ v: values[i] }, keys[i]);\r\n        }\r\n    }, error);\r\n};\r\n\r\n/** Adds or updates a key/value pair in the store\r\n * @method module:store/indexeddb~IndexedDBStore#addOrUpdate\r\n * @param {String} key - The key\r\n * @param {Object} value - The value\r\n * @param {Function} success - The success callback\r\n * @param {Function} error - The error callback\r\n */\r\nIndexedDBStore.prototype.addOrUpdate = function (key, value, success, error) {\r\n    var name = this.name;\r\n    var defaultError = this.defaultError;\r\n    var keys = [];\r\n    var values = [];\r\n\r\n    if (key instanceof Array) {\r\n        keys = key;\r\n        values = value;\r\n    } else {\r\n        keys = [key];\r\n        values = [value];\r\n    }\r\n\r\n    openTransaction(this, IDBT_READ_WRITE, function (transaction) {\r\n        transaction.onabort = getError(error, defaultError);\r\n        transaction.oncomplete = function () {\r\n            if (key instanceof Array) {\r\n                success(keys, values);\r\n            } else {\r\n                success(key, value);\r\n            }\r\n        };\r\n\r\n        for (var i = 0; i < keys.length && i < values.length; i++) {\r\n            var record = { v: values[i] };\r\n            transaction.objectStore(name).put(record, keys[i]);\r\n        }\r\n    }, error);\r\n};\r\n\r\n/** Clears the store\r\n * @method module:store/indexeddb~IndexedDBStore#clear\r\n * @param {Function} success - The success callback\r\n * @param {Function} error - The error callback\r\n */\r\nIndexedDBStore.prototype.clear = function (success, error) {\r\n    var name = this.name;\r\n    var defaultError = this.defaultError;\r\n    openTransaction(this, IDBT_READ_WRITE, function (transaction) {\r\n        transaction.onerror = getError(error, defaultError);\r\n        transaction.oncomplete = function () {\r\n            success();\r\n        };\r\n\r\n        transaction.objectStore(name).clear();\r\n    }, error);\r\n};\r\n\r\n/** Closes the connection to the database\r\n * @method module:store/indexeddb~IndexedDBStore#close\r\n*/\r\nIndexedDBStore.prototype.close = function () {\r\n    \r\n    if (this.db) {\r\n        this.db.close();\r\n        this.db = null;\r\n    }\r\n};\r\n\r\n/** Returns whether the store contains a key\r\n * @method module:store/indexeddb~IndexedDBStore#contains\r\n * @param {String} key - The key\r\n * @param {Function} success - The success callback\r\n * @param {Function} error - The error callback\r\n */\r\nIndexedDBStore.prototype.contains = function (key, success, error) {\r\n    var name = this.name;\r\n    var defaultError = this.defaultError;\r\n    openTransaction(this, IDBT_READ_ONLY, function (transaction) {\r\n        var objectStore = transaction.objectStore(name);\r\n        var request = objectStore.get(key);\r\n\r\n        transaction.oncomplete = function () {\r\n            success(!!request.result);\r\n        };\r\n        transaction.onerror = getError(error, defaultError);\r\n    }, error);\r\n};\r\n\r\nIndexedDBStore.prototype.defaultError = throwErrorCallback;\r\n\r\n/** Gets all the keys from the store\r\n * @method module:store/indexeddb~IndexedDBStore#getAllKeys\r\n * @param {Function} success - The success callback\r\n * @param {Function} error - The error callback\r\n */\r\nIndexedDBStore.prototype.getAllKeys = function (success, error) {\r\n    var name = this.name;\r\n    var defaultError = this.defaultError;\r\n    openTransaction(this, IDBT_READ_WRITE, function (transaction) {\r\n        var results = [];\r\n\r\n        transaction.oncomplete = function () {\r\n            success(results);\r\n        };\r\n\r\n        var request = transaction.objectStore(name).openCursor();\r\n\r\n        request.onerror = getError(error, defaultError);\r\n        request.onsuccess = function (event) {\r\n            var cursor = event.target.result;\r\n            if (cursor) {\r\n                results.push(cursor.key);\r\n                // Some tools have issues because continue is a javascript reserved word.\r\n                cursor[\"continue\"].call(cursor);\r\n            }\r\n        };\r\n    }, error);\r\n};\r\n\r\n/** Identifies the underlying mechanism used by the store.\r\n*/\r\nIndexedDBStore.prototype.mechanism = \"indexeddb\";\r\n\r\n/** Reads the value for the specified key\r\n * @method module:store/indexeddb~IndexedDBStore#read\r\n * @param {String} key - The key\r\n * @param {Function} success - The success callback\r\n * @param {Function} error - The error callback\r\n * If the key does not exist, the success handler will be called with value = undefined\r\n */\r\nIndexedDBStore.prototype.read = function (key, success, error) {\r\n    var name = this.name;\r\n    var defaultError = this.defaultError;\r\n    var keys = (key instanceof Array) ? key : [key];\r\n\r\n    openTransaction(this, IDBT_READ_ONLY, function (transaction) {\r\n        var values = [];\r\n\r\n        transaction.onerror = getError(error, defaultError, key, \"read\");\r\n        transaction.oncomplete = function () {\r\n            if (key instanceof Array) {\r\n                success(keys, values);\r\n            } else {\r\n                success(keys[0], values[0]);\r\n            }\r\n        };\r\n\r\n        for (var i = 0; i < keys.length; i++) {\r\n            // Some tools have issues because get is a javascript reserved word. \r\n            var objectStore = transaction.objectStore(name);\r\n            var request = objectStore.get.call(objectStore, keys[i]);\r\n            request.onsuccess = function (event) {\r\n                var record = event.target.result;\r\n                values.push(record ? record.v : undefined);\r\n            };\r\n        }\r\n    }, error);\r\n};\r\n\r\n/** Removes the specified key from the store\r\n * @method module:store/indexeddb~IndexedDBStore#remove\r\n * @param {String} key - The key\r\n * @param {Function} success - The success callback\r\n * @param {Function} error - The error callback\r\n */\r\nIndexedDBStore.prototype.remove = function (key, success, error) {\r\n\r\n    var name = this.name;\r\n    var defaultError = this.defaultError;\r\n    var keys = (key instanceof Array) ? key : [key];\r\n\r\n    openTransaction(this, IDBT_READ_WRITE, function (transaction) {\r\n        transaction.onerror = getError(error, defaultError);\r\n        transaction.oncomplete = function () {\r\n            success();\r\n        };\r\n\r\n        for (var i = 0; i < keys.length; i++) {\r\n            // Some tools have issues because continue is a javascript reserved word.\r\n            var objectStore = transaction.objectStore(name);\r\n            objectStore[\"delete\"].call(objectStore, keys[i]);\r\n        }\r\n    }, error);\r\n};\r\n\r\n/** Updates a key/value pair in the store\r\n * @method module:store/indexeddb~IndexedDBStore#update\r\n * @param {String} key - The key\r\n * @param {Object} value - The value\r\n * @param {Function} success - The success callback\r\n * @param {Function} error - The error callback\r\n */\r\nIndexedDBStore.prototype.update = function (key, value, success, error) {\r\n    var name = this.name;\r\n    var defaultError = this.defaultError;\r\n    var keys = [];\r\n    var values = [];\r\n\r\n    if (key instanceof Array) {\r\n        keys = key;\r\n        values = value;\r\n    } else {\r\n        keys = [key];\r\n        values = [value];\r\n    }\r\n\r\n    openTransaction(this, IDBT_READ_WRITE, function (transaction) {\r\n        transaction.onabort = getError(error, defaultError);\r\n        transaction.oncomplete = function () {\r\n            if (key instanceof Array) {\r\n                success(keys, values);\r\n            } else {\r\n                success(key, value);\r\n            }\r\n        };\r\n\r\n        for (var i = 0; i < keys.length && i < values.length; i++) {\r\n            var request = transaction.objectStore(name).openCursor(IDBKeyRange.only(keys[i]));\r\n            var record = { v: values[i] };\r\n            request.pair = { key: keys[i], value: record };\r\n            request.onsuccess = function (event) {\r\n                var cursor = event.target.result;\r\n                if (cursor) {\r\n                    cursor.update(event.target.pair.value);\r\n                } else {\r\n                    transaction.abort();\r\n                }\r\n            }\r\n        }\r\n    }, error);\r\n};\r\n\r\n\r\nmodule.exports = IndexedDBStore;}, \"memory\" : function(exports, module, require) {\r\n'use strict';\r\n\r\n/** @module store/memory */\r\n\r\n\r\nvar utils = require('./../utils.js');\r\n\r\n// Imports.\r\nvar throwErrorCallback = utils.throwErrorCallback;\r\nvar delay = utils.delay;\r\n\r\n/** Constructor for store objects that use a sorted array as the underlying mechanism.\r\n * @class MemoryStore\r\n * @constructor\r\n * @param {String} name - Store name.\r\n */\r\nfunction MemoryStore(name) {\r\n\r\n    var holes = [];\r\n    var items = [];\r\n    var keys = {};\r\n\r\n    this.name = name;\r\n\r\n    var getErrorCallback = function (error) {\r\n        return error || this.defaultError;\r\n    };\r\n\r\n    /** Validates that the specified key is not undefined, not null, and not an array\r\n     * @param key - Key value.\r\n     * @param {Function} error - Error callback.\r\n     * @returns {Boolean} True if the key is valid. False if the key is invalid and the error callback has been queued for execution.\r\n     */\r\n    function validateKeyInput(key, error) {\r\n\r\n        var messageString;\r\n\r\n        if (key instanceof Array) {\r\n            messageString = \"Array of keys not supported\";\r\n        }\r\n\r\n        if (key === undefined || key === null) {\r\n            messageString = \"Invalid key\";\r\n        }\r\n\r\n        if (messageString) {\r\n            delay(error, { message: messageString });\r\n            return false;\r\n        }\r\n        return true;\r\n    }\r\n\r\n    /** This method errors out if the store already contains the specified key.\r\n     * @summary Adds a new value identified by a key to the store.\r\n     * @method module:store/memory~MemoryStore#add\r\n     * @param {String} key - Key string.\r\n     * @param value - Value that is going to be added to the store.\r\n     * @param {Function} success - Callback for a successful add operation.\r\n     * @param {Function} error - Callback for handling errors. If not specified then store.defaultError is invoked.\r\n     */\r\n    this.add = function (key, value, success, error) {\r\n        error = getErrorCallback(error);\r\n\r\n        if (validateKeyInput(key, error)) {\r\n            if (!keys.hasOwnProperty(key)) {\r\n                this.addOrUpdate(key, value, success, error);\r\n            } else {\r\n                error({ message: \"key already exists\", key: key });\r\n            }\r\n        }\r\n    };\r\n\r\n    /** This method will overwrite the key's current value if it already exists in the store; otherwise it simply adds the new key and value.\r\n     * @summary Adds or updates a value identified by a key to the store.\r\n     * @method module:store/memory~MemoryStore#addOrUpdate\r\n     * @param {String} key - Key string.\r\n     * @param value - Value that is going to be added or updated to the store.\r\n     * @param {Function} success - Callback for a successful add or update operation.\r\n     * @param {Function} [error] - Callback for handling errors. If not specified then store.defaultError is invoked.\r\n    */\r\n    this.addOrUpdate = function (key, value, success, error) {\r\n        \r\n        error = getErrorCallback(error);\r\n\r\n        if (validateKeyInput(key, error)) {\r\n            var index = keys[key];\r\n            if (index === undefined) {\r\n                if (holes.length > 0) {\r\n                    index = holes.splice(0, 1);\r\n                } else {\r\n                    index = items.length;\r\n                }\r\n            }\r\n            items[index] = value;\r\n            keys[key] = index;\r\n            delay(success, key, value);\r\n        }\r\n    };\r\n\r\n    /** Removes all the data associated with this store object.\r\n     * @method module:store/memory~MemoryStore#clear\r\n     * @param {Function} success - Callback for a successful clear operation.\r\n     */\r\n    this.clear = function (success) {\r\n        items = [];\r\n        keys = {};\r\n        holes = [];\r\n        delay(success);\r\n    };\r\n\r\n    /** Checks whether a key exists in the store.\r\n     * @method module:store/memory~MemoryStore#contains\r\n     * @param {String} key - Key string.\r\n     * @param {Function} success - Callback indicating whether the store contains the key or not.\r\n     */\r\n    this.contains = function (key, success) {\r\n        var contained = keys.hasOwnProperty(key);\r\n        delay(success, contained);\r\n    };\r\n\r\n    /** Gets all the keys that exist in the store.\r\n     * @method module:store/memory~MemoryStore#getAllKeys\r\n     * @param {Function} success - Callback for a successful get operation.\r\n     */\r\n    this.getAllKeys = function (success) {\r\n\r\n        var results = [];\r\n        for (var name in keys) {\r\n            results.push(name);\r\n        }\r\n        delay(success, results);\r\n    };\r\n\r\n    /** Reads the value associated to a key in the store.\r\n     * @method module:store/memory~MemoryStore#read\r\n     * @param {String} key - Key string.\r\n     * @param {Function} success - Callback for a successful reads operation.\r\n     * @param {Function} error - Callback for handling errors. If not specified then store.defaultError is invoked.\r\n     */\r\n    this.read = function (key, success, error) {\r\n        error = getErrorCallback(error);\r\n\r\n        if (validateKeyInput(key, error)) {\r\n            var index = keys[key];\r\n            delay(success, key, items[index]);\r\n        }\r\n    };\r\n\r\n    /** Removes a key and its value from the store.\r\n     * @method module:store/memory~MemoryStore#remove\r\n     * @param {String} key - Key string.\r\n     * @param {Function} success - Callback for a successful remove operation.\r\n     * @param {Function} [error] - Callback for handling errors. If not specified then store.defaultError is invoked.\r\n     */\r\n    this.remove = function (key, success, error) {\r\n        error = getErrorCallback(error);\r\n\r\n        if (validateKeyInput(key, error)) {\r\n            var index = keys[key];\r\n            if (index !== undefined) {\r\n                if (index === items.length - 1) {\r\n                    items.pop();\r\n                } else {\r\n                    items[index] = undefined;\r\n                    holes.push(index);\r\n                }\r\n                delete keys[key];\r\n\r\n                // The last item was removed, no need to keep track of any holes in the array.\r\n                if (items.length === 0) {\r\n                    holes = [];\r\n                }\r\n            }\r\n\r\n            delay(success);\r\n        }\r\n    };\r\n\r\n    /** Updates the value associated to a key in the store.\r\n     * @method module:store/memory~MemoryStore#update\r\n     * @param {String} key - Key string.\r\n     * @param value - New value.\r\n     * @param {Function} success - Callback for a successful update operation.\r\n     * @param {Function} [error] - Callback for handling errors. If not specified then store.defaultError is invoked.\r\n     * This method errors out if the specified key is not found in the store.\r\n     */\r\n    this.update = function (key, value, success, error) {\r\n        error = getErrorCallback(error);\r\n        if (validateKeyInput(key, error)) {\r\n            if (keys.hasOwnProperty(key)) {\r\n                this.addOrUpdate(key, value, success, error);\r\n            } else {\r\n                error({ message: \"key not found\", key: key });\r\n            }\r\n        }\r\n    };\r\n}\r\n\r\n/** Creates a store object that uses memory storage as its underlying mechanism.\r\n * @method MemoryStore.create\r\n * @param {String} name - Store name.\r\n * @returns {Object} Store object.\r\n */\r\nMemoryStore.create = function (name) {\r\n    return new MemoryStore(name);\r\n};\r\n\r\n/** Checks whether the underlying mechanism for this kind of store objects is supported by the browser.\r\n * @method MemoryStore.isSupported\r\n * @returns {Boolean} True if the mechanism is supported by the browser; otherwise false.\r\n */\r\nMemoryStore.isSupported = function () {\r\n    return true;\r\n};\r\n\r\n/** This function does nothing in MemoryStore as it does not have a connection model.\r\n*/\r\nMemoryStore.prototype.close = function () {\r\n};\r\n\r\nMemoryStore.prototype.defaultError = throwErrorCallback;\r\n\r\n/** Identifies the underlying mechanism used by the store.\r\n*/\r\nMemoryStore.prototype.mechanism = \"memory\";\r\n\r\n\r\n/** MemoryStore (see {@link MemoryStore}) */\r\nmodule.exports = MemoryStore;}, \"utils\" : function(exports, module, require) {\r\n'use strict';\r\n\r\n/** @module odatajs/utils */\r\n\r\n\r\nfunction inBrowser() {\r\n    return typeof window !== 'undefined';\r\n}\r\n\r\n/** Creates a new ActiveXObject from the given progId.\r\n * @param {String} progId - ProgId string of the desired ActiveXObject.\r\n * @returns {Object} The ActiveXObject instance. Null if ActiveX is not supported by the browser.\r\n * This function throws whatever exception might occur during the creation\r\n * of the ActiveXObject.\r\n*/\r\nvar activeXObject = function (progId) {\r\n    \r\n    if (window.ActiveXObject) {\r\n        return new window.ActiveXObject(progId);\r\n    }\r\n    return null;\r\n};\r\n\r\n/** Checks whether the specified value is different from null and undefined.\r\n * @param [value] Value to check ( may be null)\r\n * @returns {Boolean} true if the value is assigned; false otherwise.\r\n*/     \r\nfunction assigned(value) {\r\n    return value !== null && value !== undefined;\r\n}\r\n\r\n/** Checks whether the specified item is in the array.\r\n * @param {Array} [arr] Array to check in.\r\n * @param item - Item to look for.\r\n * @returns {Boolean} true if the item is contained, false otherwise.\r\n*/\r\nfunction contains(arr, item) {\r\n    var i, len;\r\n    for (i = 0, len = arr.length; i < len; i++) {\r\n        if (arr[i] === item) {\r\n            return true;\r\n        }\r\n    }\r\n    return false;\r\n}\r\n\r\n/** Given two values, picks the first one that is not undefined.\r\n * @param a - First value.\r\n * @param b - Second value.\r\n * @returns a if it's a defined value; else b.\r\n */\r\nfunction defined(a, b) {\r\n    return (a !== undefined) ? a : b;\r\n}\r\n\r\n/** Delays the invocation of the specified function until execution unwinds.\r\n * @param {Function} callback - Callback function.\r\n */\r\nfunction delay(callback) {\r\n\r\n    if (arguments.length === 1) {\r\n        window.setTimeout(callback, 0);\r\n        return;\r\n    }\r\n\r\n    var args = Array.prototype.slice.call(arguments, 1);\r\n    window.setTimeout(function () {\r\n        callback.apply(this, args);\r\n    }, 0);\r\n}\r\n\r\n/** Throws an exception in case that a condition evaluates to false.\r\n * @param {Boolean} condition - Condition to evaluate.\r\n * @param {String} message - Message explaining the assertion.\r\n * @param {Object} data - Additional data to be included in the exception.\r\n */\r\nfunction djsassert(condition, message, data) {\r\n\r\n\r\n    if (!condition) {\r\n        throw { message: \"Assert fired: \" + message, data: data };\r\n    }\r\n}\r\n\r\n/** Extends the target with the specified values.\r\n * @param {Object} target - Object to add properties to.\r\n * @param {Object} values - Object with properties to add into target.\r\n * @returns {Object} The target object.\r\n*/\r\nfunction extend(target, values) {\r\n    for (var name in values) {\r\n        target[name] = values[name];\r\n    }\r\n\r\n    return target;\r\n}\r\n\r\nfunction find(arr, callback) {\r\n    /** Returns the first item in the array that makes the callback function true.\r\n     * @param {Array} [arr] Array to check in. ( may be null)\r\n     * @param {Function} callback - Callback function to invoke once per item in the array.\r\n     * @returns The first item that makes the callback return true; null otherwise or if the array is null.\r\n    */\r\n\r\n    if (arr) {\r\n        var i, len;\r\n        for (i = 0, len = arr.length; i < len; i++) {\r\n            if (callback(arr[i])) {\r\n                return arr[i];\r\n            }\r\n        }\r\n    }\r\n    return null;\r\n}\r\n\r\nfunction isArray(value) {\r\n    /** Checks whether the specified value is an array object.\r\n     * @param value - Value to check.\r\n     * @returns {Boolean} true if the value is an array object; false otherwise.\r\n     */\r\n\r\n    return Object.prototype.toString.call(value) === \"[object Array]\";\r\n}\r\n\r\n/** Checks whether the specified value is a Date object.\r\n * @param value - Value to check.\r\n * @returns {Boolean} true if the value is a Date object; false otherwise.\r\n */\r\nfunction isDate(value) {\r\n    return Object.prototype.toString.call(value) === \"[object Date]\";\r\n}\r\n\r\n/** Tests whether a value is an object.\r\n * @param value - Value to test.\r\n * @returns {Boolean} True is the value is an object; false otherwise.\r\n * Per javascript rules, null and array values are objects and will cause this function to return true.\r\n */\r\nfunction isObject(value) {\r\n    return typeof value === \"object\";\r\n}\r\n\r\n/** Parses a value in base 10.\r\n * @param {String} value - String value to parse.\r\n * @returns {Number} The parsed value, NaN if not a valid value.\r\n*/   \r\nfunction parseInt10(value) {\r\n    return parseInt(value, 10);\r\n}\r\n\r\n/** Renames a property in an object.\r\n * @param {Object} obj - Object in which the property will be renamed.\r\n * @param {String} oldName - Name of the property that will be renamed.\r\n * @param {String} newName - New name of the property.\r\n * This function will not do anything if the object doesn't own a property with the specified old name.\r\n */\r\nfunction renameProperty(obj, oldName, newName) {\r\n    if (obj.hasOwnProperty(oldName)) {\r\n        obj[newName] = obj[oldName];\r\n        delete obj[oldName];\r\n    }\r\n}\r\n\r\n/** Default error handler.\r\n * @param {Object} error - Error to handle.\r\n */\r\nfunction throwErrorCallback(error) {\r\n    throw error;\r\n}\r\n\r\n/** Removes leading and trailing whitespaces from a string.\r\n * @param {String} str String to trim\r\n * @returns {String} The string with no leading or trailing whitespace.\r\n */\r\nfunction trimString(str) {\r\n    if (str.trim) {\r\n        return str.trim();\r\n    }\r\n\r\n    return str.replace(/^\\s+|\\s+$/g, '');\r\n}\r\n\r\n/** Returns a default value in place of undefined.\r\n * @param [value] Value to check (may be null)\r\n * @param defaultValue - Value to return if value is undefined.\r\n * @returns value if it's defined; defaultValue otherwise.\r\n * This should only be used for cases where falsy values are valid;\r\n * otherwise the pattern should be 'x = (value) ? value : defaultValue;'.\r\n */\r\nfunction undefinedDefault(value, defaultValue) {\r\n    return (value !== undefined) ? value : defaultValue;\r\n}\r\n\r\n// Regular expression that splits a uri into its components:\r\n// 0 - is the matched string.\r\n// 1 - is the scheme.\r\n// 2 - is the authority.\r\n// 3 - is the path.\r\n// 4 - is the query.\r\n// 5 - is the fragment.\r\nvar uriRegEx = /^([^:\\/?#]+:)?(\\/\\/[^\\/?#]*)?([^?#:]+)?(\\?[^#]*)?(#.*)?/;\r\nvar uriPartNames = [\"scheme\", \"authority\", \"path\", \"query\", \"fragment\"];\r\n\r\n/** Gets information about the components of the specified URI.\r\n * @param {String} uri - URI to get information from.\r\n * @return  {Object} An object with an isAbsolute flag and part names (scheme, authority, etc.) if available.\r\n */\r\nfunction getURIInfo(uri) {\r\n    var result = { isAbsolute: false };\r\n\r\n    if (uri) {\r\n        var matches = uriRegEx.exec(uri);\r\n        if (matches) {\r\n            var i, len;\r\n            for (i = 0, len = uriPartNames.length; i < len; i++) {\r\n                if (matches[i + 1]) {\r\n                    result[uriPartNames[i]] = matches[i + 1];\r\n                }\r\n            }\r\n        }\r\n        if (result.scheme) {\r\n            result.isAbsolute = true;\r\n        }\r\n    }\r\n\r\n    return result;\r\n}\r\n\r\n/** Builds a URI string from its components.\r\n * @param {Object} uriInfo -  An object with uri parts (scheme, authority, etc.).\r\n * @returns {String} URI string.\r\n */\r\nfunction getURIFromInfo(uriInfo) {\r\n    return \"\".concat(\r\n        uriInfo.scheme || \"\",\r\n        uriInfo.authority || \"\",\r\n        uriInfo.path || \"\",\r\n        uriInfo.query || \"\",\r\n        uriInfo.fragment || \"\");\r\n}\r\n\r\n// Regular expression that splits a uri authority into its subcomponents:\r\n// 0 - is the matched string.\r\n// 1 - is the userinfo subcomponent.\r\n// 2 - is the host subcomponent.\r\n// 3 - is the port component.\r\nvar uriAuthorityRegEx = /^\\/{0,2}(?:([^@]*)@)?([^:]+)(?::{1}(\\d+))?/;\r\n\r\n// Regular expression that matches percentage enconded octects (i.e %20 or %3A);\r\nvar pctEncodingRegEx = /%[0-9A-F]{2}/ig;\r\n\r\n/** Normalizes the casing of a URI.\r\n * @param {String} uri - URI to normalize, absolute or relative.\r\n * @returns {String} The URI normalized to lower case.\r\n*/\r\nfunction normalizeURICase(uri) {\r\n    var uriInfo = getURIInfo(uri);\r\n    var scheme = uriInfo.scheme;\r\n    var authority = uriInfo.authority;\r\n\r\n    if (scheme) {\r\n        uriInfo.scheme = scheme.toLowerCase();\r\n        if (authority) {\r\n            var matches = uriAuthorityRegEx.exec(authority);\r\n            if (matches) {\r\n                uriInfo.authority = \"//\" +\r\n                (matches[1] ? matches[1] + \"@\" : \"\") +\r\n                (matches[2].toLowerCase()) +\r\n                (matches[3] ? \":\" + matches[3] : \"\");\r\n            }\r\n        }\r\n    }\r\n\r\n    uri = getURIFromInfo(uriInfo);\r\n\r\n    return uri.replace(pctEncodingRegEx, function (str) {\r\n        return str.toLowerCase();\r\n    });\r\n}\r\n\r\n/** Normalizes a possibly relative URI with a base URI.\r\n * @param {String} uri - URI to normalize, absolute or relative\r\n * @param {String} base - Base URI to compose with (may be null)\r\n * @returns {String} The composed URI if relative; the original one if absolute.\r\n */\r\nfunction normalizeURI(uri, base) {\r\n    if (!base) {\r\n        return uri;\r\n    }\r\n\r\n    var uriInfo = getURIInfo(uri);\r\n    if (uriInfo.isAbsolute) {\r\n        return uri;\r\n    }\r\n\r\n    var baseInfo = getURIInfo(base);\r\n    var normInfo = {};\r\n    var path;\r\n\r\n    if (uriInfo.authority) {\r\n        normInfo.authority = uriInfo.authority;\r\n        path = uriInfo.path;\r\n        normInfo.query = uriInfo.query;\r\n    } else {\r\n        if (!uriInfo.path) {\r\n            path = baseInfo.path;\r\n            normInfo.query = uriInfo.query || baseInfo.query;\r\n        } else {\r\n            if (uriInfo.path.charAt(0) === '/') {\r\n                path = uriInfo.path;\r\n            } else {\r\n                path = mergeUriPathWithBase(uriInfo.path, baseInfo.path);\r\n            }\r\n            normInfo.query = uriInfo.query;\r\n        }\r\n        normInfo.authority = baseInfo.authority;\r\n    }\r\n\r\n    normInfo.path = removeDotsFromPath(path);\r\n\r\n    normInfo.scheme = baseInfo.scheme;\r\n    normInfo.fragment = uriInfo.fragment;\r\n\r\n    return getURIFromInfo(normInfo);\r\n}\r\n\r\n/** Merges the path of a relative URI and a base URI.\r\n * @param {String} uriPath - Relative URI path.\r\n * @param {String} basePath - Base URI path.\r\n * @returns {String} A string with the merged path.\r\n */\r\nfunction mergeUriPathWithBase(uriPath, basePath) {\r\n    var path = \"/\";\r\n    var end;\r\n\r\n    if (basePath) {\r\n        end = basePath.lastIndexOf(\"/\");\r\n        path = basePath.substring(0, end);\r\n\r\n        if (path.charAt(path.length - 1) !== \"/\") {\r\n            path = path + \"/\";\r\n        }\r\n    }\r\n\r\n    return path + uriPath;\r\n}\r\n\r\n/** Removes the special folders . and .. from a URI's path.\r\n * @param {string} path - URI path component.\r\n * @returns {String} Path without any . and .. folders.\r\n */\r\nfunction removeDotsFromPath(path) {\r\n    var result = \"\";\r\n    var segment = \"\";\r\n    var end;\r\n\r\n    while (path) {\r\n        if (path.indexOf(\"..\") === 0 || path.indexOf(\".\") === 0) {\r\n            path = path.replace(/^\\.\\.?\\/?/g, \"\");\r\n        } else if (path.indexOf(\"/..\") === 0) {\r\n            path = path.replace(/^\\/\\..\\/?/g, \"/\");\r\n            end = result.lastIndexOf(\"/\");\r\n            if (end === -1) {\r\n                result = \"\";\r\n            } else {\r\n                result = result.substring(0, end);\r\n            }\r\n        } else if (path.indexOf(\"/.\") === 0) {\r\n            path = path.replace(/^\\/\\.\\/?/g, \"/\");\r\n        } else {\r\n            segment = path;\r\n            end = path.indexOf(\"/\", 1);\r\n            if (end !== -1) {\r\n                segment = path.substring(0, end);\r\n            }\r\n            result = result + segment;\r\n            path = path.replace(segment, \"\");\r\n        }\r\n    }\r\n    return result;\r\n}\r\n\r\nfunction convertByteArrayToHexString(str) {\r\n    var arr = [];\r\n    if (window.atob === undefined) {\r\n        arr = decodeBase64(str);\r\n    } else {\r\n        var binaryStr = window.atob(str);\r\n        for (var i = 0; i < binaryStr.length; i++) {\r\n            arr.push(binaryStr.charCodeAt(i));\r\n        }\r\n    }\r\n    var hexValue = \"\";\r\n    var hexValues = \"0123456789ABCDEF\";\r\n    for (var j = 0; j < arr.length; j++) {\r\n        var t = arr[j];\r\n        hexValue += hexValues[t >> 4];\r\n        hexValue += hexValues[t & 0x0F];\r\n    }\r\n    return hexValue;\r\n}\r\n\r\nfunction decodeBase64(str) {\r\n    var binaryString = \"\";\r\n    for (var i = 0; i < str.length; i++) {\r\n        var base65IndexValue = getBase64IndexValue(str[i]);\r\n        var binaryValue = \"\";\r\n        if (base65IndexValue !== null) {\r\n            binaryValue = base65IndexValue.toString(2);\r\n            binaryString += addBase64Padding(binaryValue);\r\n        }\r\n    }\r\n    var byteArray = [];\r\n    var numberOfBytes = parseInt(binaryString.length / 8, 10);\r\n    for (i = 0; i < numberOfBytes; i++) {\r\n        var intValue = parseInt(binaryString.substring(i * 8, (i + 1) * 8), 2);\r\n        byteArray.push(intValue);\r\n    }\r\n    return byteArray;\r\n}\r\n\r\nfunction getBase64IndexValue(character) {\r\n    var asciiCode = character.charCodeAt(0);\r\n    var asciiOfA = 65;\r\n    var differenceBetweenZanda = 6;\r\n    if (asciiCode >= 65 && asciiCode <= 90) {           // between \"A\" and \"Z\" inclusive\r\n        return asciiCode - asciiOfA;\r\n    } else if (asciiCode >= 97 && asciiCode <= 122) {   // between 'a' and 'z' inclusive\r\n        return asciiCode - asciiOfA - differenceBetweenZanda;\r\n    } else if (asciiCode >= 48 && asciiCode <= 57) {    // between '0' and '9' inclusive\r\n        return asciiCode + 4;\r\n    } else if (character == \"+\") {\r\n        return 62;\r\n    } else if (character == \"/\") {\r\n        return 63;\r\n    } else {\r\n        return null;\r\n    }\r\n}\r\n\r\nfunction addBase64Padding(binaryString) {\r\n    while (binaryString.length < 6) {\r\n        binaryString = \"0\" + binaryString;\r\n    }\r\n    return binaryString;\r\n\r\n}\r\n\r\nfunction getJsonValueArraryLength(data) {\r\n    if (data && data.value) {\r\n        return data.value.length;\r\n    }\r\n\r\n    return 0;\r\n}\r\n\r\nfunction sliceJsonValueArray(data, start, end) {\r\n    if (data === undefined || data.value === undefined) {\r\n        return data;\r\n    }\r\n\r\n    if (start < 0) {\r\n        start = 0;\r\n    }\r\n\r\n    var length = getJsonValueArraryLength(data);\r\n    if (length < end) {\r\n        end = length;\r\n    }\r\n\r\n    var newdata = {};\r\n    for (var property in data) {\r\n        if (property == \"value\") {\r\n            newdata[property] = data[property].slice(start, end);\r\n        } else {\r\n            newdata[property] = data[property];\r\n        }\r\n    }\r\n\r\n    return newdata;\r\n}\r\n\r\nfunction concatJsonValueArray(data, concatData) {\r\n    if (concatData === undefined || concatData.value === undefined) {\r\n        return data;\r\n    }\r\n\r\n    if (data === undefined || Object.keys(data).length === 0) {\r\n        return concatData;\r\n    }\r\n\r\n    if (data.value === undefined) {\r\n        data.value = concatData.value;\r\n        return data;\r\n    }\r\n\r\n    data.value = data.value.concat(concatData.value);\r\n\r\n    return data;\r\n}\r\n\r\nfunction endsWith(input, search) {\r\n    return input.indexOf(search, input.length - search.length) !== -1;\r\n}\r\n\r\nfunction startsWith (input, search) {\r\n    return input.indexOf(search) === 0;\r\n}\r\n\r\nfunction getFormatKind(format, defaultFormatKind) {\r\n    var formatKind = defaultFormatKind;\r\n    if (!assigned(format)) {\r\n        return formatKind;\r\n    }\r\n\r\n    var normalizedFormat = format.toLowerCase();\r\n    switch (normalizedFormat) {\r\n        case \"none\":\r\n            formatKind = 0;\r\n            break;\r\n        case \"minimal\":\r\n            formatKind = 1;\r\n            break;\r\n        case \"full\":\r\n            formatKind = 2;\r\n            break;\r\n        default:\r\n            break;\r\n    }\r\n\r\n    return formatKind;\r\n}\r\n\r\n\r\n    \r\n    \r\nexports.inBrowser = inBrowser;\r\nexports.activeXObject = activeXObject;\r\nexports.assigned = assigned;\r\nexports.contains = contains;\r\nexports.defined = defined;\r\nexports.delay = delay;\r\nexports.djsassert = djsassert;\r\nexports.extend = extend;\r\nexports.find = find;\r\nexports.getURIInfo = getURIInfo;\r\nexports.isArray = isArray;\r\nexports.isDate = isDate;\r\nexports.isObject = isObject;\r\nexports.normalizeURI = normalizeURI;\r\nexports.normalizeURICase = normalizeURICase;\r\nexports.parseInt10 = parseInt10;\r\nexports.renameProperty = renameProperty;\r\nexports.throwErrorCallback = throwErrorCallback;\r\nexports.trimString = trimString;\r\nexports.undefinedDefault = undefinedDefault;\r\nexports.decodeBase64 = decodeBase64;\r\nexports.convertByteArrayToHexString = convertByteArrayToHexString;\r\nexports.getJsonValueArraryLength = getJsonValueArraryLength;\r\nexports.sliceJsonValueArray = sliceJsonValueArray;\r\nexports.concatJsonValueArray = concatJsonValueArray;\r\nexports.startsWith = startsWith;\r\nexports.endsWith = endsWith;\r\nexports.getFormatKind = getFormatKind;}, \"xml\" : function(exports, module, require) {\r\n'use strict';\r\n \r\n\r\n/** @module odatajs/xml */\r\n\r\nvar utils    = require('./utils.js');\r\n\r\nvar activeXObject = utils.activeXObject;\r\nvar djsassert = utils.djsassert;\r\nvar extend = utils.extend;\r\nvar isArray = utils.isArray;\r\nvar normalizeURI = utils.normalizeURI;\r\n\r\n// URI prefixes to generate smaller code.\r\nvar http = \"http://\";\r\nvar w3org = http + \"www.w3.org/\";               // http://www.w3.org/\r\n\r\nvar xhtmlNS = w3org + \"1999/xhtml\";             // http://www.w3.org/1999/xhtml\r\nvar xmlnsNS = w3org + \"2000/xmlns/\";            // http://www.w3.org/2000/xmlns/\r\nvar xmlNS = w3org + \"XML/1998/namespace\";       // http://www.w3.org/XML/1998/namespace\r\n\r\nvar mozillaParserErroNS = http + \"www.mozilla.org/newlayout/xml/parsererror.xml\";\r\n\r\n/** Checks whether the specified string has leading or trailing spaces.\r\n * @param {String} text - String to check.\r\n * @returns {Boolean} true if text has any leading or trailing whitespace; false otherwise.\r\n */\r\nfunction hasLeadingOrTrailingWhitespace(text) {\r\n    var re = /(^\\s)|(\\s$)/;\r\n    return re.test(text);\r\n}\r\n\r\n/** Determines whether the specified text is empty or whitespace.\r\n * @param {String} text - Value to inspect.\r\n * @returns {Boolean} true if the text value is empty or all whitespace; false otherwise.\r\n */\r\nfunction isWhitespace(text) {\r\n\r\n\r\n    var ws = /^\\s*$/;\r\n    return text === null || ws.test(text);\r\n}\r\n\r\n/** Determines whether the specified element has xml:space='preserve' applied.\r\n * @param domElement - Element to inspect.\r\n * @returns {Boolean} Whether xml:space='preserve' is in effect.\r\n */\r\nfunction isWhitespacePreserveContext(domElement) {\r\n\r\n\r\n    while (domElement !== null && domElement.nodeType === 1) {\r\n        var val = xmlAttributeValue(domElement, \"space\", xmlNS);\r\n        if (val === \"preserve\") {\r\n            return true;\r\n        } else if (val === \"default\") {\r\n            break;\r\n        } else {\r\n            domElement = domElement.parentNode;\r\n        }\r\n    }\r\n\r\n    return false;\r\n}\r\n\r\n/** Determines whether the attribute is a XML namespace declaration.\r\n * @param domAttribute - Element to inspect.\r\n * @return {Boolean} True if the attribute is a namespace declaration (its name is 'xmlns' or starts with 'xmlns:'; false otherwise.\r\n */\r\nfunction isXmlNSDeclaration(domAttribute) {\r\n    var nodeName = domAttribute.nodeName;\r\n    return nodeName == \"xmlns\" || nodeName.indexOf(\"xmlns:\") === 0;\r\n}\r\n\r\n/** Safely set as property in an object by invoking obj.setProperty.\r\n * @param obj - Object that exposes a setProperty method.\r\n * @param {String} name - Property name\r\n * @param value - Property value.\r\n */\r\nfunction safeSetProperty(obj, name, value) {\r\n\r\n\r\n    try {\r\n        obj.setProperty(name, value);\r\n    } catch (_) { }\r\n}\r\n\r\n/** Creates an configures new MSXML 3.0 ActiveX object.\r\n * @returns {Object} New MSXML 3.0 ActiveX object.\r\n * This function throws any exception that occurs during the creation\r\n * of the MSXML 3.0 ActiveX object.\r\n */\r\nfunction msXmlDom3() {\r\n    var msxml3 = activeXObject(\"Msxml2.DOMDocument.3.0\");\r\n    if (msxml3) {\r\n        safeSetProperty(msxml3, \"ProhibitDTD\", true);\r\n        safeSetProperty(msxml3, \"MaxElementDepth\", 256);\r\n        safeSetProperty(msxml3, \"AllowDocumentFunction\", false);\r\n        safeSetProperty(msxml3, \"AllowXsltScript\", false);\r\n    }\r\n    return msxml3;\r\n}\r\n\r\n/** Creates an configures new MSXML 6.0 or MSXML 3.0 ActiveX object.\r\n * @returns {Object} New MSXML 3.0 ActiveX object.\r\n * This function will try to create a new MSXML 6.0 ActiveX object. If it fails then\r\n * it will fallback to create a new MSXML 3.0 ActiveX object. Any exception that\r\n * happens during the creation of the MSXML 6.0 will be handled by the function while\r\n * the ones that happend during the creation of the MSXML 3.0 will be thrown.\r\n */\r\nfunction msXmlDom() {\r\n    try {\r\n        var msxml = activeXObject(\"Msxml2.DOMDocument.6.0\");\r\n        if (msxml) {\r\n            msxml.async = true;\r\n        }\r\n        return msxml;\r\n    } catch (_) {\r\n        return msXmlDom3();\r\n    }\r\n}\r\n\r\n/** Parses an XML string using the MSXML DOM.\r\n * @returns {Object} New MSXML DOMDocument node representing the parsed XML string.\r\n * This function throws any exception that occurs during the creation\r\n * of the MSXML ActiveX object.  It also will throw an exception\r\n * in case of a parsing error.\r\n */\r\nfunction msXmlParse(text) {\r\n    var dom = msXmlDom();\r\n    if (!dom) {\r\n        return null;\r\n    }\r\n\r\n    dom.loadXML(text);\r\n    var parseError = dom.parseError;\r\n    if (parseError.errorCode !== 0) {\r\n        xmlThrowParserError(parseError.reason, parseError.srcText, text);\r\n    }\r\n    return dom;\r\n}\r\n\r\n/** Throws a new exception containing XML parsing error information.\r\n * @param exceptionOrReason - String indicating the reason of the parsing failure or Object detailing the parsing error.\r\n * @param {String} srcText -     String indicating the part of the XML string that caused the parsing error.\r\n * @param {String} errorXmlText - XML string for wich the parsing failed.\r\n */\r\nfunction xmlThrowParserError(exceptionOrReason, srcText, errorXmlText) {\r\n\r\n    if (typeof exceptionOrReason === \"string\") {\r\n        exceptionOrReason = { message: exceptionOrReason };\r\n    }\r\n    throw extend(exceptionOrReason, { srcText: srcText || \"\", errorXmlText: errorXmlText || \"\" });\r\n}\r\n\r\n/** Returns an XML DOM document from the specified text.\r\n * @param {String} text - Document text.\r\n * @returns XML DOM document.\r\n * This function will throw an exception in case of a parse error\r\n */\r\nfunction xmlParse(text) {\r\n    var domParser = undefined;\r\n    if (utils.inBrowser()) {\r\n        domParser = window.DOMParser && new window.DOMParser();\r\n    } else {\r\n        domParser = new (require('xmldom').DOMParser)();\r\n    }\r\n    var dom;\r\n\r\n    if (!domParser) {\r\n        dom = msXmlParse(text);\r\n        if (!dom) {\r\n            xmlThrowParserError(\"XML DOM parser not supported\");\r\n        }\r\n        return dom;\r\n    }\r\n\r\n    try {\r\n        dom = domParser.parseFromString(text, \"text/xml\");\r\n    } catch (e) {\r\n        xmlThrowParserError(e, \"\", text);\r\n    }\r\n\r\n    var element = dom.documentElement;\r\n    var nsURI = element.namespaceURI;\r\n    var localName = xmlLocalName(element);\r\n\r\n    // Firefox reports errors by returing the DOM for an xml document describing the problem.\r\n    if (localName === \"parsererror\" && nsURI === mozillaParserErroNS) {\r\n        var srcTextElement = xmlFirstChildElement(element, mozillaParserErroNS, \"sourcetext\");\r\n        var srcText = srcTextElement ? xmlNodeValue(srcTextElement) : \"\";\r\n        xmlThrowParserError(xmlInnerText(element) || \"\", srcText, text);\r\n    }\r\n\r\n    // Chrome (and maybe other webkit based browsers) report errors by injecting a header with an error message.\r\n    // The error may be localized, so instead we simply check for a header as the\r\n    // top element or descendant child of the document.\r\n    if (localName === \"h3\" && nsURI === xhtmlNS || xmlFirstDescendantElement(element, xhtmlNS, \"h3\")) {\r\n        var reason = \"\";\r\n        var siblings = [];\r\n        var cursor = element.firstChild;\r\n        while (cursor) {\r\n            if (cursor.nodeType === 1) {\r\n                reason += xmlInnerText(cursor) || \"\";\r\n            }\r\n            siblings.push(cursor.nextSibling);\r\n            cursor = cursor.firstChild || siblings.shift();\r\n        }\r\n        reason += xmlInnerText(element) || \"\";\r\n        xmlThrowParserError(reason, \"\", text);\r\n    }\r\n\r\n    return dom;\r\n}\r\n\r\n/** Builds a XML qualified name string in the form of \"prefix:name\".\r\n * @param {String} prefix - Prefix string (may be null)\r\n * @param {String} name - Name string to qualify with the prefix.\r\n * @returns {String} Qualified name.\r\n */\r\nfunction xmlQualifiedName(prefix, name) {\r\n    return prefix ? prefix + \":\" + name : name;\r\n}\r\n\r\n/** Appends a text node into the specified DOM element node.\r\n * @param domNode - DOM node for the element.\r\n * @param {String} textNode - Text to append as a child of element.\r\n*/\r\nfunction xmlAppendText(domNode, textNode) {\r\n    if (hasLeadingOrTrailingWhitespace(textNode.data)) {\r\n        var attr = xmlAttributeNode(domNode, xmlNS, \"space\");\r\n        if (!attr) {\r\n            attr = xmlNewAttribute(domNode.ownerDocument, xmlNS, xmlQualifiedName(\"xml\", \"space\"));\r\n            xmlAppendChild(domNode, attr);\r\n        }\r\n        attr.value = \"preserve\";\r\n    }\r\n    domNode.appendChild(textNode);\r\n    return domNode;\r\n}\r\n\r\n/** Iterates through the XML element's attributes and invokes the callback function for each one.\r\n * @param element - Wrapped element to iterate over.\r\n * @param {Function} onAttributeCallback - Callback function to invoke with wrapped attribute nodes.\r\n*/\r\nfunction xmlAttributes(element, onAttributeCallback) {\r\n    var attributes = element.attributes;\r\n    var i, len;\r\n    for (i = 0, len = attributes.length; i < len; i++) {\r\n        onAttributeCallback(attributes.item(i));\r\n    }\r\n}\r\n\r\n/** Returns the value of a DOM element's attribute.\r\n * @param domNode - DOM node for the owning element.\r\n * @param {String} localName - Local name of the attribute.\r\n * @param {String} nsURI - Namespace URI of the attribute.\r\n * @returns {String} - The attribute value, null if not found (may be null)\r\n */\r\nfunction xmlAttributeValue(domNode, localName, nsURI) {\r\n\r\n    var attribute = xmlAttributeNode(domNode, localName, nsURI);\r\n    return attribute ? xmlNodeValue(attribute) : null;\r\n}\r\n\r\n/** Gets an attribute node from a DOM element.\r\n * @param domNode - DOM node for the owning element.\r\n * @param {String} localName - Local name of the attribute.\r\n * @param {String} nsURI - Namespace URI of the attribute.\r\n * @returns The attribute node, null if not found.\r\n */\r\nfunction xmlAttributeNode(domNode, localName, nsURI) {\r\n\r\n    var attributes = domNode.attributes;\r\n    if (attributes.getNamedItemNS) {\r\n        return attributes.getNamedItemNS(nsURI || null, localName);\r\n    }\r\n\r\n    return attributes.getQualifiedItem(localName, nsURI) || null;\r\n}\r\n\r\n/** Gets the value of the xml:base attribute on the specified element.\r\n * @param domNode - Element to get xml:base attribute value from.\r\n * @param [baseURI] - Base URI used to normalize the value of the xml:base attribute ( may be null)\r\n * @returns {String} Value of the xml:base attribute if found; the baseURI or null otherwise.\r\n */\r\nfunction xmlBaseURI(domNode, baseURI) {\r\n\r\n    var base = xmlAttributeNode(domNode, \"base\", xmlNS);\r\n    return (base ? normalizeURI(base.value, baseURI) : baseURI) || null;\r\n}\r\n\r\n\r\n/** Iterates through the XML element's child DOM elements and invokes the callback function for each one.\r\n * @param domNode - DOM Node containing the DOM elements to iterate over.\r\n * @param {Function} onElementCallback - Callback function to invoke for each child DOM element.\r\n*/\r\nfunction xmlChildElements(domNode, onElementCallback) {\r\n\r\n    xmlTraverse(domNode, /*recursive*/false, function (child) {\r\n        if (child.nodeType === 1) {\r\n            onElementCallback(child);\r\n        }\r\n        // continue traversing.\r\n        return true;\r\n    });\r\n}\r\n\r\n/** Gets the descendant element under root that corresponds to the specified path and namespace URI.\r\n * @param root - DOM element node from which to get the descendant element.\r\n * @param {String} namespaceURI - The namespace URI of the element to match.\r\n * @param {String} path - Path to the desired descendant element.\r\n * @return The element specified by path and namespace URI.\r\n * All the elements in the path are matched against namespaceURI.\r\n * The function will stop searching on the first element that doesn't match the namespace and the path.\r\n */\r\nfunction xmlFindElementByPath(root, namespaceURI, path) {\r\n    var parts = path.split(\"/\");\r\n    var i, len;\r\n    for (i = 0, len = parts.length; i < len; i++) {\r\n        root = root && xmlFirstChildElement(root, namespaceURI, parts[i]);\r\n    }\r\n    return root || null;\r\n}\r\n\r\n/** Gets the DOM element or DOM attribute node under root that corresponds to the specified path and namespace URI.\r\n * @param root - DOM element node from which to get the descendant node.\r\n * @param {String} namespaceURI - The namespace URI of the node to match.\r\n * @param {String} path - Path to the desired descendant node.\r\n * @return The node specified by path and namespace URI.\r\n\r\n* This function will traverse the path and match each node associated to a path segement against the namespace URI.\r\n* The traversal stops when the whole path has been exahusted or a node that doesn't belogong the specified namespace is encountered.\r\n* The last segment of the path may be decorated with a starting @ character to indicate that the desired node is a DOM attribute.\r\n*/\r\nfunction xmlFindNodeByPath(root, namespaceURI, path) {\r\n    \r\n\r\n    var lastSegmentStart = path.lastIndexOf(\"/\");\r\n    var nodePath = path.substring(lastSegmentStart + 1);\r\n    var parentPath = path.substring(0, lastSegmentStart);\r\n\r\n    var node = parentPath ? xmlFindElementByPath(root, namespaceURI, parentPath) : root;\r\n    if (node) {\r\n        if (nodePath.charAt(0) === \"@\") {\r\n            return xmlAttributeNode(node, nodePath.substring(1), namespaceURI);\r\n        }\r\n        return xmlFirstChildElement(node, namespaceURI, nodePath);\r\n    }\r\n    return null;\r\n}\r\n\r\n/** Returns the first child DOM element under the specified DOM node that matches the specified namespace URI and local name.\r\n * @param domNode - DOM node from which the child DOM element is going to be retrieved.\r\n * @param {String} [namespaceURI] - \r\n * @param {String} [localName] - \r\n * @return The node's first child DOM element that matches the specified namespace URI and local name; null otherwise.\r\n */\r\nfunction xmlFirstChildElement(domNode, namespaceURI, localName) {\r\n\r\n    return xmlFirstElementMaybeRecursive(domNode, namespaceURI, localName, /*recursive*/false);\r\n}\r\n\r\n/** Returns the first descendant DOM element under the specified DOM node that matches the specified namespace URI and local name.\r\n * @param domNode - DOM node from which the descendant DOM element is going to be retrieved.\r\n * @param {String} [namespaceURI] - \r\n * @param {String} [localName] - \r\n * @return The node's first descendant DOM element that matches the specified namespace URI and local name; null otherwise.\r\n*/\r\nfunction xmlFirstDescendantElement(domNode, namespaceURI, localName) {\r\n    if (domNode.getElementsByTagNameNS) {\r\n        var result = domNode.getElementsByTagNameNS(namespaceURI, localName);\r\n        return result.length > 0 ? result[0] : null;\r\n    }\r\n    return xmlFirstElementMaybeRecursive(domNode, namespaceURI, localName, /*recursive*/true);\r\n}\r\n\r\n/** Returns the first descendant DOM element under the specified DOM node that matches the specified namespace URI and local name.\r\n * @param domNode - DOM node from which the descendant DOM element is going to be retrieved.\r\n * @param {String} [namespaceURI] - \r\n * @param {String} [localName] - \r\n * @param {Boolean} recursive \r\n * - True if the search should include all the descendants of the DOM node.  \r\n * - False if the search should be scoped only to the direct children of the DOM node.\r\n * @return The node's first descendant DOM element that matches the specified namespace URI and local name; null otherwise.\r\n */\r\nfunction xmlFirstElementMaybeRecursive(domNode, namespaceURI, localName, recursive) {\r\n\r\n    var firstElement = null;\r\n    xmlTraverse(domNode, recursive, function (child) {\r\n        if (child.nodeType === 1) {\r\n            var isExpectedNamespace = !namespaceURI || xmlNamespaceURI(child) === namespaceURI;\r\n            var isExpectedNodeName = !localName || xmlLocalName(child) === localName;\r\n\r\n            if (isExpectedNamespace && isExpectedNodeName) {\r\n                firstElement = child;\r\n            }\r\n        }\r\n        return firstElement === null;\r\n    });\r\n    return firstElement;\r\n}\r\n\r\n/** Gets the concatenated value of all immediate child text and CDATA nodes for the specified element.\r\n * @param xmlElement - Element to get values for.\r\n * @returns {String} Text for all direct children.\r\n */\r\nfunction xmlInnerText(xmlElement) {\r\n\r\n    var result = null;\r\n    var root = (xmlElement.nodeType === 9 && xmlElement.documentElement) ? xmlElement.documentElement : xmlElement;\r\n    var whitespaceAlreadyRemoved = root.ownerDocument.preserveWhiteSpace === false;\r\n    var whitespacePreserveContext;\r\n\r\n    xmlTraverse(root, false, function (child) {\r\n        if (child.nodeType === 3 || child.nodeType === 4) {\r\n            // isElementContentWhitespace indicates that this is 'ignorable whitespace',\r\n            // but it's not defined by all browsers, and does not honor xml:space='preserve'\r\n            // in some implementations.\r\n            //\r\n            // If we can't tell either way, we walk up the tree to figure out whether\r\n            // xml:space is set to preserve; otherwise we discard pure-whitespace.\r\n            //\r\n            // For example <a>  <b>1</b></a>. The space between <a> and <b> is usually 'ignorable'.\r\n            var text = xmlNodeValue(child);\r\n            var shouldInclude = whitespaceAlreadyRemoved || !isWhitespace(text);\r\n            if (!shouldInclude) {\r\n                // Walk up the tree to figure out whether we are in xml:space='preserve' context\r\n                // for the cursor (needs to happen only once).\r\n                if (whitespacePreserveContext === undefined) {\r\n                    whitespacePreserveContext = isWhitespacePreserveContext(root);\r\n                }\r\n\r\n                shouldInclude = whitespacePreserveContext;\r\n            }\r\n\r\n            if (shouldInclude) {\r\n                if (!result) {\r\n                    result = text;\r\n                } else {\r\n                    result += text;\r\n                }\r\n            }\r\n        }\r\n        // Continue traversing?\r\n        return true;\r\n    });\r\n    return result;\r\n}\r\n\r\n/** Returns the localName of a XML node.\r\n * @param domNode - DOM node to get the value from.\r\n * @returns {String} localName of domNode.\r\n */\r\nfunction xmlLocalName(domNode) {\r\n\r\n    return domNode.localName || domNode.baseName;\r\n}\r\n\r\n/** Returns the namespace URI of a XML node.\r\n * @param domNode - DOM node to get the value from.\r\n * @returns {String} Namespace URI of domNode.\r\n */\r\nfunction xmlNamespaceURI(domNode) {\r\n\r\n    return domNode.namespaceURI || null;\r\n}\r\n\r\n/** Returns the value or the inner text of a XML node.\r\n * @param domNode - DOM node to get the value from.\r\n * @return Value of the domNode or the inner text if domNode represents a DOM element node.\r\n */\r\nfunction xmlNodeValue(domNode) {\r\n    \r\n    if (domNode.nodeType === 1) {\r\n        return xmlInnerText(domNode);\r\n    }\r\n    return domNode.nodeValue;\r\n}\r\n\r\n/** Walks through the descendants of the domNode and invokes a callback for each node.\r\n * @param domNode - DOM node whose descendants are going to be traversed.\r\n * @param {Boolean} recursive\r\n * - True if the traversal should include all the descenants of the DOM node.\r\n * - False if the traversal should be scoped only to the direct children of the DOM node.\r\n * @param {Boolean} onChildCallback - Called for each child\r\n * @returns {String} Namespace URI of node.\r\n */\r\nfunction xmlTraverse(domNode, recursive, onChildCallback) {\r\n\r\n    var subtrees = [];\r\n    var child = domNode.firstChild;\r\n    var proceed = true;\r\n    while (child && proceed) {\r\n        proceed = onChildCallback(child);\r\n        if (proceed) {\r\n            if (recursive && child.firstChild) {\r\n                subtrees.push(child.firstChild);\r\n            }\r\n            child = child.nextSibling || subtrees.shift();\r\n        }\r\n    }\r\n}\r\n\r\n/** Returns the next sibling DOM element of the specified DOM node.\r\n * @param domNode - DOM node from which the next sibling is going to be retrieved.\r\n * @param {String} [namespaceURI] - \r\n * @param {String} [localName] - \r\n * @return The node's next sibling DOM element, null if there is none.\r\n */\r\nfunction xmlSiblingElement(domNode, namespaceURI, localName) {\r\n\r\n    var sibling = domNode.nextSibling;\r\n    while (sibling) {\r\n        if (sibling.nodeType === 1) {\r\n            var isExpectedNamespace = !namespaceURI || xmlNamespaceURI(sibling) === namespaceURI;\r\n            var isExpectedNodeName = !localName || xmlLocalName(sibling) === localName;\r\n\r\n            if (isExpectedNamespace && isExpectedNodeName) {\r\n                return sibling;\r\n            }\r\n        }\r\n        sibling = sibling.nextSibling;\r\n    }\r\n    return null;\r\n}\r\n\r\n/** Creates a new empty DOM document node.\r\n * @return New DOM document node.\r\n *\r\n * This function will first try to create a native DOM document using\r\n * the browsers createDocument function.  If the browser doesn't\r\n * support this but supports ActiveXObject, then an attempt to create\r\n * an MSXML 6.0 DOM will be made. If this attempt fails too, then an attempt\r\n * for creating an MXSML 3.0 DOM will be made.  If this last attemp fails or\r\n * the browser doesn't support ActiveXObject then an exception will be thrown.\r\n */\r\nfunction xmlDom() {\r\n    var implementation = window.document.implementation;\r\n    return (implementation && implementation.createDocument) ?\r\n       implementation.createDocument(null, null, null) :\r\n       msXmlDom();\r\n}\r\n\r\n/** Appends a collection of child nodes or string values to a parent DOM node.\r\n * @param parent - DOM node to which the children will be appended.\r\n * @param {Array} children - Array containing DOM nodes or string values that will be appended to the parent.\r\n * @return The parent with the appended children or string values.\r\n *  If a value in the children collection is a string, then a new DOM text node is going to be created\r\n *  for it and then appended to the parent.\r\n */\r\nfunction xmlAppendChildren(parent, children) {\r\n    if (!isArray(children)) {\r\n        return xmlAppendChild(parent, children);\r\n    }\r\n\r\n    var i, len;\r\n    for (i = 0, len = children.length; i < len; i++) {\r\n        children[i] && xmlAppendChild(parent, children[i]);\r\n    }\r\n    return parent;\r\n}\r\n\r\n/** Appends a child node or a string value to a parent DOM node.\r\n * @param parent - DOM node to which the child will be appended.\r\n * @param child - Child DOM node or string value to append to the parent.\r\n * @return The parent with the appended child or string value.\r\n * If child is a string value, then a new DOM text node is going to be created\r\n * for it and then appended to the parent.\r\n */\r\nfunction xmlAppendChild(parent, child) {\r\n\r\n    djsassert(parent !== child, \"xmlAppendChild() - parent and child are one and the same!\");\r\n    if (child) {\r\n        if (typeof child === \"string\") {\r\n            return xmlAppendText(parent, xmlNewText(parent.ownerDocument, child));\r\n        }\r\n        if (child.nodeType === 2) {\r\n            parent.setAttributeNodeNS ? parent.setAttributeNodeNS(child) : parent.setAttributeNode(child);\r\n        } else {\r\n            parent.appendChild(child);\r\n        }\r\n    }\r\n    return parent;\r\n}\r\n\r\n/** Creates a new DOM attribute node.\r\n * @param dom - DOM document used to create the attribute.\r\n * @param {String} namespaceURI - Namespace URI.\r\n * @param {String} qualifiedName - Qualified OData name\r\n * @param {String} value - Value of the new attribute\r\n * @return DOM attribute node for the namespace declaration.\r\n */\r\nfunction xmlNewAttribute(dom, namespaceURI, qualifiedName, value) {\r\n\r\n    var attribute =\r\n        dom.createAttributeNS && dom.createAttributeNS(namespaceURI, qualifiedName) ||\r\n        dom.createNode(2, qualifiedName, namespaceURI || undefined);\r\n\r\n    attribute.value = value || \"\";\r\n    return attribute;\r\n}\r\n\r\n/** Creates a new DOM element node.\r\n * @param dom - DOM document used to create the DOM element.\r\n * @param {String} namespaceURI - Namespace URI of the new DOM element.\r\n * @param {String} qualifiedName - Qualified name in the form of \"prefix:name\" of the new DOM element.\r\n * @param {Array} [children] Collection of child DOM nodes or string values that are going to be appended to the new DOM element.\r\n * @return New DOM element.\r\n * If a value in the children collection is a string, then a new DOM text node is going to be created\r\n * for it and then appended to the new DOM element.\r\n */\r\nfunction xmlNewElement(dom, namespaceURI, qualifiedName, children) {\r\n    var element =\r\n        dom.createElementNS && dom.createElementNS(nampespaceURI, qualifiedName) ||\r\n        dom.createNode(1, qualifiedName, nampespaceURI || undefined);\r\n\r\n    return xmlAppendChildren(element, children || []);\r\n}\r\n\r\n/** Creates a namespace declaration attribute.\r\n * @param dom - DOM document used to create the attribute.\r\n * @param {String} namespaceURI - Namespace URI.\r\n * @param {String} prefix - Namespace prefix.\r\n * @return DOM attribute node for the namespace declaration.\r\n */\r\nfunction xmlNewNSDeclaration(dom, namespaceURI, prefix) {\r\n    return xmlNewAttribute(dom, xmlnsNS, xmlQualifiedName(\"xmlns\", prefix), namespaceURI);\r\n}\r\n\r\n/** Creates a new DOM document fragment node for the specified xml text.\r\n * @param dom - DOM document from which the fragment node is going to be created.\r\n * @param {String} text XML text to be represented by the XmlFragment.\r\n * @return New DOM document fragment object.\r\n */\r\nfunction xmlNewFragment(dom, text) {\r\n\r\n    var value = \"<c>\" + text + \"</c>\";\r\n    var tempDom = xmlParse(value);\r\n    var tempRoot = tempDom.documentElement;\r\n    var imported = (\"importNode\" in dom) ? dom.importNode(tempRoot, true) : tempRoot;\r\n    var fragment = dom.createDocumentFragment();\r\n\r\n    var importedChild = imported.firstChild;\r\n    while (importedChild) {\r\n        fragment.appendChild(importedChild);\r\n        importedChild = importedChild.nextSibling;\r\n    }\r\n    return fragment;\r\n}\r\n\r\n/** Creates new DOM text node.\r\n * @param dom - DOM document used to create the text node.\r\n * @param {String} text - Text value for the DOM text node.\r\n * @return DOM text node.\r\n */ \r\nfunction xmlNewText(dom, text) {\r\n    return dom.createTextNode(text);\r\n}\r\n\r\n/** Creates a new DOM element or DOM attribute node as specified by path and appends it to the DOM tree pointed by root.\r\n * @param dom - DOM document used to create the new node.\r\n * @param root - DOM element node used as root of the subtree on which the new nodes are going to be created.\r\n * @param {String} namespaceURI - Namespace URI of the new DOM element or attribute.\r\n * @param {String} prefix - Prefix used to qualify the name of the new DOM element or attribute.\r\n * @param {String} path - Path string describing the location of the new DOM element or attribute from the root element.\r\n * @return DOM element or attribute node for the last segment of the path.\r\n\r\n * This function will traverse the path and will create a new DOM element with the specified namespace URI and prefix\r\n * for each segment that doesn't have a matching element under root.\r\n * The last segment of the path may be decorated with a starting @ character. In this case a new DOM attribute node\r\n * will be created.\r\n */\r\nfunction xmlNewNodeByPath(dom, root, namespaceURI, prefix, path) {\r\n    var name = \"\";\r\n    var parts = path.split(\"/\");\r\n    var xmlFindNode = xmlFirstChildElement;\r\n    var xmlNewNode = xmlNewElement;\r\n    var xmlNode = root;\r\n\r\n    var i, len;\r\n    for (i = 0, len = parts.length; i < len; i++) {\r\n        name = parts[i];\r\n        if (name.charAt(0) === \"@\") {\r\n            name = name.substring(1);\r\n            xmlFindNode = xmlAttributeNode;\r\n            xmlNewNode = xmlNewAttribute;\r\n        }\r\n\r\n        var childNode = xmlFindNode(xmlNode, namespaceURI, name);\r\n        if (!childNode) {\r\n            childNode = xmlNewNode(dom, namespaceURI, xmlQualifiedName(prefix, name));\r\n            xmlAppendChild(xmlNode, childNode);\r\n        }\r\n        xmlNode = childNode;\r\n    }\r\n    return xmlNode;\r\n}\r\n\r\n/** Returns the text representation of the document to which the specified node belongs.\r\n * @param domNode - Wrapped element in the document to serialize.\r\n * @returns {String} Serialized document.\r\n*/\r\nfunction xmlSerialize(domNode) {\r\n    var xmlSerializer = window.XMLSerializer;\r\n    if (xmlSerializer) {\r\n        var serializer = new xmlSerializer();\r\n        return serializer.serializeToString(domNode);\r\n    }\r\n\r\n    if (domNode.xml) {\r\n        return domNode.xml;\r\n    }\r\n\r\n    throw { message: \"XML serialization unsupported\" };\r\n}\r\n\r\n/** Returns the XML representation of the all the descendants of the node.\r\n * @param domNode - Node to serialize.\r\n * @returns {String} The XML representation of all the descendants of the node.\r\n */\r\nfunction xmlSerializeDescendants(domNode) {\r\n    var children = domNode.childNodes;\r\n    var i, len = children.length;\r\n    if (len === 0) {\r\n        return \"\";\r\n    }\r\n\r\n    // Some implementations of the XMLSerializer don't deal very well with fragments that\r\n    // don't have a DOMElement as their first child. The work around is to wrap all the\r\n    // nodes in a dummy root node named \"c\", serialize it and then just extract the text between\r\n    // the <c> and the </c> substrings.\r\n\r\n    var dom = domNode.ownerDocument;\r\n    var fragment = dom.createDocumentFragment();\r\n    var fragmentRoot = dom.createElement(\"c\");\r\n\r\n    fragment.appendChild(fragmentRoot);\r\n    // Move the children to the fragment tree.\r\n    for (i = 0; i < len; i++) {\r\n        fragmentRoot.appendChild(children[i]);\r\n    }\r\n\r\n    var xml = xmlSerialize(fragment);\r\n    xml = xml.substr(3, xml.length - 7);\r\n\r\n    // Move the children back to the original dom tree.\r\n    for (i = 0; i < len; i++) {\r\n        domNode.appendChild(fragmentRoot.childNodes[i]);\r\n    }\r\n\r\n    return xml;\r\n}\r\n\r\n/** Returns the XML representation of the node and all its descendants.\r\n * @param domNode - Node to serialize\r\n * @returns {String} The XML representation of the node and all its descendants.\r\n */\r\nfunction xmlSerializeNode(domNode) {\r\n\r\n    var xml = domNode.xml;\r\n    if (xml !== undefined) {\r\n        return xml;\r\n    }\r\n\r\n    if (window.XMLSerializer) {\r\n        var serializer = new window.XMLSerializer();\r\n        return serializer.serializeToString(domNode);\r\n    }\r\n\r\n    throw { message: \"XML serialization unsupported\" };\r\n}\r\n\r\nexports.http = http;\r\nexports.w3org = w3org;\r\nexports.xmlNS = xmlNS;\r\nexports.xmlnsNS = xmlnsNS;\r\n\r\nexports.hasLeadingOrTrailingWhitespace = hasLeadingOrTrailingWhitespace;\r\nexports.isXmlNSDeclaration = isXmlNSDeclaration;\r\nexports.xmlAppendChild = xmlAppendChild;\r\nexports.xmlAppendChildren = xmlAppendChildren;\r\nexports.xmlAttributeNode = xmlAttributeNode;\r\nexports.xmlAttributes = xmlAttributes;\r\nexports.xmlAttributeValue = xmlAttributeValue;\r\nexports.xmlBaseURI = xmlBaseURI;\r\nexports.xmlChildElements = xmlChildElements;\r\nexports.xmlFindElementByPath = xmlFindElementByPath;\r\nexports.xmlFindNodeByPath = xmlFindNodeByPath;\r\nexports.xmlFirstChildElement = xmlFirstChildElement;\r\nexports.xmlFirstDescendantElement = xmlFirstDescendantElement;\r\nexports.xmlInnerText = xmlInnerText;\r\nexports.xmlLocalName = xmlLocalName;\r\nexports.xmlNamespaceURI = xmlNamespaceURI;\r\nexports.xmlNodeValue = xmlNodeValue;\r\nexports.xmlDom = xmlDom;\r\nexports.xmlNewAttribute = xmlNewAttribute;\r\nexports.xmlNewElement = xmlNewElement;\r\nexports.xmlNewFragment = xmlNewFragment;\r\nexports.xmlNewNodeByPath = xmlNewNodeByPath;\r\nexports.xmlNewNSDeclaration = xmlNewNSDeclaration;\r\nexports.xmlNewText = xmlNewText;\r\nexports.xmlParse = xmlParse;\r\nexports.xmlQualifiedName = xmlQualifiedName;\r\nexports.xmlSerialize = xmlSerialize;\r\nexports.xmlSerializeDescendants = xmlSerializeDescendants;\r\nexports.xmlSiblingElement = xmlSiblingElement;\r\n}};\r\n\r\nvar modules = {};\r\n\r\nvar require = function(path) {\r\n    var name = path.substring(path.lastIndexOf('/')+1,path.length-3);\r\n    if (modules[name]) { return modules[name].exports; }\r\n\r\n    modules[name] = { exports : {}};\r\n    console.log(name);\r\n    if (name === 'sou') {\r\n      var i = 0;\r\n    }\r\n    datas[name].call(this,modules[name].exports,modules[name],require);\r\n    return modules[name].exports;\r\n  };\r\n\r\nwindow.odatajs = {};\r\ninit.call(this,window.odatajs,window.odatajs,require);\r\n\r\n\r\n"]}