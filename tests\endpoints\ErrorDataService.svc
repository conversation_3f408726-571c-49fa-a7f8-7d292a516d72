<!--
/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing,
 * software distributed under the License is distributed on an
 * "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 * KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations
 * under the License.
 */
-->

<%@ ServiceHost Language="C#" Factory="Microsoft.OData.Service.DataServiceHostFactory, Microsoft.OData.Service, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35"
    Service="DataJS.Tests.ErrorDataService" %>



namespace DataJS.Tests
{
    using System;
    using System.Collections.Generic;
    using Microsoft.OData.Service;
    using System.Linq;

    /// <summary>
    /// A data service that contains in-stream errors
    /// </summary>
    public class ErrorDataService : DataService<ErrorDataSource>
    {
        // This method is called only once to initialize service-wide policies.
        public static void InitializeService(DataServiceConfiguration config)
        {
            config.SetEntitySetAccessRule("*", EntitySetRights.All);
            config.DataServiceBehavior.MaxProtocolVersion = Microsoft.OData.Client.ODataProtocolVersion.V4;
        }
    }

    public class ErrorDataSource
    {
        public IQueryable<ErrorType> Entities
        {
            get
            {
                ErrorType[] entities = new ErrorType[]
                {
                    new ErrorType(() => 0),
                    new ErrorType(() => { throw new ApplicationException(); })
                };

                return entities.AsQueryable();
            }
        }
    }
    
    public class ErrorType
    {
        Func<int> generateID;
        
        public ErrorType(Func<int> generateID)
        {
            this.generateID = generateID;
        }
        
        public int ID
        {
            get { return this.generateID(); }
        }
    }
}