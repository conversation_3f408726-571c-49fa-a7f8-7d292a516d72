// 简单测试文件 - 使用 CommonJS 方式加载模块
// 临时将 package.json 改回 CommonJS 模式来测试

console.log('开始测试 odatajs 库...');

try {
    // 由于目前还是 CommonJS 格式，我们需要先改回去测试
    console.log('当前项目结构:');
    console.log('- lib/utils.js (部分转换为 ES 模块)');
    console.log('- lib/xml.js (CommonJS)');
    console.log('- lib/deferred.js (CommonJS)');
    console.log('- lib/cache.js (CommonJS)');
    console.log('- lib/odata.js (CommonJS)');
    console.log('- lib/store.js (CommonJS)');
    
    console.log('\n项目状态: 需要完成 ES 模块转换才能正常启动');
    console.log('建议: 先完成所有模块的 ES 模块转换，然后再启动项目');
    
} catch (error) {
    console.error('测试失败:', error.message);
}
