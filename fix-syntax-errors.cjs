// 修复转换过程中产生的语法错误
const fs = require('fs');
const path = require('path');

// 需要修复的文件列表
const filesToFix = [
    './lib/odata/odatautils.js',
    './lib/odata/handler.js',
    './lib/odata/json.js',
    './lib/odata/metadata.js',
    './lib/odata/net.js',
    './lib/odata/net-browser.js',
    './lib/odata/batch.js',
    './lib/store.js',
    './lib/store/dom.js',
    './lib/store/indexeddb.js',
    './lib/store/memory.js',
    './lib/cache.js',
    './lib/cache/source.js'
];

function fixSyntaxErrors(filePath) {
    console.log(`修复文件: ${filePath}`);
    
    if (!fs.existsSync(filePath)) {
        console.log(`  ⚠️  文件不存在: ${filePath}`);
        return;
    }
    
    let content = fs.readFileSync(filePath, 'utf8');
    let fixCount = 0;
    
    // 修复 ;() 错误 (如 getUTCHours;() -> getUTCHours())
    const semicolonParenPattern = /(\w+);(\(\))/g;
    content = content.replace(semicolonParenPattern, (match, funcName, parens) => {
        fixCount++;
        return funcName + parens;
    });
    
    // 修复 ; || 错误 (如 value.__metadata; || {} -> value.__metadata || {})
    const semicolonOrPattern = /(\w+|\]); \|\|/g;
    content = content.replace(semicolonOrPattern, (match, varName) => {
        fixCount++;
        return varName + ' ||';
    });

    // 修复 ; = 错误 (如 const x = y; = z -> const x = y || z)
    const semicolonEqualsPattern = /(\w+|\]); = /g;
    content = content.replace(semicolonEqualsPattern, (match, varName) => {
        fixCount++;
        return varName + ' = ';
    });

    // 修复 ;. 错误 (如 context.contentType;.properties -> context.contentType.properties)
    const semicolonDotPattern = /(\w+|\]); \./g;
    content = content.replace(semicolonDotPattern, (match, varName) => {
        fixCount++;
        return varName + '.';
    });
    
    // 修复 const var = exports.xxx; = require() 错误
    const exportRequirePattern = /const\s+(\w+)\s*=\s*exports\.(\w+);\s*=\s*require\(['"]([^'"]+)['"]\);?/g;
    content = content.replace(exportRequirePattern, (match, varName, exportName, modulePath) => {
        fixCount++;
        if (!modulePath.endsWith('.js')) {
            modulePath += '.js';
        }
        return `import * as ${varName} from '${modulePath}';`;
    });
    
    // 修复剩余的 require 语句
    const requirePattern = /var\s+(\w+)\s*=\s*require\(['"]([^'"]+)['"]\);?/g;
    content = content.replace(requirePattern, (match, varName, modulePath) => {
        fixCount++;
        if (modulePath.startsWith('./') || modulePath.startsWith('../')) {
            if (!modulePath.endsWith('.js')) {
                modulePath += '.js';
            }
        }
        return `import * as ${varName} from '${modulePath}';`;
    });
    
    // 修复 exports.xxx = require() 模式
    const exportsRequirePattern = /exports\.(\w+)\s*=\s*require\(['"]([^'"]+)['"]\);?/g;
    content = content.replace(exportsRequirePattern, (match, exportName, modulePath) => {
        fixCount++;
        if (!modulePath.endsWith('.js')) {
            modulePath += '.js';
        }
        return `import * as ${exportName}Module from '${modulePath}';\nexport const ${exportName} = ${exportName}Module;`;
    });
    
    if (fixCount > 0) {
        fs.writeFileSync(filePath, content);
        console.log(`  ✅ 修复了 ${fixCount} 个语法错误`);
    } else {
        console.log(`  ℹ️  没有发现需要修复的错误`);
    }
}

// 修复所有文件
console.log('开始修复语法错误...');
filesToFix.forEach(fixSyntaxErrors);
console.log('\n🎉 语法错误修复完成！');
