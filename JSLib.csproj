﻿<?xml version="1.0" encoding="utf-8"?>
<!--
/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing,
 * software distributed under the License is distributed on an
 * "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 * KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations
 * under the License.
 */-->
<Project ToolsVersion="4.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProductVersion>
    </ProductVersion>
    <SchemaVersion>2.0</SchemaVersion>
    <ProjectGuid>{73ADF1A7-613B-4679-885B-2AE4AFAA9A6A}</ProjectGuid>
    <ProjectTypeGuids>{349c5851-65df-11da-9384-00065b846f21};{fae04ec0-301f-11d3-bf4b-00c04f79efbc}</ProjectTypeGuids>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>JSLib</RootNamespace>
    <AssemblyName>JSLib</AssemblyName>
    <TargetFrameworkVersion>v4.0</TargetFrameworkVersion>
    <SccProjectName>SAK</SccProjectName>
    <SccLocalPath>SAK</SccLocalPath>
    <SccAuxPath>SAK</SccAuxPath>
    <SccProvider>SAK</SccProvider>
    <UseIISExpress>false</UseIISExpress>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <ItemGroup>
    <Content Include="src\index.js" />
    <Content Include="src\banner.txt" />
    <Content Include="src\lib\cache.js" />
    <Content Include="src\lib\odatajs.js" />
    <Content Include="src\lib\odata.js" />
    <Content Include="src\lib\store.js" />
    <Content Include="src\lib\cache\source.js" />
    <Content Include="src\lib\odatajs\deferred.js" />
    <Content Include="src\lib\odatajs\utils.js" />
    <Content Include="src\lib\odatajs\xml.js" />
    <Content Include="src\lib\odata\batch.js" />
    <Content Include="src\lib\odata\handler.js" />
    <Content Include="src\lib\odata\json.js" />
    <Content Include="src\lib\odata\metadata.js" />
    <Content Include="src\lib\odata\net.js" />
    <Content Include="src\lib\odata\utils.js" />
    <Content Include="src\lib\store\dom.js" />
    <Content Include="src\lib\store\indexeddb.js" />
    <Content Include="src\lib\store\memory.js" />
    <Content Include="tests\cache-tests.js" />
    <Content Include="tests\common\CacheVerifier.js" />
    <Content Include="tests\common\common.js" />
    <Content Include="tests\common\Instrument.svc" />
    <Content Include="tests\common\Instrument.js" />
    <Content Include="tests\common\mockXMLHttpRequest.js" />
    <Content Include="tests\common\mockHttpClient.js" />
    <Content Include="tests\common\djstest.js" />
    <Content Include="tests\common\gpo-ie8-tour-disable.reg" />
    <Content Include="tests\common\ObservableHttpClient.js" />
    <Content Include="tests\common\ODataVerifyReader.js" />
    <Content Include="tests\common\ODataVerifyReader.svc" />
    <Content Include="tests\common\rx.js" />
    <Content Include="tests\common\TestLogger.svc" />
    <Content Include="tests\common\TestSynchronizerClient.js" />
    <Content Include="tests\e2etest\Test.html" />
    <Content Include="tests\odata-json-light-tests.js" />
    <Content Include="tests\odatajs-startup-perf-test.html" />
    <Content Include="tests\endpoints\BasicAuthDataService.svc" />
    <Content Include="tests\endpoints\FoodStoreDataServiceV4.svc" />
    <Content Include="tests\endpoints\CustomAnnotations.xml" />
    <Content Include="tests\endpoints\CustomDataService.svc" />
    <Content Include="tests\endpoints\EpmDataService.svc" />
    <Content Include="tests\endpoints\ErrorDataService.svc" />
    <Content Include="tests\endpoints\LargeCollectionService.svc" />
    <Content Include="tests\endpoints\web.config" />
    <Content Include="tests\odatajs-cache-large-collection-functional-tests.html" />
    <Content Include="tests\odatajs-cache-large-collection-functional-tests.js" />
    <Content Include="tests\odatajs-cache-long-haul-tests.html" />
    <Content Include="tests\odata-batch-functional-tests.html" />
    <Content Include="tests\odata-batch-functional-tests.js" />
    <Content Include="tests\odata-batch-tests.js" />
    <Content Include="tests\odata-cache-filter-functional-tests.html" />
    <Content Include="tests\odata-cache-filter-functional-tests.js" />
    <Content Include="tests\odata-cache-fperf-tests.html" />
    <Content Include="tests\odata-cache-fperf-tests.js" />
    <Content Include="tests\odata-cache-functional-tests.html" />
    <Content Include="tests\odata-cache-functional-tests.js" />
    <Content Include="tests\odata-cache-rx-functional-tests.html" />
    <Content Include="tests\odata-cache-rx-functional-tests.js" />
    <Content Include="tests\odata-fuzz.html" />
    <Content Include="tests\odata-metadata-tests.js" />
    <Content Include="tests\odata-handler-tests.js" />
    <Content Include="tests\odata-json-tests.js" />
    <Content Include="tests\odata-links-functional-tests.html" />
    <Content Include="tests\odata-links-functional-tests.js" />
    <Content Include="tests\odata-metadata-awareness-functional-tests.html" />
    <Content Include="tests\odata-metadata-awareness-functional-tests.js" />
    <Content Include="tests\odata-net-tests.js" />
    <Content Include="tests\odata-perf-tests.html" />
    <Content Include="tests\odata-perf-tests.js" />
    <Content Include="tests\odata-read-crossdomain-functional-tests.html" />
    <Content Include="tests\odata-read-crossdomain-functional-tests.js" />
    <Content Include="tests\odata-read-functional-tests.html" />
    <Content Include="tests\odata-request-functional-tests.html" />
    <Content Include="tests\odata-request-functional-tests.js" />
    <Content Include="tests\odata-read-functional-tests.js" />
    <Content Include="tests\odata-qunit-tests.htm" />
    <Content Include="tests\odata-tests.js" />
    <Content Include="tests\odata-xml-tests.js" />
    <Content Include="tests\run-tests.wsf" />
    <Content Include="tests\store-indexeddb-tests.js" />
    <Content Include="tests\store-tests.js" />
    <Content Include="tests\test-list.js" />
    <Content Include="tests\test-manager.html" />
    <!-- Configuration file for the web project. -->
    <Content Include="Web.config">
      <SubType>Designer</SubType>
    </Content>
  </ItemGroup>
  <ItemGroup>
    <Compile Include="tests\code\CsdlReader.cs" />
    <Compile Include="tests\code\JsDate.cs" />
    <Compile Include="tests\code\JsonObject.cs" />
    <Compile Include="tests\code\ReaderUtils.cs" />
    <Compile Include="tests\code\ReflectionDataContext.cs" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="packages.config" />
  </ItemGroup>
  <ItemGroup>
    <Reference Include="Microsoft.OData.Client, Version=6.5.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\special-packages\Microsoft.OData.Client.6.5.0\lib\net40\Microsoft.OData.Client.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.OData.Core, Version=6.5.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\special-packages\Microsoft.OData.Core.6.5.0\lib\portable-net40+sl5+wp8+win8+wpa\Microsoft.OData.Core.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.OData.Edm, Version=6.5.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\special-packages\Microsoft.OData.Edm.6.5.0\lib\portable-net40+sl5+wp8+win8+wpa\Microsoft.OData.Edm.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.OData.Service, Version=6.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\special-packages\Microsoft.OData.Service.6.5.0\lib\net40\Microsoft.OData.Service.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Spatial, Version=6.5.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\special-packages\Microsoft.Spatial.6.5.0\lib\portable-net40+sl5+wp8+win8+wpa\Microsoft.Spatial.dll</HintPath>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.Net" />
    <Reference Include="System.Runtime.Serialization" />
    <Reference Include="System.ServiceModel" />
    <Reference Include="System.ServiceModel.Activation" />
    <Reference Include="System.ServiceModel.Web" />
    <Reference Include="System.Web.Extensions" />
    <Reference Include="System.Xml" />
    <Reference Include="System.Xml.Linq" />
  </ItemGroup>
  <Import Project="$(MSBuildBinPath)\Microsoft.CSharp.targets" />
  <Import Project="$(MSBuildExtensionsPath32)\Microsoft\VisualStudio\v10.0\WebApplications\Microsoft.WebApplication.targets" />
  <ProjectExtensions>
    <VisualStudio>
      <FlavorProperties GUID="{349c5851-65df-11da-9384-00065b846f21}">
        <WebProjectProperties>
          <UseIIS>False</UseIIS>
          <AutoAssignPort>True</AutoAssignPort>
          <DevelopmentServerPort>4002</DevelopmentServerPort>
          <DevelopmentServerVPath>/</DevelopmentServerVPath>
          <IISUrl>
          </IISUrl>
          <NTLMAuthentication>False</NTLMAuthentication>
          <UseCustomServer>False</UseCustomServer>
          <CustomServerUrl>
          </CustomServerUrl>
          <SaveServerSettingsInUserFile>False</SaveServerSettingsInUserFile>
        </WebProjectProperties>
      </FlavorProperties>
    </VisualStudio>
  </ProjectExtensions>
</Project>