﻿/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing,
 * software distributed under the License is distributed on an
 * "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 * KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations
 * under the License.
*/

<%@ ServiceHost Language="C#" Debug="true" Factory="System.ServiceModel.Activation.WebScriptServiceHostFactory"
    Service="DataJS.Tests.Instrument" %>

namespace DataJS.Tests
{
    using System;
    using System.Collections.Generic;
    using System.Diagnostics;
    using System.IO;
    using System.Linq;
    using System.Runtime.Serialization;
    using System.ServiceModel;
    using System.ServiceModel.Activation;
    using System.ServiceModel.Syndication;
    using System.ServiceModel.Web;
    using System.Text;

    /// <summary>
    /// Instrumentation utilities
    /// </summary>
    [ServiceContract]
    [ServiceBehavior(IncludeExceptionDetailInFaults = true)]
    [AspNetCompatibilityRequirements(RequirementsMode = AspNetCompatibilityRequirementsMode.Allowed)]
    public class Instrument
    {
        static readonly Dictionary<string, string> userAgents = new Dictionary<string,string>
        {
            { "MSIE", "iexplore" },
            { "Firefox", "firefox" },
            { "Chrome", "chrome" },
            { "Safari", "safari" }
        };
        
        /// <summary>
        /// Gets the memory size used by the browser
        /// </summary>
        /// <returns>The memory size used by the browser (in bytes), or zero if browser is not supported</returns>
        [OperationContract]
        [WebGet]
        public Stream GetBrowserMemorySize()
        {
            string userAgentString = WebOperationContext.Current.IncomingRequest.UserAgent;
            string userAgentKey = Instrument.userAgents.Keys.FirstOrDefault(ua => userAgentString.Contains(ua));

            if (userAgentKey != null)
            {
                string processName = userAgents[userAgentKey];
                long totalMemory = Process.GetProcessesByName(processName).Select(p => p.WorkingSet64).Sum();
                
                return new MemoryStream(Encoding.UTF8.GetBytes(totalMemory.ToString()));
            }
            else
            {
                return new MemoryStream(Encoding.UTF8.GetBytes("0"));
            }
        }
    }
}