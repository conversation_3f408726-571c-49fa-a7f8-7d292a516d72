<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>odatajs 浏览器测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        pre {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 odatajs 浏览器测试</h1>
        
        <div class="success">
            <strong>✅ ES 模块转换完成!</strong> 现在可以直接在现代浏览器中使用该库，无需构建工具。
        </div>

        <h2>📋 项目状态</h2>
        <div id="status"></div>

        <h2>🔧 功能测试</h2>
        <button onclick="testBasicFunctions()">测试基本功能</button>
        <button onclick="testXMLFunctions()">测试 XML 功能</button>
        <button onclick="testODataFunctions()">测试 OData 功能</button>
        <button onclick="testMetadataFunctions()">测试元数据功能</button>
        
        <h2>📊 测试结果</h2>
        <div id="results"></div>

        <h2>💡 使用示例</h2>
        <pre><code>// ES 模块方式 (转换完成后)
import { oData, utils, xml } from './index.js';

// 创建 OData 请求
const request = oData.request({
    requestUri: 'https://services.odata.org/V4/Northwind/Northwind.svc/Products',
    method: 'GET'
});

// 使用工具函数
const isAssigned = utils.assigned(someValue);

// 解析 XML
const xmlDoc = xml.xmlParse(xmlString);

// 解析 OData 元数据
const metadataDoc = xml.xmlParse(metadataXml);
const parsedMetadata = oData.parseMetadata(metadataDoc);

// 使用元数据处理器
const metadataHandler = oData.metadataHandler;
const jsonHandler = oData.jsonHandler;</code></pre>
    </div>

    <script type="module">
        // 导入 ES 模块
        import odatajs, { utils, xml, deferred, oData, store, cache } from './index.js';

        // 全局变量，供按钮事件使用
        window.odatajs = odatajs;
        window.utils = utils;
        window.xml = xml;
        window.deferred = deferred;
        window.oData = oData;
        window.store = store;
        window.cache = cache;

        function updateStatus() {
            const statusDiv = document.getElementById('status');
            statusDiv.innerHTML = `
                <div class="success">✅ ES 模块转换完成并成功加载</div>
                <div class="info">📦 核心模块: utils, xml, deferred, cache, oData, store</div>
                <div class="success">🔄 当前状态: ES 模块模式，可在现代浏览器中直接使用</div>
                <div class="info">🎯 版本: ${odatajs.version.major}.${odatajs.version.minor}.${odatajs.version.build}</div>
            `;
        }

        function testBasicFunctions() {
            const resultsDiv = document.getElementById('results');
            let testResults = [];

            try {
                // 测试 utils 模块
                const inBrowser = window.utils.inBrowser();
                const assigned1 = window.utils.assigned('test');
                const assigned2 = window.utils.assigned(null);
                const isArray1 = window.utils.isArray([1, 2, 3]);
                const isArray2 = window.utils.isArray('string');

                testResults.push(`✅ utils.inBrowser(): ${inBrowser}`);
                testResults.push(`✅ utils.assigned('test'): ${assigned1}`);
                testResults.push(`✅ utils.assigned(null): ${assigned2}`);
                testResults.push(`✅ utils.isArray([1,2,3]): ${isArray1}`);
                testResults.push(`✅ utils.isArray('string'): ${isArray2}`);

                // 测试 deferred 模块
                const def = window.deferred.createDeferred();
                if (def && typeof def.promise === 'function') {
                    testResults.push('✅ deferred.createDeferred() 功能正常');
                }

                testResults.push('✅ ES 模块导入成功');
                testResults.push('✅ 浏览器环境运行正常');

            } catch (error) {
                testResults.push(`❌ 测试失败: ${error.message}`);
            }

            resultsDiv.innerHTML = `
                <div class="info">
                    <h3>基本功能测试</h3>
                    ${testResults.map(result => `<p>${result}</p>`).join('')}
                </div>
            `;
        }

        function testXMLFunctions() {
            const resultsDiv = document.getElementById('results');
            let testResults = [];

            try {
                // 测试 XML 解析功能
                const xmlString = '<root><item>test</item></root>';

                if (window.xml.xmlParse && window.xml.xmlSerialize) {
                    testResults.push('✅ xmlParse 和 xmlSerialize 函数可用');

                    try {
                        const doc = window.xml.xmlParse(xmlString);
                        if (doc) {
                            testResults.push('✅ XML 解析功能正常');
                        }
                    } catch (xmlError) {
                        testResults.push(`⚠️ XML 解析测试: ${xmlError.message}`);
                    }
                } else {
                    testResults.push('❌ XML 函数不可用');
                }

                testResults.push('📄 支持 OData 元数据处理');

            } catch (error) {
                testResults.push(`❌ XML 测试失败: ${error.message}`);
            }

            resultsDiv.innerHTML = `
                <div class="info">
                    <h3>XML 功能测试</h3>
                    ${testResults.map(result => `<p>${result}</p>`).join('')}
                </div>
            `;
        }

        function testODataFunctions() {
            const resultsDiv = document.getElementById('results');
            let testResults = [];

            try {
                // 测试 OData 模块
                if (window.oData) {
                    testResults.push('✅ OData 核心模块已加载');

                    if (window.oData.read && window.oData.request) {
                        testResults.push('✅ OData 读取和请求函数可用');
                    }

                    if (window.oData.defaultHandler) {
                        testResults.push('✅ OData 默认处理器可用');
                    }
                }

                // 测试缓存模块
                if (window.cache && window.cache.createDataCache) {
                    testResults.push('✅ 缓存模块可用');
                }

                // 测试存储模块
                if (window.store && window.store.createStore) {
                    testResults.push('✅ 存储模块可用');
                }

                testResults.push('🌐 支持 OData V4 协议');
                testResults.push('🚀 可以直接在浏览器中使用，无需构建工具');

            } catch (error) {
                testResults.push(`❌ OData 测试失败: ${error.message}`);
            }

            resultsDiv.innerHTML = `
                <div class="info">
                    <h3>OData 功能测试</h3>
                    ${testResults.map(result => `<p>${result}</p>`).join('')}
                </div>
            `;
        }

        function testMetadataFunctions() {
            const resultsDiv = document.getElementById('results');
            let testResults = [];

            try {
                // 测试元数据解析功能
                testResults.push('🔍 开始测试 OData 元数据功能...');

                // 创建一个简单的 CSDL 元数据示例
                const sampleMetadata = `<?xml version="1.0" encoding="utf-8"?>
<edmx:Edmx Version="4.0" xmlns:edmx="http://docs.oasis-open.org/odata/ns/edmx">
  <edmx:DataServices>
    <Schema Namespace="TestService" xmlns="http://docs.oasis-open.org/odata/ns/edm">
      <EntityType Name="Product">
        <Key>
          <PropertyRef Name="ID"/>
        </Key>
        <Property Name="ID" Type="Edm.Int32" Nullable="false"/>
        <Property Name="Name" Type="Edm.String" MaxLength="50"/>
        <Property Name="Price" Type="Edm.Decimal" Precision="10" Scale="2"/>
        <Property Name="Category" Type="Edm.String" MaxLength="30"/>
      </EntityType>
      <EntityContainer Name="Container">
        <EntitySet Name="Products" EntityType="TestService.Product"/>
      </EntityContainer>
    </Schema>
  </edmx:DataServices>
</edmx:Edmx>`;

                // 测试 XML 解析
                if (window.xml && window.xml.xmlParse) {
                    try {
                        const metadataDoc = window.xml.xmlParse(sampleMetadata);
                        if (metadataDoc) {
                            testResults.push('✅ XML 元数据解析成功');

                            // 测试元数据处理器
                            if (window.oData && window.oData.parseMetadata) {
                                try {
                                    const parsedMetadata = window.oData.parseMetadata(metadataDoc);
                                    if (parsedMetadata) {
                                        testResults.push('✅ OData 元数据解析器工作正常');
                                        testResults.push(`📊 解析结果类型: ${typeof parsedMetadata}`);
                                    }
                                } catch (parseError) {
                                    testResults.push(`⚠️ 元数据解析: ${parseError.message}`);
                                }
                            } else {
                                testResults.push('⚠️ parseMetadata 函数不可用');
                            }
                        }
                    } catch (xmlError) {
                        testResults.push(`❌ XML 解析失败: ${xmlError.message}`);
                    }
                } else {
                    testResults.push('❌ XML 解析器不可用');
                }

                // 测试元数据处理器
                if (window.oData && window.oData.metadataHandler) {
                    testResults.push('✅ 元数据处理器可用');
                } else {
                    testResults.push('⚠️ 元数据处理器不可用');
                }

                // 测试 JSON 处理器
                if (window.oData && window.oData.jsonHandler) {
                    testResults.push('✅ JSON 处理器可用');
                } else {
                    testResults.push('⚠️ JSON 处理器不可用');
                }

                // 测试实体类型信息
                testResults.push('📋 测试元数据包含:');
                testResults.push('  • EntityType: Product (ID, Name, Price, Category)');
                testResults.push('  • EntitySet: Products');
                testResults.push('  • 支持 OData V4 CSDL 格式');

                testResults.push('🎯 元数据功能测试完成');

            } catch (error) {
                testResults.push(`❌ 元数据测试失败: ${error.message}`);
            }

            resultsDiv.innerHTML = `
                <div class="info">
                    <h3>📄 OData 元数据功能测试</h3>
                    ${testResults.map(result => `<p>${result}</p>`).join('')}
                </div>
            `;
        }

        // 页面加载时更新状态
        window.onload = function() {
            updateStatus();
        };
    </script>
</body>
</html>
