<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>简单 XML 测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .result {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .success { background: #d4edda; border-color: #c3e6cb; }
        .error { background: #f8d7da; border-color: #f5c6cb; }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background: #0056b3; }
    </style>
</head>
<body>
    <h1>🧪 简单 XML 测试</h1>
    
    <button onclick="testBasicXML()">测试基本 XML 解析</button>
    <button onclick="testODataXML()">测试 OData XML</button>
    <button onclick="clearResults()">清空结果</button>
    
    <div id="results"></div>

    <script type="module">
        import { xml } from './index.js';
        
        // 全局变量
        window.xmlModule = xml;
        
        console.log('XML 模块:', xml);
        console.log('xmlParse 函数:', xml.xmlParse);
        
        // 简单的 XML 示例
        window.simpleXML = `<?xml version="1.0" encoding="utf-8"?>
<root>
    <item id="1">测试项目</item>
    <item id="2">另一个项目</item>
</root>`;

        // OData 元数据示例
        window.odataXML = `<?xml version="1.0" encoding="utf-8"?>
<edmx:Edmx Version="4.0" xmlns:edmx="http://docs.oasis-open.org/odata/ns/edmx">
    <edmx:DataServices>
        <Schema Namespace="TestService" xmlns="http://docs.oasis-open.org/odata/ns/edm">
            <EntityType Name="Product">
                <Key><PropertyRef Name="ID"/></Key>
                <Property Name="ID" Type="Edm.Int32" Nullable="false"/>
                <Property Name="Name" Type="Edm.String" MaxLength="50"/>
            </EntityType>
        </Schema>
    </edmx:DataServices>
</edmx:Edmx>`;

        console.log('✅ 模块加载完成');
    </script>

    <script>
        function clearResults() {
            document.getElementById('results').innerHTML = '';
        }

        function addResult(content, type = 'result') {
            const resultsDiv = document.getElementById('results');
            const resultDiv = document.createElement('div');
            resultDiv.className = `result ${type}`;
            resultDiv.innerHTML = content;
            resultsDiv.appendChild(resultDiv);
        }

        function testBasicXML() {
            addResult('<h3>🔧 基本 XML 解析测试</h3>');
            
            console.log('开始测试基本 XML...');
            console.log('xmlModule:', window.xmlModule);
            
            if (!window.xmlModule) {
                addResult('❌ XML 模块未加载', 'error');
                return;
            }
            
            if (!window.xmlModule.xmlParse) {
                addResult('❌ xmlParse 函数不存在', 'error');
                console.log('xmlModule 内容:', Object.keys(window.xmlModule));
                return;
            }
            
            try {
                const doc = window.xmlModule.xmlParse(window.simpleXML);
                if (doc) {
                    addResult('✅ 基本 XML 解析成功', 'success');
                    addResult(`📊 文档类型: ${doc.constructor.name}`);
                    
                    const rootElement = doc.documentElement || doc;
                    if (rootElement) {
                        addResult(`📋 根元素: ${rootElement.nodeName || rootElement.tagName}`);
                        
                        const items = rootElement.getElementsByTagName('item');
                        addResult(`📝 找到 ${items.length} 个 item 元素`);
                    }
                } else {
                    addResult('❌ XML 解析返回空结果', 'error');
                }
            } catch (error) {
                addResult(`❌ XML 解析失败: ${error.message}`, 'error');
                console.error('XML 解析错误:', error);
            }
        }

        function testODataXML() {
            addResult('<h3>📄 OData XML 解析测试</h3>');
            
            if (!window.xmlModule || !window.xmlModule.xmlParse) {
                addResult('❌ XML 解析器不可用', 'error');
                return;
            }
            
            try {
                const doc = window.xmlModule.xmlParse(window.odataXML);
                if (doc) {
                    addResult('✅ OData XML 解析成功', 'success');
                    
                    const rootElement = doc.documentElement || doc;
                    addResult(`📋 根元素: ${rootElement.nodeName || rootElement.tagName}`);
                    
                    // 查找 EntityType
                    const entityTypes = doc.getElementsByTagName('EntityType');
                    addResult(`🏗️ 找到 ${entityTypes.length} 个 EntityType`);
                    
                    if (entityTypes.length > 0) {
                        const entityType = entityTypes[0];
                        const name = entityType.getAttribute('Name');
                        addResult(`📝 实体类型名称: ${name}`);
                        
                        const properties = entityType.getElementsByTagName('Property');
                        addResult(`📊 属性数量: ${properties.length}`);
                    }
                    
                    addResult('🎯 OData XML 解析完成', 'success');
                } else {
                    addResult('❌ OData XML 解析返回空结果', 'error');
                }
            } catch (error) {
                addResult(`❌ OData XML 解析失败: ${error.message}`, 'error');
                console.error('OData XML 解析错误:', error);
            }
        }
    </script>
</body>
</html>
