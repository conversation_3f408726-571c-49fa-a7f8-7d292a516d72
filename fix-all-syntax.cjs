// 全面修复所有语法错误
const fs = require('fs');

// 需要修复的文件列表
const jsFiles = [
    './lib/utils.js',
    './lib/xml.js',
    './lib/deferred.js',
    './lib/cache.js',
    './lib/cache/source.js',
    './lib/odata.js',
    './lib/odata/batch.js',
    './lib/odata/handler.js',
    './lib/odata/json.js',
    './lib/odata/metadata.js',
    './lib/odata/net.js',
    './lib/odata/net-browser.js',
    './lib/odata/odatautils.js',
    './lib/store.js',
    './lib/store/dom.js',
    './lib/store/indexeddb.js',
    './lib/store/memory.js',
    './index.js',
    './index-browser.js'
];

function fixAllSyntaxErrors(filePath) {
    console.log(`检查文件: ${filePath}`);
    
    if (!fs.existsSync(filePath)) {
        console.log(`  ⚠️  文件不存在: ${filePath}`);
        return;
    }
    
    let content = fs.readFileSync(filePath, 'utf8');
    let fixCount = 0;
    const originalContent = content;
    
    // 1. 修复 ;() 错误
    content = content.replace(/(\w+);(\(\))/g, (match, funcName, parens) => {
        fixCount++;
        return funcName + parens;
    });
    
    // 2. 修复 ;. 错误
    content = content.replace(/(\w+|\]|\));(\.[a-zA-Z_$][\w$]*)/g, (match, varName, property) => {
        fixCount++;
        return varName + property;
    });
    
    // 3. 修复 ; = 错误
    content = content.replace(/(\w+|\]|\)); = /g, (match, varName) => {
        fixCount++;
        return varName + ' = ';
    });
    
    // 4. 修复 ; || 错误
    content = content.replace(/(\w+|\]|\)); \|\|/g, (match, varName) => {
        fixCount++;
        return varName + ' ||';
    });
    
    // 5. 修复错误的 const/var 声明
    content = content.replace(/const\s+(\w+)\s*=\s*exports\.(\w+);\s*=\s*require\(['"]([^'"]+)['"]\);?/g, (match, varName, exportName, modulePath) => {
        fixCount++;
        if (!modulePath.endsWith('.js')) {
            modulePath += '.js';
        }
        return `import * as ${varName} from '${modulePath}';`;
    });
    
    // 6. 修复剩余的 require 语句
    content = content.replace(/var\s+(\w+)\s*=\s*require\(['"]([^'"]+)['"]\);?/g, (match, varName, modulePath) => {
        if (modulePath.startsWith('./') || modulePath.startsWith('../')) {
            if (!modulePath.endsWith('.js')) {
                modulePath += '.js';
            }
        }
        fixCount++;
        return `import * as ${varName} from '${modulePath}';`;
    });
    
    // 7. 修复 exports.xxx = require() 模式
    content = content.replace(/exports\.(\w+)\s*=\s*require\(['"]([^'"]+)['"]\);?/g, (match, exportName, modulePath) => {
        if (!modulePath.endsWith('.js')) {
            modulePath += '.js';
        }
        fixCount++;
        return `import * as ${exportName}Module from '${modulePath}';\nexport const ${exportName} = ${exportName}Module;`;
    });
    
    if (fixCount > 0) {
        fs.writeFileSync(filePath, content);
        console.log(`  ✅ 修复了 ${fixCount} 个语法错误`);
    } else {
        console.log(`  ℹ️  没有发现需要修复的错误`);
    }
}

// 修复所有文件
console.log('开始全面修复语法错误...');
jsFiles.forEach(fixAllSyntaxErrors);
console.log('\n🎉 全面语法错误修复完成！');
