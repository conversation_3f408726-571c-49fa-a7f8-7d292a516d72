// 转换 xml.js 为 ES 模块
const fs = require('fs');

// 读取文件
let content = fs.readFileSync('./lib/xml.js', 'utf8');

// 获取所有需要导出的函数名
const exportMatches = content.match(/^exports\.(\w+)\s*=\s*(\w+);$/gm);
const exportedFunctions = [];

if (exportMatches) {
    exportMatches.forEach(match => {
        const funcName = match.match(/^exports\.(\w+)/)[1];
        exportedFunctions.push(funcName);
    });
}

console.log('找到的导出函数:', exportedFunctions.length);

// 转换函数定义为 export
exportedFunctions.forEach(funcName => {
    // 匹配 function funcName( 的模式
    const functionPattern = new RegExp(`^function\\s+${funcName}\\s*\\(`, 'gm');
    content = content.replace(functionPattern, `export function ${funcName}(`);
    
    // 匹配 var funcName = function( 的模式  
    const varPattern = new RegExp(`^var\\s+${funcName}\\s*=\\s*function\\s*\\(`, 'gm');
    content = content.replace(varPattern, `export const ${funcName} = function(`);
});

// 移除末尾的 exports 语句
const exportsPattern = /^exports\.\w+\s*=\s*\w+;$/gm;
content = content.replace(exportsPattern, '');

// 清理多余的空行
content = content.replace(/\n\s*\n\s*\n/g, '\n\n');

// 写回文件
fs.writeFileSync('./lib/xml.js', content);

console.log('✅ xml.js 转换完成！');
console.log('已转换的函数:', exportedFunctions.length);
console.log('已移除 exports 语句');
