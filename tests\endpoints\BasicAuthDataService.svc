<%@ ServiceHost Language="C#" Factory="Microsoft.OData.Service.DataServiceHostFactory, Microsoft.OData.Service, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35"
    Service="DataJS.Tests.BasicAuthDataService" %>
/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing,
 * software distributed under the License is distributed on an
 * "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 * KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations
 * under the License.
 */
namespace DataJS.Tests
{
    using System;
    using System.Collections.Generic;
    using Microsoft.OData.Service;
    using System.Linq;
    using System.ServiceModel;
    using System.ServiceModel.Web;
    using System.Text;
    using System.Web;

    [ServiceBehavior(IncludeExceptionDetailInFaults = true)]
    public class BasicAuthDataService : DataService<BasicAuthDataSource>
    {
        const string Username = "djsUser";
        const string Password = "djsPassword";
        
        // This method is called only once to initialize service-wide policies.
        public static void InitializeService(DataServiceConfiguration config)
        {
            config.SetEntitySetAccessRule("*", EntitySetRights.All);
            config.SetServiceOperationAccessRule("*", ServiceOperationRights.All);
            config.DataServiceBehavior.MaxProtocolVersion = Microsoft.OData.Client.ODataProtocolVersion.V4;
            config.UseVerboseErrors = true;
        }

        public BasicAuthDataService()
            : base()
        {
            this.ProcessingPipeline.ProcessingRequest += OnRequest;
        }

        [WebInvoke]
        public void ResetData()
        {
            this.CurrentDataSource.ResetData();
        }

        private static void UnauthorizedRequest(DataServiceOperationContext context)
        {
            context.ResponseHeaders["WWW-Authenticate"] = "Basic realm=\"DataJS.Tests\"";
            throw new DataServiceException(401, "401 Unauthorized");
        }

        private void OnRequest(object sender, DataServiceProcessingPipelineEventArgs e)
        {
            string authHeader = e.OperationContext.RequestHeaders["Authorization"];
            
            // Validate the Authorization header
            if (authHeader == null || !authHeader.StartsWith("Basic"))
            {
                UnauthorizedRequest(e.OperationContext);
            }

            // Decode the username and password from the header
            string base64Credentials = authHeader.Substring(6);
            string[] credentials = Encoding.ASCII.GetString(Convert.FromBase64String(base64Credentials)).Split(':');
            if (credentials.Length != 2 || !(credentials[0].Equals(Username) && credentials[1].Equals(Password)))
            {
                UnauthorizedRequest(e.OperationContext);
            }
        }
    }

    public class BasicAuthDataSource : ReflectionDataContext, IUpdatable
    {
        private static bool dataInitialized;

        public IQueryable<Customer> Customers
        {
            get { return this.GetResourceSetEntities<Customer>("Customers").AsQueryable(); }
        }

        public void ResetData()
        {
            this.ClearData();

            IList<Customer> customers = this.GetResourceSetEntities<Customer>("Customers");
            foreach (int i in Enumerable.Range(1, 16))
            {
                customers.Add(new Customer()
                {
                    ID = i,
                    Name = "Customer " + i
                });
            }
        }

        protected override void EnsureDataIsInitialized()
        {
            if (!dataInitialized)
            {
                this.ResetData();
                dataInitialized = true;
            }
        }
    }

    public class Customer
    {
        public int ID { get; set; }
        public string Name { get; set; }
    }
}