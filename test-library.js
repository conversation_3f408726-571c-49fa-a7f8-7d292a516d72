// 测试 odatajs 库的基本功能
const odatajs = require('./index.js');

console.log('🚀 odatajs 库测试开始...\n');

// 测试版本信息
console.log('📋 版本信息:');
console.log(`   主版本: ${odatajs.version.major}`);
console.log(`   次版本: ${odatajs.version.minor}`);
console.log(`   构建版本: ${odatajs.version.build}`);
console.log(`   完整版本: ${odatajs.version.major}.${odatajs.version.minor}.${odatajs.version.build}\n`);

// 测试可用模块
console.log('📦 可用模块:');
const modules = ['deferred', 'utils', 'xml', 'oData', 'store', 'cache'];
modules.forEach(module => {
    if (odatajs[module]) {
        console.log(`   ✅ ${module} - 已加载`);
    } else {
        console.log(`   ❌ ${module} - 未找到`);
    }
});

// 测试 utils 模块的一些基本功能
console.log('\n🔧 utils 模块测试:');
if (odatajs.utils) {
    try {
        // 测试 inBrowser 函数
        const inBrowser = odatajs.utils.inBrowser();
        console.log(`   inBrowser(): ${inBrowser}`);
        
        // 测试 assigned 函数
        const assigned1 = odatajs.utils.assigned('test');
        const assigned2 = odatajs.utils.assigned(null);
        console.log(`   assigned('test'): ${assigned1}`);
        console.log(`   assigned(null): ${assigned2}`);
        
        console.log('   ✅ utils 模块基本功能正常');
    } catch (error) {
        console.log(`   ❌ utils 模块测试失败: ${error.message}`);
    }
}

// 测试 XML 模块
console.log('\n📄 XML 模块测试:');
if (odatajs.xml) {
    try {
        // 检查 XML 模块是否有基本的解析功能
        if (typeof odatajs.xml.xmlParse === 'function') {
            console.log('   ✅ xmlParse 函数可用');
        }
        if (typeof odatajs.xml.xmlSerialize === 'function') {
            console.log('   ✅ xmlSerialize 函数可用');
        }
        console.log('   ✅ XML 模块加载成功');
    } catch (error) {
        console.log(`   ❌ XML 模块测试失败: ${error.message}`);
    }
}

console.log('\n🎉 测试完成！项目已成功启动并可以正常使用。');
console.log('\n💡 下一步建议:');
console.log('   1. 如需转换为 ES 模块，请继续模块转换工作');
console.log('   2. 可以开始使用 odatajs 进行 OData 客户端开发');
console.log('   3. 参考 README.md 了解更多使用方法');
