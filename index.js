/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing,
 * software distributed under the License is distributed on an
 * "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 * KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations
 * under the License.
 */

import * as deferred from './lib/deferred.js';
import * as utils from './lib/utils.js';
import * as xml from './lib/xml.js';
import * as oData from './lib/odata.js';
import * as store from './lib/store.js';
import * as cache from './lib/cache.js';

const odatajs = {
    version: {
        major: 4,
        minor: 0,
        build: 0
    },
    deferred,
    utils,
    xml,
    oData,
    store,
    cache
};

if (typeof window !== 'undefined') {
    //expose to browsers window object
    window.odatajs = odatajs;
}

// ES module export
export default odatajs;
export { deferred, utils, xml, oData, store, cache };
