<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>OData 元数据功能演示</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        pre {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 4px;
            overflow-x: auto;
            border: 1px solid #dee2e6;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
            font-size: 14px;
        }
        button:hover {
            background: #0056b3;
        }
        .metadata-output {
            max-height: 400px;
            overflow-y: auto;
            border: 1px solid #dee2e6;
            padding: 10px;
            background: #f8f9fa;
        }
        .highlight {
            background-color: #fff3cd;
            padding: 2px 4px;
            border-radius: 3px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>📄 OData 元数据功能演示</h1>
        
        <div class="success">
            <strong>✅ ES 模块版本</strong> - 直接在浏览器中运行，无需构建工具
        </div>

        <h2>🎯 功能测试</h2>
        <button onclick="checkModules()">检查模块状态</button>
        <button onclick="testXMLParsing()">测试 XML 解析</button>
        <button onclick="testMetadataParsing()">测试元数据解析</button>
        <button onclick="testEntityTypes()">分析实体类型</button>
        <button onclick="clearResults()">清空结果</button>
        
        <h2>📊 测试结果</h2>
        <div id="results"></div>

        <h2>📋 示例元数据</h2>
        <pre><code id="sample-metadata">&lt;?xml version="1.0" encoding="utf-8"?&gt;
&lt;edmx:Edmx Version="4.0" xmlns:edmx="http://docs.oasis-open.org/odata/ns/edmx"&gt;
  &lt;edmx:DataServices&gt;
    &lt;Schema Namespace="NorthwindModel" xmlns="http://docs.oasis-open.org/odata/ns/edm"&gt;
      &lt;EntityType Name="Product"&gt;
        &lt;Key&gt;
          &lt;PropertyRef Name="ProductID"/&gt;
        &lt;/Key&gt;
        &lt;Property Name="ProductID" Type="Edm.Int32" Nullable="false"/&gt;
        &lt;Property Name="ProductName" Type="Edm.String" MaxLength="40"/&gt;
        &lt;Property Name="UnitPrice" Type="Edm.Decimal" Precision="19" Scale="4"/&gt;
        &lt;Property Name="UnitsInStock" Type="Edm.Int16"/&gt;
        &lt;Property Name="Discontinued" Type="Edm.Boolean" Nullable="false"/&gt;
      &lt;/EntityType&gt;
      &lt;EntityType Name="Category"&gt;
        &lt;Key&gt;
          &lt;PropertyRef Name="CategoryID"/&gt;
        &lt;/Key&gt;
        &lt;Property Name="CategoryID" Type="Edm.Int32" Nullable="false"/&gt;
        &lt;Property Name="CategoryName" Type="Edm.String" MaxLength="15"/&gt;
        &lt;Property Name="Description" Type="Edm.String"/&gt;
      &lt;/EntityType&gt;
      &lt;EntityContainer Name="NorthwindEntities"&gt;
        &lt;EntitySet Name="Products" EntityType="NorthwindModel.Product"/&gt;
        &lt;EntitySet Name="Categories" EntityType="NorthwindModel.Category"/&gt;
      &lt;/EntityContainer&gt;
    &lt;/Schema&gt;
  &lt;/edmx:DataServices&gt;
&lt;/edmx:Edmx&gt;</code></pre>
    </div>

    <script type="module">
        // 导入 ES 模块
        import odatajs, { utils, xml, oData } from './index.js';

        // 调试信息
        console.log('📄 开始加载模块...');
        console.log('odatajs:', odatajs);
        console.log('utils:', utils);
        console.log('xml:', xml);
        console.log('oData:', oData);

        // 全局变量
        window.odatajs = odatajs;
        window.utils = utils;
        window.xml = xml;
        window.oData = oData;

        // 验证模块加载
        console.log('window.xml:', window.xml);
        console.log('window.xml.xmlParse:', window.xml ? window.xml.xmlParse : 'undefined');

        // 示例元数据
        window.sampleMetadata = `<?xml version="1.0" encoding="utf-8"?>
<edmx:Edmx Version="4.0" xmlns:edmx="http://docs.oasis-open.org/odata/ns/edmx">
  <edmx:DataServices>
    <Schema Namespace="NorthwindModel" xmlns="http://docs.oasis-open.org/odata/ns/edm">
      <EntityType Name="Product">
        <Key>
          <PropertyRef Name="ProductID"/>
        </Key>
        <Property Name="ProductID" Type="Edm.Int32" Nullable="false"/>
        <Property Name="ProductName" Type="Edm.String" MaxLength="40"/>
        <Property Name="UnitPrice" Type="Edm.Decimal" Precision="19" Scale="4"/>
        <Property Name="UnitsInStock" Type="Edm.Int16"/>
        <Property Name="Discontinued" Type="Edm.Boolean" Nullable="false"/>
      </EntityType>
      <EntityType Name="Category">
        <Key>
          <PropertyRef Name="CategoryID"/>
        </Key>
        <Property Name="CategoryID" Type="Edm.Int32" Nullable="false"/>
        <Property Name="CategoryName" Type="Edm.String" MaxLength="15"/>
        <Property Name="Description" Type="Edm.String"/>
      </EntityType>
      <EntityContainer Name="NorthwindEntities">
        <EntitySet Name="Products" EntityType="NorthwindModel.Product"/>
        <EntitySet Name="Categories" EntityType="NorthwindModel.Category"/>
      </EntityContainer>
    </Schema>
  </edmx:DataServices>
</edmx:Edmx>`;

        console.log('📄 OData 元数据演示页面已加载');
        console.log('✅ ES 模块导入成功');

        // 添加模块状态检查函数
        window.checkModules = function() {
            console.log('=== 模块状态检查 ===');
            console.log('window.xml:', window.xml);
            console.log('window.xml.xmlParse:', window.xml ? window.xml.xmlParse : 'undefined');
            console.log('window.oData:', window.oData);
            console.log('window.utils:', window.utils);

            // 在页面上显示结果
            clearResults();
            addResult('<h3>🔍 模块状态检查</h3>');
            addResult(`window.xml: ${window.xml ? '✅ 已加载' : '❌ 未加载'}`, window.xml ? 'success' : 'error');
            if (window.xml) {
                addResult(`xmlParse 函数: ${window.xml.xmlParse ? '✅ 可用' : '❌ 不可用'}`, window.xml.xmlParse ? 'success' : 'error');
                const xmlFunctions = Object.keys(window.xml);
                addResult(`XML 模块函数: ${xmlFunctions.join(', ')}`, 'info');
            }
            addResult(`window.oData: ${window.oData ? '✅ 已加载' : '❌ 未加载'}`, window.oData ? 'success' : 'error');
            addResult(`window.utils: ${window.utils ? '✅ 已加载' : '❌ 未加载'}`, window.utils ? 'success' : 'error');
        };
    </script>

    <script>
        function clearResults() {
            document.getElementById('results').innerHTML = '';
        }

        function addResult(content, type = 'info') {
            const resultsDiv = document.getElementById('results');
            const resultDiv = document.createElement('div');
            resultDiv.className = type;
            resultDiv.innerHTML = content;
            resultsDiv.appendChild(resultDiv);
        }

        function testXMLParsing() {
            addResult('<h3>🔧 XML 解析测试</h3>');

            // 首先检查模块状态
            addResult(`🔍 检查模块状态: window.xml = ${window.xml ? '✅' : '❌'}`, 'info');
            if (window.xml) {
                addResult(`🔍 xmlParse 函数: ${window.xml.xmlParse ? '✅' : '❌'}`, 'info');
            }

            try {
                if (window.xml && window.xml.xmlParse) {
                    const doc = window.xml.xmlParse(window.sampleMetadata);
                    if (doc) {
                        addResult('✅ XML 解析成功', 'success');
                        addResult(`📊 文档类型: ${doc.constructor.name}`, 'info');

                        const rootElement = doc.documentElement || doc;
                        if (rootElement) {
                            addResult(`📋 根元素: <span class="highlight">${rootElement.nodeName || rootElement.tagName}</span>`, 'info');
                        }

                        // 检查命名空间
                        if (rootElement.namespaceURI) {
                            addResult(`🌐 命名空间: ${rootElement.namespaceURI}`, 'info');
                        }

                        addResult('🎯 XML 解析功能正常工作', 'success');
                    } else {
                        addResult('❌ XML 解析返回空结果', 'error');
                    }
                } else {
                    addResult('❌ XML 解析器不可用', 'error');
                    addResult('💡 提示: 请检查 ES 模块是否正确加载', 'info');
                }
            } catch (error) {
                addResult(`❌ XML 解析失败: ${error.message}`, 'error');
                console.error('XML 解析错误:', error);
            }
        }

        function testMetadataParsing() {
            addResult('<h3>📄 元数据解析测试</h3>');
            
            try {
                const doc = window.xml.xmlParse(window.sampleMetadata);
                if (doc) {
                    addResult('✅ XML 文档解析成功', 'success');
                    
                    // 测试 OData 元数据解析
                    if (window.oData && window.oData.parseMetadata) {
                        try {
                            const metadata = window.oData.parseMetadata(doc);
                            if (metadata) {
                                addResult('✅ OData 元数据解析成功', 'success');
                                addResult(`📊 解析结果类型: ${typeof metadata}`, 'info');
                                
                                if (typeof metadata === 'object') {
                                    const keys = Object.keys(metadata);
                                    addResult(`🔑 包含 ${keys.length} 个属性: ${keys.slice(0, 5).join(', ')}${keys.length > 5 ? '...' : ''}`, 'info');
                                }
                            } else {
                                addResult('⚠️ parseMetadata 返回空结果', 'error');
                            }
                        } catch (parseError) {
                            addResult(`⚠️ 元数据解析错误: ${parseError.message}`, 'error');
                        }
                    } else {
                        addResult('⚠️ parseMetadata 函数不可用', 'error');
                    }
                    
                    // 测试处理器
                    if (window.oData.metadataHandler) {
                        addResult('✅ metadataHandler 可用', 'success');
                    }
                    
                } else {
                    addResult('❌ 无法解析 XML 文档', 'error');
                }
            } catch (error) {
                addResult(`❌ 元数据解析测试失败: ${error.message}`, 'error');
            }
        }

        function testEntityTypes() {
            addResult('<h3>🏗️ 实体类型分析</h3>');

            // 检查模块状态
            if (!window.xml || !window.xml.xmlParse) {
                addResult('❌ XML 解析器不可用，无法进行实体类型分析', 'error');
                addResult('💡 请先确保 ES 模块正确加载', 'info');
                return;
            }

            try {
                const doc = window.xml.xmlParse(window.sampleMetadata);
                if (doc) {
                    // 查找 EntityType 元素
                    const entityTypes = doc.getElementsByTagName('EntityType');
                    addResult(`📋 找到 <span class="highlight">${entityTypes.length}</span> 个实体类型:`, 'info');
                    
                    for (let i = 0; i < entityTypes.length; i++) {
                        const entityType = entityTypes[i];
                        const name = entityType.getAttribute('Name');
                        
                        addResult(`<strong>🏷️ ${name}</strong>`, 'info');
                        
                        // 查找属性
                        const properties = entityType.getElementsByTagName('Property');
                        addResult(`&nbsp;&nbsp;📝 属性 (${properties.length}):`, 'info');
                        
                        for (let j = 0; j < properties.length; j++) {
                            const prop = properties[j];
                            const propName = prop.getAttribute('Name');
                            const propType = prop.getAttribute('Type');
                            const nullable = prop.getAttribute('Nullable');
                            const maxLength = prop.getAttribute('MaxLength');
                            
                            let propInfo = `&nbsp;&nbsp;&nbsp;&nbsp;• <span class="highlight">${propName}</span>: ${propType}`;
                            if (nullable === 'false') propInfo += ' (必填)';
                            if (maxLength) propInfo += ` [最大长度: ${maxLength}]`;
                            
                            addResult(propInfo, 'info');
                        }
                        
                        // 查找键
                        const keys = entityType.getElementsByTagName('PropertyRef');
                        if (keys.length > 0) {
                            const keyNames = Array.from(keys).map(k => k.getAttribute('Name')).join(', ');
                            addResult(`&nbsp;&nbsp;🔑 主键: <span class="highlight">${keyNames}</span>`, 'info');
                        }
                    }
                    
                    // 查找 EntitySet
                    const entitySets = doc.getElementsByTagName('EntitySet');
                    addResult(`<br>📦 找到 <span class="highlight">${entitySets.length}</span> 个实体集:`, 'info');
                    
                    for (let i = 0; i < entitySets.length; i++) {
                        const entitySet = entitySets[i];
                        const name = entitySet.getAttribute('Name');
                        const entityType = entitySet.getAttribute('EntityType');
                        addResult(`&nbsp;&nbsp;• <span class="highlight">${name}</span> → ${entityType}`, 'info');
                    }
                    
                    addResult('🎯 实体类型分析完成', 'success');
                    
                } else {
                    addResult('❌ 无法解析 XML 文档', 'error');
                }
            } catch (error) {
                addResult(`❌ 实体类型分析失败: ${error.message}`, 'error');
            }
        }
    </script>
</body>
</html>
