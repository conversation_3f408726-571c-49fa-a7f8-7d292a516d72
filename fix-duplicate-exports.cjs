// 修复重复导出的问题
const fs = require('fs');

const jsFiles = [
    './lib/utils.js',
    './lib/xml.js',
    './lib/deferred.js',
    './lib/cache.js',
    './lib/cache/source.js',
    './lib/odata.js',
    './lib/odata/batch.js',
    './lib/odata/handler.js',
    './lib/odata/json.js',
    './lib/odata/metadata.js',
    './lib/odata/net.js',
    './lib/odata/net-browser.js',
    './lib/odata/odatautils.js',
    './lib/store.js',
    './lib/store/dom.js',
    './lib/store/indexeddb.js',
    './lib/store/memory.js',
    './index.js',
    './index-browser.js'
];

function fixDuplicateExports(filePath) {
    console.log(`检查文件: ${filePath}`);
    
    if (!fs.existsSync(filePath)) {
        console.log(`  ⚠️  文件不存在: ${filePath}`);
        return;
    }
    
    let content = fs.readFileSync(filePath, 'utf8');
    let fixCount = 0;
    
    // 查找所有函数定义
    const functionMatches = content.match(/^(export\s+)?function\s+(\w+)/gm);
    const exportedFunctions = new Set();
    
    if (functionMatches) {
        functionMatches.forEach(match => {
            const funcName = match.match(/function\s+(\w+)/)[1];
            if (match.startsWith('export')) {
                exportedFunctions.add(funcName);
            }
        });
    }
    
    // 移除重复的 export const xxx = xxx; 语句
    const lines = content.split('\n');
    const filteredLines = [];
    
    for (let i = 0; i < lines.length; i++) {
        const line = lines[i];
        const exportMatch = line.match(/^export const (\w+) = \1;$/);
        
        if (exportMatch) {
            const exportName = exportMatch[1];
            if (exportedFunctions.has(exportName)) {
                // 这是一个重复的导出，跳过这一行
                console.log(`  移除重复导出: ${exportName}`);
                fixCount++;
                continue;
            }
        }
        
        filteredLines.push(line);
    }
    
    if (fixCount > 0) {
        const newContent = filteredLines.join('\n');
        fs.writeFileSync(filePath, newContent);
        console.log(`  ✅ 修复了 ${fixCount} 个重复导出`);
    } else {
        console.log(`  ℹ️  没有发现重复导出`);
    }
}

// 修复所有文件
console.log('开始修复重复导出...');
jsFiles.forEach(fixDuplicateExports);
console.log('\n🎉 重复导出修复完成！');
