// 转换 deferred.js 为 ES 模块
const fs = require('fs');

// 读取文件
let content = fs.readFileSync('./lib/deferred.js', 'utf8');

// 获取所有需要导出的函数名
const exportMatches = content.match(/^exports\.(\w+)\s*=\s*(\w+);$/gm);
const exportedFunctions = [];

if (exportMatches) {
    exportMatches.forEach(match => {
        const funcName = match.match(/^exports\.(\w+)/)[1];
        exportedFunctions.push(funcName);
    });
}

console.log('找到的导出函数:', exportedFunctions.length);

// 转换函数定义为 export
exportedFunctions.forEach(funcName => {
    // 匹配 function funcName( 的模式
    const functionPattern = new RegExp(`^function\\s+${funcName}\\s*\\(`, 'gm');
    content = content.replace(functionPattern, `export function ${funcName}(`);
    
    // 匹配 var funcName = function( 的模式  
    const varPattern = new RegExp(`^var\\s+${funcName}\\s*=\\s*function\\s*\\(`, 'gm');
    content = content.replace(varPattern, `export const ${funcName} = function(`);
    
    // 匹配构造函数的模式 (如 DjsDeferred)
    const constructorPattern = new RegExp(`^var\\s+${funcName}\\s*=\\s*function\\s+${funcName}\\s*\\(`, 'gm');
    content = content.replace(constructorPattern, `export function ${funcName}(`);
});

// 移除末尾的 exports 语句
const exportsPattern = /^exports\.\w+\s*=\s*\w+;$/gm;
content = content.replace(exportsPattern, '');

// 清理多余的空行
content = content.replace(/\n\s*\n\s*\n/g, '\n\n');

// 写回文件
fs.writeFileSync('./lib/deferred.js', content);

console.log('✅ deferred.js 转换完成！');
console.log('已转换的函数:', exportedFunctions.length);
console.log('已移除 exports 语句');
