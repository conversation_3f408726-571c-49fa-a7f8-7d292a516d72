﻿<!--
/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing,
 * software distributed under the License is distributed on an
 * "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 * KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations
 * under the License.
*/
-->
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
    <title>metadata awareness tests</title>
    <meta http-equiv="cache-control" content="no-cache" />
    <meta http-equiv="pragma" content="no-cache" />
    <meta http-equiv="expires" content="-1" />
    <link rel="stylesheet" href="http://code.jquery.com/qunit/qunit-1.10.0.css" type="text/css" />
    <script type="text/javascript" src="http://cdnjs.cloudflare.com/ajax/libs/json2/20110223/json2.js"></script>
    <script type="text/javascript" src="http://code.jquery.com/jquery-1.4.4.min.js"></script>
    <script type="text/javascript" src="http://code.jquery.com/qunit/qunit-1.10.0.js"></script>
    <script type="text/javascript" src="common/odataVerifyReader.js"></script>
    <script type="text/javascript" src="common/TestSynchronizerClient.js"></script>
    <script type="text/javascript">
        window.TestSynchronizer.init(QUnit);
    </script>
    <script type="text/javascript" src="../_build/lib/odatajs-latest.js"></script>
    <script type="text/javascript" src="common/common.js"></script>   
    <script type="text/javascript" src="common/djstest.js"></script>
    <script type="text/javascript" src="common/djstest-browser-ext.js"></script>
    <script type="text/javascript" src="odata-metadata-awareness-functional-tests.js"></script>
</head>
<body>
    <h1 id="qunit-header">metadata awareness tests</h1>
    <h2 id="qunit-banner"></h2>
    <h2 id="qunit-userAgent"></h2>
    <ol id="qunit-tests"></ol>
</body>
</html>