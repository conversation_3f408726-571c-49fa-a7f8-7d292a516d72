// 转换所有剩余模块为 ES 模块
const fs = require('fs');
const path = require('path');

// 需要转换的文件列表
const filesToConvert = [
    './lib/cache/source.js',
    './lib/cache.js',
    './lib/odata.js',
    './lib/odata/batch.js',
    './lib/odata/handler.js', 
    './lib/odata/json.js',
    './lib/odata/metadata.js',
    './lib/odata/net.js',
    './lib/odata/net-browser.js',
    './lib/odata/odatautils.js',
    './lib/store.js',
    './lib/store/dom.js',
    './lib/store/indexeddb.js',
    './lib/store/memory.js'
];

function convertFile(filePath) {
    console.log(`\n转换文件: ${filePath}`);
    
    if (!fs.existsSync(filePath)) {
        console.log(`  ⚠️  文件不存在: ${filePath}`);
        return;
    }
    
    let content = fs.readFileSync(filePath, 'utf8');
    
    // 1. 转换 require 语句为 import
    // 匹配各种 require 模式
    content = content.replace(/var\s+(\w+)\s*=\s*require\(['"]([^'"]+)['"]\);?/g, (match, varName, modulePath) => {
        // 处理相对路径
        if (modulePath.startsWith('./') || modulePath.startsWith('../')) {
            if (!modulePath.endsWith('.js')) {
                modulePath += '.js';
            }
        }
        return `import * as ${varName} from '${modulePath}';`;
    });
    
    // 2. 转换解构赋值的 require
    content = content.replace(/var\s+(\w+)\s*=\s*(\w+)\.(\w+);?/g, (match, varName, moduleName, propName) => {
        // 这些通常是从已导入模块中提取属性，保持原样或转换为解构导入
        return `const ${varName} = ${moduleName}.${propName};`;
    });
    
    // 3. 获取所有需要导出的函数名
    const exportMatches = content.match(/^exports\.(\w+)\s*=\s*(\w+);$/gm);
    const exportedFunctions = [];
    
    if (exportMatches) {
        exportMatches.forEach(match => {
            const funcName = match.match(/^exports\.(\w+)/)[1];
            exportedFunctions.push(funcName);
        });
    }
    
    console.log(`  找到导出函数: ${exportedFunctions.length}`);
    
    // 4. 转换函数定义为 export
    exportedFunctions.forEach(funcName => {
        // 匹配 function funcName( 的模式
        const functionPattern = new RegExp(`^function\\s+${funcName}\\s*\\(`, 'gm');
        content = content.replace(functionPattern, `export function ${funcName}(`);
        
        // 匹配 var funcName = function( 的模式  
        const varPattern = new RegExp(`^var\\s+${funcName}\\s*=\\s*function\\s*\\(`, 'gm');
        content = content.replace(varPattern, `export const ${funcName} = function(`);
    });
    
    // 5. 移除末尾的 exports 语句
    const exportsPattern = /^exports\.\w+\s*=\s*\w+;$/gm;
    content = content.replace(exportsPattern, '');
    
    // 6. 清理多余的空行
    content = content.replace(/\n\s*\n\s*\n/g, '\n\n');
    
    // 写回文件
    fs.writeFileSync(filePath, content);
    
    console.log(`  ✅ 转换完成: ${exportedFunctions.length} 个函数`);
}

// 转换所有文件
console.log('开始批量转换模块...');
filesToConvert.forEach(convertFile);
console.log('\n🎉 所有模块转换完成！');
